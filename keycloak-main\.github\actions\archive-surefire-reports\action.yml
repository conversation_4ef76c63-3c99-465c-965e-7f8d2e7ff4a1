name: Archive Surefire reports
description: It will upload and archive surefire reports per particular test run.
inputs:
  job-id:
    description: 'Id of the particular job run.'
    required: true
  release-branches:
    description: 'List of all related release branches (in JSON format)'
    required: false
    default: '["refs/heads/release/22.0","refs/heads/release/24.0","refs/heads/release/26.0"]'
  keep-days:
    description: 'For how many days to store the particular artifact.'
    required: false
    default: 1

runs:
  using: composite
  steps:
    - id: find-surefire-reports-linux
      name: Find Surefire reports directories
      if: runner.os == 'Linux'
      shell: bash
      run: |
        echo "dirs<<EOF" >> $GITHUB_OUTPUT
        echo "$(find ~+ -type d -not -empty -name surefire-reports*)" >> $GITHUB_OUTPUT 
        echo "EOF" >> $GITHUB_OUTPUT

    - id: find-surefire-reports-win
      name: Find Surefire reports directories
      if: runner.os == 'Windows'
      shell: bash
      run: |
        echo "dirs<<EOF" >> $GITHUB_OUTPUT
        echo "$(find ~+ -type d -not -empty -name surefire-reports* | sed 's@/d@D:@')" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - id: upload-surefire-linux
      name: Upload Surefire reports
      if: (!cancelled() && contains(fromJSON(inputs.release-branches), github.ref) && contains(fromJSON('["push", "workflow_dispatch"]'), github.event_name))
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
      with:
        name: surefire-${{ inputs.job-id }}
        path: |
          ${{ steps.find-surefire-reports-linux.outputs.dirs }}
          ${{ steps.find-surefire-reports-win.outputs.dirs }}
        retention-days: ${{ inputs.keep-days }}
