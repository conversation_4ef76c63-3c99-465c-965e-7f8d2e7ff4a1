# Operational Excellence & Compliance Guide
## NGC Aether OpenWebUI + Keycloak Enterprise Deployment

### 🎯 Operational Excellence Framework

This guide establishes operational procedures, compliance requirements, and governance frameworks for enterprise-grade deployment in the NGC Aether environment.

## 🔄 CONFIGURATION MANAGEMENT

### 1. **Infrastructure as Code Implementation**

**Create Terraform configuration for NGC environment**:

```hcl
# terraform/main.tf
terraform {
  required_providers {
    docker = {
      source  = "kreuzwerker/docker"
      version = "~> 3.0"
    }
  }
  
  backend "s3" {
    bucket = "ngc-aether-terraform-state"
    key    = "openwebui/terraform.tfstate"
    region = "us-gov-west-1"
  }
}

provider "docker" {
  host = "tcp://************:2376"
}

# Network configuration
resource "docker_network" "aether_frontend" {
  name = "aether-frontend"
  driver = "bridge"
  
  ipam_config {
    subnet = "**********/24"
    gateway = "**********"
  }
}

resource "docker_network" "aether_backend" {
  name = "aether-backend"
  driver = "bridge"
  internal = true
  
  ipam_config {
    subnet = "**********/24"
    gateway = "**********"
  }
}

# Volume management
resource "docker_volume" "postgres_data" {
  name = "aether-postgres-data"
  
  labels = {
    environment = "production"
    backup_policy = "daily"
    retention = "30d"
  }
}

resource "docker_volume" "keycloak_data" {
  name = "aether-keycloak-data"
  
  labels = {
    environment = "production"
    backup_policy = "daily"
    retention = "30d"
  }
}
```

### 2. **GitOps Deployment Pipeline**

**Create .github/workflows/deploy-production.yml**:

```yaml
name: Deploy to NGC Aether Production

on:
  push:
    branches: [main]
    paths: 
      - 'docker-compose.yml'
      - 'realm-config.json'
      - '.env.production'

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'config'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  deploy:
    needs: security-scan
    runs-on: self-hosted
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Validate configuration
        run: |
          ./scripts/validate-config.sh
          ./verify-dns.ps1
      
      - name: Create backup
        run: ./scripts/create-pre-deployment-backup.sh
      
      - name: Deploy to production
        run: |
          docker-compose down --remove-orphans
          docker-compose pull
          docker-compose up -d
      
      - name: Health check
        run: ./scripts/post-deployment-health-check.sh
      
      - name: Notify teams
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#ngc-aether-ops'
```

## 🔐 SECRETS MANAGEMENT & ROTATION

### 3. **HashiCorp Vault Integration**

**Create vault-integration.yml**:

```yaml
version: "3.9"

services:
  vault:
    image: vault:latest
    container_name: aether-vault
    cap_add:
      - IPC_LOCK
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: ${VAULT_ROOT_TOKEN}
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
    ports:
      - "8200:8200"
    volumes:
      - vault_data:/vault/data
      - ./vault/config:/vault/config:ro
    command: vault server -config=/vault/config/vault.hcl
    networks:
      - vault-network

  vault-init:
    image: vault:latest
    container_name: aether-vault-init
    environment:
      VAULT_ADDR: http://vault:8200
      VAULT_TOKEN: ${VAULT_ROOT_TOKEN}
    volumes:
      - ./vault/scripts:/scripts:ro
    command: /scripts/init-vault.sh
    depends_on:
      - vault
    networks:
      - vault-network

networks:
  vault-network:
    driver: bridge

volumes:
  vault_data:
```

**Create vault/scripts/init-vault.sh**:

```bash
#!/bin/bash
set -e

# Wait for Vault to be ready
until vault status; do
  echo "Waiting for Vault to be ready..."
  sleep 5
done

# Enable KV secrets engine
vault secrets enable -path=aether kv-v2

# Create policies
vault policy write aether-openwebui - <<EOF
path "aether/data/openwebui/*" {
  capabilities = ["read"]
}
EOF

vault policy write aether-keycloak - <<EOF
path "aether/data/keycloak/*" {
  capabilities = ["read"]
}
EOF

# Store secrets
vault kv put aether/openwebui/database \
  username="${OPENWEBUI_DB_USER}" \
  password="${OPENWEBUI_DB_PASSWORD}"

vault kv put aether/keycloak/database \
  username="${KEYCLOAK_DB_USER}" \
  password="${KEYCLOAK_DB_PASSWORD}"

vault kv put aether/keycloak/admin \
  username="${KEYCLOAK_ADMIN_USER}" \
  password="${KEYCLOAK_ADMIN_PASSWORD}"

echo "Vault initialization completed"
```

### 4. **Automated Secrets Rotation**

**Create secrets-rotation.ps1**:

```powershell
# Automated Secrets Rotation Script
param(
    [string]$VaultAddr = "https://vault.aether-prod.gc1.myngc.com:8200",
    [string]$RotationType = "all" # all, database, keycloak, oauth
)

function Rotate-DatabasePasswords {
    Write-Host "Rotating database passwords..." -ForegroundColor Yellow
    
    # Generate new passwords
    $newPostgresPassword = [System.Web.Security.Membership]::GeneratePassword(32, 8)
    $newKeycloakDbPassword = [System.Web.Security.Membership]::GeneratePassword(32, 8)
    
    # Update Vault
    vault kv put aether/postgres/credentials password=$newPostgresPassword
    vault kv put aether/keycloak/database password=$newKeycloakDbPassword
    
    # Update database
    docker exec aether-postgres psql -U postgres -c "ALTER USER postgres PASSWORD '$newPostgresPassword';"
    docker exec aether-postgres psql -U postgres -c "ALTER USER keycloak PASSWORD '$newKeycloakDbPassword';"
    
    # Restart services with new passwords
    docker-compose restart keycloak
    docker-compose restart open-webui
    
    Write-Host "Database passwords rotated successfully" -ForegroundColor Green
}

function Rotate-KeycloakAdmin {
    Write-Host "Rotating Keycloak admin password..." -ForegroundColor Yellow
    
    $newAdminPassword = [System.Web.Security.Membership]::GeneratePassword(32, 8)
    
    # Update via Keycloak Admin API
    $token = Get-KeycloakAdminToken
    $headers = @{ "Authorization" = "Bearer $token"; "Content-Type" = "application/json" }
    $body = @{ "type" = "password"; "value" = $newAdminPassword; "temporary" = $false } | ConvertTo-Json
    
    Invoke-RestMethod -Uri "$keycloakUrl/admin/realms/master/users/$adminUserId/reset-password" -Method PUT -Headers $headers -Body $body
    
    # Update Vault
    vault kv put aether/keycloak/admin password=$newAdminPassword
    
    Write-Host "Keycloak admin password rotated successfully" -ForegroundColor Green
}

# Execute rotation based on type
switch ($RotationType) {
    "database" { Rotate-DatabasePasswords }
    "keycloak" { Rotate-KeycloakAdmin }
    "all" { 
        Rotate-DatabasePasswords
        Rotate-KeycloakAdmin
    }
}
```

## 📊 COMPLIANCE & GOVERNANCE

### 5. **NIST Cybersecurity Framework Compliance**

**Create compliance-matrix.md**:

```markdown
# NIST CSF Compliance Matrix

## IDENTIFY (ID)
- [x] ID.AM-1: Physical devices and systems are inventoried
- [x] ID.AM-2: Software platforms and applications are inventoried
- [x] ID.AM-3: Organizational communication and data flows are mapped
- [x] ID.GV-1: Organizational cybersecurity policy is established
- [x] ID.RA-1: Asset vulnerabilities are identified and documented

## PROTECT (PR)
- [x] PR.AC-1: Identities and credentials are issued, managed, verified
- [x] PR.AC-4: Access permissions and authorizations are managed
- [x] PR.DS-1: Data-at-rest is protected
- [x] PR.DS-2: Data-in-transit is protected
- [x] PR.PT-1: Audit/log records are determined, documented, implemented

## DETECT (DE)
- [x] DE.AE-1: A baseline of network operations is established
- [x] DE.CM-1: The network is monitored to detect potential cybersecurity events
- [x] DE.CM-7: Monitoring for unauthorized personnel, connections, devices

## RESPOND (RS)
- [x] RS.RP-1: Response plan is executed during or after an incident
- [x] RS.CO-2: Incidents are reported consistent with established criteria
- [x] RS.AN-1: Notifications from detection systems are investigated

## RECOVER (RC)
- [x] RC.RP-1: Recovery plan is executed during or after a cybersecurity incident
- [x] RC.IM-1: Recovery plans incorporate lessons learned
- [x] RC.CO-3: Recovery activities are communicated to stakeholders
```

### 6. **Audit Logging & Compliance Reporting**

**Create audit-logging.yml**:

```yaml
version: "3.9"

services:
  audit-collector:
    image: fluent/fluent-bit:latest
    container_name: aether-audit-collector
    volumes:
      - ./logging/fluent-bit.conf:/fluent-bit/etc/fluent-bit.conf:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - logging
    environment:
      - SPLUNK_HEC_TOKEN=${SPLUNK_HEC_TOKEN}
      - SPLUNK_HEC_HOST=${SPLUNK_HEC_HOST}

  compliance-reporter:
    image: alpine:latest
    container_name: aether-compliance-reporter
    volumes:
      - ./scripts/compliance-report.sh:/compliance-report.sh:ro
      - ./reports:/reports
    command: /bin/sh -c "chmod +x /compliance-report.sh && /compliance-report.sh"
    environment:
      - REPORT_SCHEDULE=${COMPLIANCE_REPORT_SCHEDULE:-daily}
```

**Create scripts/compliance-report.sh**:

```bash
#!/bin/bash
set -e

REPORT_DATE=$(date +%Y%m%d)
REPORT_DIR="/reports"
REPORT_FILE="$REPORT_DIR/compliance_report_$REPORT_DATE.json"

mkdir -p $REPORT_DIR

# Generate compliance report
cat > $REPORT_FILE << EOF
{
  "report_date": "$(date -Iseconds)",
  "environment": "NGC_Aether_Production",
  "compliance_framework": "NIST_CSF",
  "security_controls": {
    "authentication": {
      "mfa_enabled": $(check_mfa_status),
      "password_policy": $(check_password_policy),
      "session_management": $(check_session_management)
    },
    "encryption": {
      "data_at_rest": $(check_encryption_at_rest),
      "data_in_transit": $(check_encryption_in_transit),
      "key_management": $(check_key_management)
    },
    "monitoring": {
      "log_collection": $(check_log_collection),
      "intrusion_detection": $(check_ids),
      "vulnerability_scanning": $(check_vuln_scanning)
    }
  },
  "incidents": $(get_security_incidents),
  "vulnerabilities": $(get_vulnerability_report),
  "recommendations": $(get_recommendations)
}
EOF

echo "Compliance report generated: $REPORT_FILE"
```

## 🔄 CHANGE MANAGEMENT

### 7. **Change Control Process**

**Create change-management.md**:

```markdown
# Change Management Process

## Change Categories

### Category 1: Emergency Changes
- Security patches
- Critical bug fixes
- Service outages

**Approval**: CISO + Operations Manager
**Timeline**: Immediate
**Documentation**: Post-implementation

### Category 2: Standard Changes
- Feature updates
- Configuration changes
- Routine maintenance

**Approval**: Change Advisory Board
**Timeline**: 5 business days
**Documentation**: Pre-implementation

### Category 3: Major Changes
- Architecture modifications
- New service deployments
- Infrastructure changes

**Approval**: Architecture Review Board + CISO
**Timeline**: 15 business days
**Documentation**: Comprehensive impact analysis

## Change Request Template

```yaml
change_request:
  id: CR-YYYY-NNNN
  title: "Brief description"
  category: "emergency|standard|major"
  requestor: "Name <email>"
  implementation_date: "YYYY-MM-DD"
  
  description: |
    Detailed description of the change
  
  business_justification: |
    Why this change is needed
  
  technical_details: |
    How the change will be implemented
  
  risk_assessment:
    probability: "low|medium|high"
    impact: "low|medium|high"
    mitigation: "Risk mitigation strategies"
  
  testing_plan: |
    How the change will be tested
  
  rollback_plan: |
    How to rollback if issues occur
  
  approvals:
    - name: "Approver Name"
      role: "Role"
      date: "YYYY-MM-DD"
```

## 📋 OPERATIONAL RUNBOOKS

### 8. **Incident Response Procedures**

**Create incident-response.md**:

```markdown
# Incident Response Runbook

## Severity Levels

### Severity 1 (Critical)
- Complete service outage
- Security breach
- Data loss

**Response Time**: 15 minutes
**Escalation**: Immediate to on-call engineer + management

### Severity 2 (High)
- Partial service degradation
- Performance issues
- Authentication problems

**Response Time**: 1 hour
**Escalation**: On-call engineer

### Severity 3 (Medium)
- Minor functionality issues
- Non-critical errors

**Response Time**: 4 hours
**Escalation**: Standard support queue

## Response Procedures

### 1. Initial Response (0-15 minutes)
- Acknowledge incident
- Assess severity
- Notify stakeholders
- Begin investigation

### 2. Investigation (15-60 minutes)
- Gather logs and metrics
- Identify root cause
- Implement temporary fixes
- Document findings

### 3. Resolution (1-4 hours)
- Implement permanent fix
- Verify resolution
- Update stakeholders
- Schedule post-mortem

### 4. Post-Incident (24-48 hours)
- Conduct post-mortem
- Document lessons learned
- Update procedures
- Implement preventive measures
```

This operational excellence guide establishes the framework for enterprise-grade operations, compliance, and governance required for the NGC Aether environment.
