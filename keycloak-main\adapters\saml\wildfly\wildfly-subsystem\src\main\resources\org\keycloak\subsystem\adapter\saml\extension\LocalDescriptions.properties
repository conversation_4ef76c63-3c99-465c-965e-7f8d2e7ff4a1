#
# Copyright 2016 Red Hat, Inc. and/or its affiliates
# and other contributors as indicated by the <AUTHOR>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

keycloak-saml.subsystem=Keycloak adapter subsystem
keycloak-saml.subsystem.add=Operation Adds Keycloak adapter subsystem
keycloak-saml.subsystem.remove=Operation removes Keycloak adapter subsystem
keycloak-saml.subsystem.secure-deployment=A deployment secured by Keycloak.

keycloak-saml.secure-deployment=A deployment secured by Keycloak
keycloak-saml.secure-deployment.add=Add a deployment to be secured by Keycloak
keycloak-saml.secure-deployment.remove=Remove a deployment to be secured by Keycloak
keycloak-saml.secure-deployment.SP=A security provider configuration for secure deployment

keycloak-saml.SP=A security provider configuration for secure deployment
keycloak-saml.SP.add=Add a security provider configuration to deployment secured by Keycloak SAML
keycloak-saml.SP.remove=Remove a security provider definition from deployment secured by Keycloak SAML
keycloak-saml.SP.sslPolicy=SSL Policy to use
keycloak-saml.SP.nameIDPolicyFormat=Name ID policy format URN
keycloak-saml.SP.logoutPage=URI to a logout page
keycloak-saml.SP.forceAuthentication=Redirected unauthenticated request to a login page
keycloak-saml.SP.keepDOMAssertion=Attribute to inject the DOM representation of the assertion into the SamlPrincipal (respecting the original syntax)
keycloak-saml.SP.isPassive=If user isn't logged in just return with an error.  Used to check if a user is already logged in or not
keycloak-saml.SP.turnOffChangeSessionIdOnLogin=The session id is changed by default on a successful login.  Change this to true if you want to turn this off
keycloak-saml.SP.autodetectBearerOnly=Set to true if the application serves both a web application and web services (e.g. SOAP or REST). It allows redirection of unauthenticated users of the web application to the Keycloak login page, but send an HTTP 401 status code to unauthenticated SOAP or REST clients instead
keycloak-saml.SP.RoleIdentifiers=Role identifiers
keycloak-saml.SP.PrincipalNameMapping-policy=Principal name mapping policy
keycloak-saml.SP.PrincipalNameMapping-attribute-name=Principal name mapping attribute name
keycloak-saml.SP.Key=A key definition
keycloak-saml.SP.IDP=Identity provider definition
keycloak-saml.SP.roleMappingsProviderId=The string that identifies the role mappings provider to be used within the SP
keycloak-saml.SP.roleMappingsProviderConfig=The configuration properties of the role mappings provider

keycloak-saml.Key=A key configuration for service provider or identity provider
keycloak-saml.Key.add=Add a key definition
keycloak-saml.Key.remove=Remove a key definition
keycloak-saml.Key.signing=Key can be used for signing
keycloak-saml.Key.encryption=Key can be used for encryption
keycloak-saml.Key.PrivateKeyPem=Private key string in pem format
keycloak-saml.Key.PublicKeyPem=Public key string in pem format
keycloak-saml.Key.CertificatePem=Certificate key string in pem format
keycloak-saml.Key.KeyStore=Key store definition
keycloak-saml.Key.KeyStore.file=Key store filesystem path
keycloak-saml.Key.KeyStore.resource=Key store resource URI
keycloak-saml.Key.KeyStore.password=Key store password
keycloak-saml.Key.KeyStore.type=Key store format
keycloak-saml.Key.KeyStore.alias=Key alias
keycloak-saml.Key.KeyStore.PrivateKey-alias=Private key alias
keycloak-saml.Key.KeyStore.PrivateKey-password=Private key password
keycloak-saml.Key.KeyStore.Certificate-alias=Certificate alias

keycloak-saml.IDP=An identity provider configuration
keycloak-saml.IDP.add=Add an identity provider
keycloak-saml.IDP.remove=Remove an identity provider
keycloak-saml.IDP.signaturesRequired=Require signatures for SingleSignOnService and SingleLogoutService
keycloak-saml.IDP.signatureAlgorithm=Signature algorithm
keycloak-saml.IDP.signatureCanonicalizationMethod=Signature canonicalization method
keycloak-saml.IDP.metadataUrl=The URL used to retrieve the IDP metadata from
keycloak-saml.IDP.SingleSignOnService=Single sign-on configuration
keycloak-saml.IDP.SingleSignOnService.signRequest=Sign SSO requests
keycloak-saml.IDP.SingleSignOnService.validateResponseSignature=Validate an SSO response signature
keycloak-saml.IDP.SingleSignOnService.validateAssertionSignature=Validate an SSO assertion signature
keycloak-saml.IDP.SingleSignOnService.requestBinding=HTTP method to use for requests
keycloak-saml.IDP.SingleSignOnService.responseBinding=HTTP method to use for responses
keycloak-saml.IDP.SingleSignOnService.bindingUrl=SSO endpoint URL
keycloak-saml.IDP.SingleSignOnService.assertionConsumerServiceUrl=Endpoint of Assertion Consumer Service at SP
keycloak-saml.IDP.SingleLogoutService=Single logout configuration
keycloak-saml.IDP.SingleLogoutService.validateRequestSignature=Validate a SingleLogoutService request signature
keycloak-saml.IDP.SingleLogoutService.validateResponseSignature=Validate a SingleLogoutService response signature
keycloak-saml.IDP.SingleLogoutService.signRequest=Sign SingleLogoutService requests
keycloak-saml.IDP.SingleLogoutService.signResponse=Sign SingleLogoutService responses
keycloak-saml.IDP.SingleLogoutService.requestBinding=HTTP method to use for request
keycloak-saml.IDP.SingleLogoutService.responseBinding=HTTP method to use for response
keycloak-saml.IDP.SingleLogoutService.postBindingUrl=Endpoint URL for posting
keycloak-saml.IDP.SingleLogoutService.redirectBindingUrl=Endpoint URL for redirects
keycloak-saml.IDP.Key=Key definition for identity provider
keycloak-saml.IDP.AllowedClockSkew=Allowed clock skew between the IDP and the SP
keycloak-saml.IDP.AllowedClockSkew.value=Allowed clock skew value between the IDP and the SP
keycloak-saml.IDP.AllowedClockSkew.unit=Time unit for the value of the clock skew. Values: MINUTES, SECONDS, MILLISECONDS, MICROSECONDS, NANOSECONDS
keycloak-saml.IDP.HttpClient=Configuration of HTTP client used for automatic retrieval of certificates for signature validation
keycloak-saml.IDP.HttpClient.allowAnyHostname=Define if hostname validation should be disabled (true) or not (false)
keycloak-saml.IDP.HttpClient.clientKeystore=Path to the keystore that contains client certificates for two-way SSL
keycloak-saml.IDP.HttpClient.clientKeystorePassword=The keystore password
keycloak-saml.IDP.HttpClient.connectionPoolSize=The number of pooled connections
keycloak-saml.IDP.HttpClient.disableTrustManager=Define if SSL certificate validation should be disabled (true) or not (false)
keycloak-saml.IDP.HttpClient.proxyUrl=URL to the HTTP proxy, if applicable
keycloak-saml.IDP.HttpClient.truststore=Path to the truststore used to validate the IDP certificates
keycloak-saml.IDP.HttpClient.truststorePassword=The truststore password
keycloak-saml.IDP.HttpClient.socketTimeout=Timeout for socket waiting for data in milliseconds
keycloak-saml.IDP.HttpClient.connectionTimeout=Timeout for establishing the connection with the remote host in milliseconds
keycloak-saml.IDP.HttpClient.connectionTtl=The connection time to live in milliseconds