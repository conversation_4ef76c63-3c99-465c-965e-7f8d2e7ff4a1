name: <PERSON><PERSON> Cache
description: Caches Maven artifacts

inputs:
  create-cache-if-it-doesnt-exist:
    description: > 
      Only those callers which fill the cache with the right contents should set this to true to avoid creating a cache
      which contains too few or too many entries.
    required: false
    default: false

runs:
  using: composite
  steps:
    - id: weekly-cache-key
      name: Key for weekly rotation of cache
      shell: bash
      run: echo "key=mvn-`date -u "+%Y-%U"`" >> $GITHUB_OUTPUT

    - id: cache-maven-repository
      name: Maven cache
      uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
      if: inputs.create-cache-if-it-doesnt-exist == 'true'
      with:
        # Two asterisks are needed to make the follow-up exclusion work
        # see https://github.com/actions/toolkit/issues/713 for the upstream issue
        path: |
          ~/.m2/repository/*/*
          !~/.m2/repository/org/keycloak
        key: ${{ steps.weekly-cache-key.outputs.key }}
        # Enable cross-os archive use the cache on both Linux and Windows
        enableCrossOsArchive: true

    - id: restore-maven-repository
      name: <PERSON>ven cache
      uses: actions/cache/restore@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
      if: inputs.create-cache-if-it-doesnt-exist == 'false'
      with:
        # This needs to repeat the same path pattern as above to find the matching cache
        path: |
          ~/.m2/repository/*/*
          !~/.m2/repository/org/keycloak
        key: ${{ steps.weekly-cache-key.outputs.key }}
        enableCrossOsArchive: true

    - shell: bash
      name: Copy restored maven repo to home folder in Windows
      if: (steps.cache-maven-repository.outputs.cache-hit == 'true' || steps.restore-maven-repository.outputs.cache-hit == 'true') && runner.os == 'Windows'
      run: |
        if [ -d ../../../.m2/repository ]; then
          cp -r ../../../.m2/repository ~/.m2
          rm -r ../../../.m2/repository
        fi

    - id: node-cache
      name: Node cache
      uses: ./.github/actions/node-cache
