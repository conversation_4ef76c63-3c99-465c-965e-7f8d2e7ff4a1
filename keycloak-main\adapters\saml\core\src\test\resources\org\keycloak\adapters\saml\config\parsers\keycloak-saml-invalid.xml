<!--
  ~ Copyright 2016 Red Hat, Inc. and/or its affiliates
  ~ and other contributors as indicated by the <AUTHOR>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<keycloak-saml-adapter xmlns="urn:keycloak:saml:adapter">
    <SP entityID="sp"
        sslPolicy="ssl"
        nameIDPolicyFormat="format"
        signatureAlgorithm=""
        signatureCanonicalizationMethod=""
        forceAuthentication="true"
        isPassive="true">
        <Keys>
            <Key signing="true" >
                <KeyStore file="file" resource="cp" password="pw">
                    <PrivateKey alias="private alias" password="private pw"/>
                    <Certificate alias="cert alias"/>
                </KeyStore>
            </Key>
            <Key encryption="true">
                <PrivateKeyPemmm>
                    private pem
                </PrivateKeyPemmm>
                <PublicKeyPem>
                    public pem
                </PublicKeyPem>
            </Key>
        </Keys>
        <PrincipalNameMapping policy="policy" attribute="attribute"/>
        <RoleMapping>
            <Attribute name="member"/>
        </RoleMapping>
        <IDP entityID="idp"
             signaturesRequired="true"
                >
            <SingleSignOnService signRequest="true"
                                 validateResponseSignature="true"
                                 requestBinding="post"
                                 bindingUrl="url"
                    />

            <Keys>
                <Key signing="true">
                    <CertificatePem>
                        cert pem
                    </CertificatePem>
                </Key>
            </Keys>
        </IDP>
    </SP>
</keycloak-saml-adapter>