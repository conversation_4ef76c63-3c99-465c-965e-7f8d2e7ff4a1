<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Copyright 2016 Red Hat, Inc. and/or its affiliates
  ~ and other contributors as indicated by the <AUTHOR>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<xs:schema version="1.0"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="urn:keycloak:saml:adapter"
           targetNamespace="urn:keycloak:saml:adapter"
           elementFormDefault="qualified"
           attributeFormDefault="unqualified">

    <xs:element name="keycloak-saml-adapter" type="adapter-type"/>
    <xs:complexType name="adapter-type">
        <xs:annotation>
            <xs:documentation>
                <![CDATA[
                    The Keycloak SAML Adapter keycloak-saml.xml config file
                ]]>
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="SP" maxOccurs="1" minOccurs="0" type="sp-type"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="sp-type">
        <xs:all>
            <xs:element name="Keys" type="keys-type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="PrincipalNameMapping" type="principal-name-mapping-type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="RoleIdentifiers" type="role-identifiers-type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="IDP" type="idp-type" minOccurs="1" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="entityID" type="xs:string" use="required"/>
        <xs:attribute name="sslPolicy" type="xs:string" use="optional"/>
        <xs:attribute name="nameIDPolicyFormat" type="xs:string" use="optional"/>
        <xs:attribute name="logoutPage" type="xs:string" use="optional"/>
        <xs:attribute name="forceAuthentication" type="xs:boolean" use="optional"/>
        <xs:attribute name="isPassive" type="xs:boolean" use="optional"/>
        <xs:attribute name="turnOffChangeSessionIdOnLogin" type="xs:boolean" use="optional"/>
    </xs:complexType>

    <xs:complexType name="keys-type">
        <xs:sequence>
            <xs:element name="Key" type="key-type" minOccurs="1" maxOccurs="2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="key-type">
        <xs:all>
            <xs:element name="KeyStore" maxOccurs="1" minOccurs="0" type="key-store-type"/>
            <xs:element name="PrivateKeyPem" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="PublicKeyPem" type="xs:string" minOccurs="0" maxOccurs="1"/>
            <xs:element name="CertificatePem" type="xs:string" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="signing" type="xs:boolean" use="optional"/>
        <xs:attribute name="encryption" type="xs:boolean" use="optional"/>
    </xs:complexType>
    <xs:complexType name="key-store-type">
        <xs:all>
            <xs:element name="PrivateKey" maxOccurs="1" minOccurs="0" type="private-key-type"/>
            <xs:element name="Certificate" type="certificate-type" minOccurs="0" maxOccurs="1"/>
        </xs:all>
        <xs:attribute name="file" type="xs:string" use="optional"/>
        <xs:attribute name="resource" type="xs:string" use="optional"/>
        <xs:attribute name="password" type="xs:string" use="required"/>
    </xs:complexType>
    <xs:complexType name="private-key-type">
        <xs:attribute name="alias" type="xs:string" use="required"/>
        <xs:attribute name="password" type="xs:string" use="required"/>
    </xs:complexType>
    <xs:complexType name="certificate-type">
        <xs:attribute name="alias" type="xs:string" use="required"/>
    </xs:complexType>
    <xs:complexType name="principal-name-mapping-type">
        <xs:attribute name="policy" type="xs:string" use="required"/>
        <xs:attribute name="attribute" type="xs:string" use="optional"/>
    </xs:complexType>
    <xs:complexType name="role-identifiers-type">
        <xs:choice minOccurs="0" maxOccurs="unbounded">
            <xs:element name="Attribute" maxOccurs="unbounded" minOccurs="0" type="attribute-type"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="attribute-type">
        <xs:attribute name="name" type="xs:string" use="required"/>
    </xs:complexType>
    <xs:complexType name="idp-type">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="SingleSignOnService" maxOccurs="1" minOccurs="1" type="sign-on-type"/>
            <xs:element name="SingleLogoutService" type="logout-type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="Keys" type="keys-type" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
        <xs:attribute name="entityID" type="xs:string" use="required"/>
        <xs:attribute name="signaturesRequired" type="xs:boolean" use="required"/>
        <xs:attribute name="signatureAlgorithm" type="xs:string" use="optional"/>
        <xs:attribute name="signatureCanonicalizationMethod" type="xs:string" use="optional"/>
        <xs:attribute name="encryption" type="xs:boolean" use="optional"/>
    </xs:complexType>
    <xs:complexType name="sign-on-type">
        <xs:attribute name="signRequest" type="xs:boolean" use="optional"/>
        <xs:attribute name="validateResponseSignature" type="xs:boolean" use="optional"/>
        <xs:attribute name="validateAssertionSignature" type="xs:boolean" use="optional"/>
        <xs:attribute name="requestBinding" type="xs:string" use="optional"/>
        <xs:attribute name="responseBinding" type="xs:string" use="optional"/>
        <xs:attribute name="bindingUrl" type="xs:string" use="optional"/>
    </xs:complexType>

    <xs:complexType name="logout-type">
        <xs:attribute name="signRequest" type="xs:boolean" use="optional"/>
        <xs:attribute name="signResponse" type="xs:boolean" use="optional"/>
        <xs:attribute name="validateRequestSignature" type="xs:boolean" use="optional"/>
        <xs:attribute name="validateResponseSignature" type="xs:boolean" use="optional"/>
        <xs:attribute name="requestBinding" type="xs:string" use="optional"/>
        <xs:attribute name="responseBinding" type="xs:string" use="optional"/>
        <xs:attribute name="postBindingUrl" type="xs:string" use="optional"/>
        <xs:attribute name="redirectBindingUrl" type="xs:string" use="optional"/>
    </xs:complexType>




</xs:schema>
