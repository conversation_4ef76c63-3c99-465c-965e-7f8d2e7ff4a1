# SSL/HTTPS Setup Guide for NGC Aether Production Environment

## Overview
This guide walks you through setting up SSL certificates for secure HTTPS access to all services in your NGC Aether environment.

## Prerequisites
- All Docker services are running and healthy
- DNS records are configured to point to your server IP (************)
- Nginx Proxy Manager is accessible at http://localhost:81

## Step 1: Access Nginx Proxy Manager

1. Open browser: `http://localhost:81`
2. Login with default credentials:
   - Email: `<EMAIL>`
   - Password: `changeme`
3. Change default credentials when prompted

## Step 2: Configure Proxy Hosts

### 2.1 OpenWebUI Proxy Host

1. Click "Proxy Hosts" → "Add Proxy Host"
2. Configure:
   - **Domain Names**: `openwebui.aether-prod.gc1.myngc.com`
   - **Scheme**: `http`
   - **Forward Hostname/IP**: `open-webui` (container name)
   - **Forward Port**: `8080`
   - **Cache Assets**: ✅ Enabled
   - **Block Common Exploits**: ✅ Enabled
   - **Websockets Support**: ✅ Enabled

3. SSL Tab:
   - **SSL Certificate**: Request a new SSL Certificate
   - **Force SSL**: ✅ Enabled
   - **HTTP/2 Support**: ✅ Enabled
   - **HSTS Enabled**: ✅ Enabled
   - **HSTS Subdomains**: ✅ Enabled

### 2.2 Keycloak Proxy Host

1. Add new Proxy Host:
   - **Domain Names**: `keycloak.aether-prod.gc1.myngc.com`
   - **Scheme**: `http`
   - **Forward Hostname/IP**: `keycloak`
   - **Forward Port**: `8080`
   - **Cache Assets**: ✅ Enabled
   - **Block Common Exploits**: ✅ Enabled

2. Advanced Tab - Custom Nginx Configuration:
```nginx
# Keycloak specific headers
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_set_header X-Forwarded-Host $host;
proxy_set_header X-Forwarded-Port $server_port;

# Increase buffer sizes for Keycloak
proxy_buffer_size 128k;
proxy_buffers 4 256k;
proxy_busy_buffers_size 256k;
```

3. SSL Tab: Same as OpenWebUI

### 2.3 Additional Service Proxy Hosts

Repeat for other services:

**Ollama API**:
- Domain: `ollama.aether-prod.gc1.myngc.com`
- Forward to: `ollama:11434`

**Tika Server**:
- Domain: `tika.aether-prod.gc1.myngc.com`
- Forward to: `tika:9998`

**Pipelines**:
- Domain: `pipelines.aether-prod.gc1.myngc.com`
- Forward to: `pipelines:9099`

## Step 3: DNS Configuration

Ensure these DNS records exist in your DNS server (NG-V2K-DC05.northgrum.com):

```
openwebui.aether-prod.gc1.myngc.com    A    ************
keycloak.aether-prod.gc1.myngc.com     A    ************
ollama.aether-prod.gc1.myngc.com       A    ************
tika.aether-prod.gc1.myngc.com         A    ************
pipelines.aether-prod.gc1.myngc.com    A    ************
npm.aether-prod.gc1.myngc.com          A    ************
```

## Step 4: Test SSL Configuration

### 4.1 Verify DNS Resolution
```powershell
nslookup openwebui.aether-prod.gc1.myngc.com NG-V2K-DC05.northgrum.com
nslookup keycloak.aether-prod.gc1.myngc.com NG-V2K-DC05.northgrum.com
```

### 4.2 Test HTTPS Access
```powershell
# Test OpenWebUI
Invoke-WebRequest -Uri "https://openwebui.aether-prod.gc1.myngc.com" -UseBasicParsing

# Test Keycloak
Invoke-WebRequest -Uri "https://keycloak.aether-prod.gc1.myngc.com" -UseBasicParsing

# Test OIDC Discovery
Invoke-WebRequest -Uri "https://keycloak.aether-prod.gc1.myngc.com/realms/openwebui-realm/.well-known/openid-configuration" -UseBasicParsing
```

## Step 5: Update Environment Variables

After SSL is configured, verify your .env file has the correct HTTPS URLs:

```bash
# Production HTTPS URLs (should already be configured)
WEBUI_URL=https://openwebui.aether-prod.gc1.myngc.com
KEYCLOAK_HOSTNAME=keycloak.aether-prod.gc1.myngc.com
OAUTH_SERVER_URL=https://keycloak.aether-prod.gc1.myngc.com/realms/openwebui-realm
OPENID_PROVIDER_URL=https://keycloak.aether-prod.gc1.myngc.com/realms/openwebui-realm
OAUTH_REDIRECT_URI=https://openwebui.aether-prod.gc1.myngc.com/oauth/oidc/callback
```

## Step 6: Restart Services

After SSL configuration:
```powershell
# Restart services to pick up new SSL configuration
docker compose restart keycloak open-webui
```

## Troubleshooting

### Common Issues:

1. **Certificate Generation Fails**:
   - Check DNS resolution
   - Ensure ports 80/443 are accessible
   - Verify domain ownership

2. **Keycloak SSL Issues**:
   - Check proxy headers in Nginx config
   - Verify KC_HOSTNAME matches certificate domain

3. **OpenWebUI OAuth Redirect Issues**:
   - Ensure OAUTH_REDIRECT_URI uses HTTPS
   - Check Keycloak client configuration

### Verification Commands:
```powershell
# Check certificate validity
openssl s_client -connect openwebui.aether-prod.gc1.myngc.com:443 -servername openwebui.aether-prod.gc1.myngc.com

# Test SSL grade
curl -I https://openwebui.aether-prod.gc1.myngc.com
```

## Security Best Practices

1. **Strong SSL Configuration**:
   - Use TLS 1.2+ only
   - Enable HSTS
   - Use strong cipher suites

2. **Certificate Management**:
   - Set up automatic renewal
   - Monitor certificate expiration
   - Use proper certificate chains

3. **Access Control**:
   - Restrict admin access to NPM
   - Use strong passwords
   - Enable 2FA where possible
