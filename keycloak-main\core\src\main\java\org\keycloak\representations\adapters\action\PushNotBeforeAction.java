/*
 * Copyright 2016 Red Hat, Inc. and/or its affiliates
 * and other contributors as indicated by the <AUTHOR>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.keycloak.representations.adapters.action;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 * @version $Revision: 1 $
 */
public class PushNotBeforeAction extends AdminAction {

    public static final String PUSH_NOT_BEFORE = "PUSH_NOT_BEFORE";
    protected int notBefore;

    public PushNotBeforeAction() {
    }

    public PushNotBeforeAction(String id, int expiration, String resource, int notBefore) {
        super(id, expiration, resource, PUSH_NOT_BEFORE);
        this.notBefore = notBefore;
    }

    public int getNotBefore() {
        return notBefore;
    }

    public void setNotBefore(int notBefore) {
        this.notBefore = notBefore;
    }

    @Override
    public boolean validate() {
        return PUSH_NOT_BEFORE.equals(action);
    }

}
