<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2016 Red Hat, Inc. and/or its affiliates
  ~ and other contributors as indicated by the <AUTHOR>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.keycloak</groupId>
        <artifactId>keycloak-parent</artifactId>
        <version>999.0.0-SNAPSHOT</version>
        <relativePath>../../../../pom.xml</relativePath>
    </parent>

    <artifactId>keycloak-saml-wildfly-subsystem</artifactId>
    <name>Keycloak Wildfly SAML Adapter Subsystem</name>
    <description/>
    <packaging>jar</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <redirectTestOutputToFile>false</redirectTestOutputToFile>
                    <enableAssertions>true</enableAssertions>
                    <systemProperties>
                        <property>
                            <name>jboss.home</name>
                            <value>${jboss.home}</value>
                        </property>
                    </systemProperties>
                    <includes>
                        <include>**/*TestCase.java</include>
                    </includes>
                </configuration>
            </plugin>            
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.wildfly.core</groupId>
            <artifactId>wildfly-controller</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.wildfly.core</groupId>
            <artifactId>wildfly-server</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>${ee.maven.groupId}</groupId>
            <artifactId>wildfly-web-common</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging-annotations</artifactId>
            <!-- This is a compile-time dependency of this project, but is not needed at compile or runtime by other
            projects that depend on this project.-->
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging-processor</artifactId>
            <!-- This is a compile-time dependency of this project, but is not needed at compile or runtime by other
            projects that depend on this project.-->
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.wildfly.common</groupId>
            <artifactId>wildfly-common</artifactId>
            <version>${wildfly.common.wildfly.aligned.version}</version>
        </dependency>

        <dependency>
            <groupId>org.wildfly.core</groupId>
            <artifactId>wildfly-subsystem-test-framework</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-saml-adapter-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-saml-wildfly-elytron-adapter</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>
