/*
 * Copyright 2024 Red Hat, Inc. and/or its affiliates
 * and other contributors as indicated by the <AUTHOR>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.keycloak.sdjwt.consumer;

import org.keycloak.common.VerificationException;
import org.keycloak.crypto.SignatureVerifierContext;
import org.keycloak.sdjwt.IssuerSignedJWT;

import java.util.List;

/**
 * A trusted Issuer for running SD-JWT VP verification.
 *
 * <AUTHOR> href="mailto:<PERSON>.<PERSON>@adorsys.com"><PERSON></a>
 */
public interface TrustedSdJwtIssuer {

    /**
     * Resolves potential verifying keys to validate the Issuer-signed JWT.
     * The method ensures that the resolved public keys can be trusted.
     *
     * @param issuerSignedJWT The Issuer-signed JWT to validate.
     * @return trusted verifying keys
     * @throws VerificationException if no trustworthy verifying key could be resolved
     */
    List<SignatureVerifierContext> resolveIssuerVerifyingKeys(IssuerSignedJWT issuerSignedJWT)
            throws VerificationException;
}
