name: Changed Files
description: Checks changes against target branch

inputs:
  token:
    description: GitHub Token
    required: true

outputs:
  ci:
    description: Should "ci.yml" execute
    value: ${{ steps.changes.outputs.ci }}
  ci-quarkus:
    description: Should "ci.yml" execute (Quarkus)
    value: ${{ steps.changes.outputs.ci-quarkus }}
  ci-store:
    description: Should "ci.yml" execute (Store)
    value: ${{ steps.changes.outputs.ci-store }}
  ci-sssd:
    description: Should "ci.yml" execute (SSSD)
    value: ${{ steps.changes.outputs.ci-sssd }}
  ci-webauthn:
    description: Should "ci.yml" execute (WebAuthn)
    value: ${{ steps.changes.outputs.ci-webauthn }}
  operator:
    description: Should "operator-ci.yml" execute
    value: ${{ steps.changes.outputs.operator }}
  js:
    description: Should "js-ci.yml" execute
    value: ${{ steps.changes.outputs.js }}
  codeql-java:
    description: Should "codeql-analysis.yml / java" execute
    value: ${{ steps.changes.outputs.codeql-java }}
  codeql-javascript:
    description: Should "codeql-analysis.yml / javascript" execute
    value: ${{ steps.changes.outputs.codeql-javascript }}
  codeql-typescript:
    description: Should "codeql-analysis.yml / typescript" execute
    value: ${{ steps.changes.outputs.codeql-typescript }}
  guides:
    description: Should "guides.yml" execute
    value: ${{ steps.changes.outputs.guides }}
  documentation:
    description: Should "documentation.yml" execute
    value: ${{ steps.changes.outputs.documentation }}
  sssd:
    description: Should "sssd.yml" execute
    value: ${{ steps.changes.outputs.sssd }}

runs:
  using: composite
  steps:
    - id: changes
      name: Find changes
      shell: bash
      run: .github/actions/conditional/conditional.sh ${{ github.repository }} ${{ github.ref }}
      env:
        GITHUB_TOKEN: ${{ inputs.token }}
