# NGC Aether Production Environment Testing Script
# This script performs comprehensive testing of all services

Write-Host "🧪 Testing NGC Aether Production Environment..." -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Function to test HTTP endpoint
function Test-HttpEndpoint {
    param(
        [string]$Url,
        [string]$ServiceName,
        [int]$ExpectedStatusCode = 200,
        [int]$TimeoutSeconds = 30
    )
    
    try {
        Write-Host "🔍 Testing $ServiceName at $Url..." -ForegroundColor Yellow
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSeconds -UseBasicParsing
        
        if ($response.StatusCode -eq $ExpectedStatusCode) {
            Write-Host "✅ $ServiceName is responding correctly (Status: $($response.StatusCode))" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️ $ServiceName returned unexpected status: $($response.StatusCode)" -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "❌ $ServiceName is not responding: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to test database connectivity
function Test-DatabaseConnectivity {
    Write-Host "🗄️ Testing PostgreSQL connectivity..." -ForegroundColor Yellow
    
    try {
        $result = docker exec ai-postgres pg_isready -U openwebui -d openwebui
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ PostgreSQL is accepting connections" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ PostgreSQL is not ready" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Failed to test PostgreSQL: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to check container logs for errors
function Check-ContainerLogs {
    param([string]$ContainerName)
    
    Write-Host "📋 Checking $ContainerName logs for errors..." -ForegroundColor Yellow
    $logs = docker logs $ContainerName --tail 50 2>&1
    
    $errorPatterns = @("ERROR", "FATAL", "Exception", "failed", "error")
    $hasErrors = $false
    
    foreach ($pattern in $errorPatterns) {
        if ($logs -match $pattern) {
            $hasErrors = $true
            break
        }
    }
    
    if ($hasErrors) {
        Write-Host "⚠️ Found potential errors in $ContainerName logs" -ForegroundColor Yellow
        Write-Host "Recent logs:" -ForegroundColor Gray
        $logs | Select-Object -Last 10 | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
    } else {
        Write-Host "✅ No obvious errors in $ContainerName logs" -ForegroundColor Green
    }
}

# Test 1: Container Status
Write-Host "`n1️⃣ Testing Container Status..." -ForegroundColor Cyan
$containers = @("ai-postgres", "ai-keycloak", "ai-nginx-proxy-manager", "ai-ollama", "ai-open-webui", "ai-pipelines", "ai-tika")
$allRunning = $true

foreach ($container in $containers) {
    $status = docker ps --filter "name=$container" --format "{{.Status}}"
    if ($status -like "*Up*") {
        Write-Host "✅ $container is running" -ForegroundColor Green
    } else {
        Write-Host "❌ $container is not running" -ForegroundColor Red
        $allRunning = $false
    }
}

# Test 2: Database Connectivity
Write-Host "`n2️⃣ Testing Database Connectivity..." -ForegroundColor Cyan
$dbTest = Test-DatabaseConnectivity

# Test 3: Service Health Endpoints
Write-Host "`n3️⃣ Testing Service Health Endpoints..." -ForegroundColor Cyan
$healthTests = @{
    "Keycloak" = "http://localhost:9090/health"
    "OpenWebUI" = "http://localhost:3000/health"
    "Nginx Proxy Manager" = "http://localhost:81/api"
    "Ollama" = "http://localhost:11434/api/tags"
    "Tika" = "http://localhost:9998/version"
    "Pipelines" = "http://localhost:9099/health"
}

$healthResults = @{}
foreach ($service in $healthTests.Keys) {
    $healthResults[$service] = Test-HttpEndpoint -Url $healthTests[$service] -ServiceName $service
}

# Test 4: Keycloak Realm Configuration
Write-Host "`n4️⃣ Testing Keycloak Realm Configuration..." -ForegroundColor Cyan
$realmTest = Test-HttpEndpoint -Url "http://localhost:9090/realms/openwebui-realm" -ServiceName "Keycloak Realm"

# Test 5: OIDC Discovery
Write-Host "`n5️⃣ Testing OIDC Discovery..." -ForegroundColor Cyan
$oidcTest = Test-HttpEndpoint -Url "http://localhost:9090/realms/openwebui-realm/.well-known/openid-configuration" -ServiceName "OIDC Discovery"

# Test 6: Container Logs Check
Write-Host "`n6️⃣ Checking Container Logs..." -ForegroundColor Cyan
foreach ($container in $containers) {
    Check-ContainerLogs -ContainerName $container
}

# Test Summary
Write-Host "`n📊 Test Summary:" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan

$totalTests = 0
$passedTests = 0

# Container status
$totalTests++
if ($allRunning) { $passedTests++ }
Write-Host "Container Status: $(if ($allRunning) { '✅ PASS' } else { '❌ FAIL' })" -ForegroundColor $(if ($allRunning) { 'Green' } else { 'Red' })

# Database connectivity
$totalTests++
if ($dbTest) { $passedTests++ }
Write-Host "Database Connectivity: $(if ($dbTest) { '✅ PASS' } else { '❌ FAIL' })" -ForegroundColor $(if ($dbTest) { 'Green' } else { 'Red' })

# Health endpoints
foreach ($service in $healthResults.Keys) {
    $totalTests++
    if ($healthResults[$service]) { $passedTests++ }
    Write-Host "$service Health: $(if ($healthResults[$service]) { '✅ PASS' } else { '❌ FAIL' })" -ForegroundColor $(if ($healthResults[$service]) { 'Green' } else { 'Red' })
}

# Realm configuration
$totalTests++
if ($realmTest) { $passedTests++ }
Write-Host "Keycloak Realm: $(if ($realmTest) { '✅ PASS' } else { '❌ FAIL' })" -ForegroundColor $(if ($realmTest) { 'Green' } else { 'Red' })

# OIDC Discovery
$totalTests++
if ($oidcTest) { $passedTests++ }
Write-Host "OIDC Discovery: $(if ($oidcTest) { '✅ PASS' } else { '❌ FAIL' })" -ForegroundColor $(if ($oidcTest) { 'Green' } else { 'Red' })

Write-Host "`n🎯 Overall Result: $passedTests/$totalTests tests passed" -ForegroundColor $(if ($passedTests -eq $totalTests) { 'Green' } else { 'Yellow' })

if ($passedTests -eq $totalTests) {
    Write-Host "🎉 All tests passed! Your NGC Aether environment is ready for production." -ForegroundColor Green
} else {
    Write-Host "⚠️ Some tests failed. Please review the output above and fix any issues." -ForegroundColor Yellow
}
