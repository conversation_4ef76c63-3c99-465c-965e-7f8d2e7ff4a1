# SSO Integration Testing Guide

## Overview
This guide provides step-by-step instructions to test the Single Sign-On (SSO) integration between Keycloak and OpenWebUI.

## Prerequisites
- All Docker services are running and healthy
- SSL certificates are configured (if using HTTPS)
- Keycloak realm is imported and configured
- OpenWebUI is configured with correct OAuth settings

## Step 1: Verify Keycloak Configuration

### 1.1 Access Keycloak Admin Console
```
URL: http://localhost:9090 (or https://keycloak.aether-prod.gc1.myngc.com)
Username: admin
Password: [from your .env file - KEYCLOAK_ADMIN_PASSWORD]
```

### 1.2 Verify Realm Configuration
1. Select "openwebui-realm" from the realm dropdown
2. Go to "Realm Settings" → "General"
3. Verify:
   - Realm is enabled
   - SSL required is set to "external requests"
   - Login theme is configured

### 1.3 Verify Client Configuration
1. Go to "Clients" → "openwebui"
2. Verify settings:
   - Client ID: `openwebui`
   - Client Protocol: `openid-connect`
   - Access Type: `confidential`
   - Standard Flow Enabled: ✅
   - Direct Access Grants Enabled: ✅
   - Valid Redirect URIs: 
     - `http://localhost:3000/oauth/oidc/callback`
     - `https://openwebui.aether-prod.gc1.myngc.com/oauth/oidc/callback`

### 1.4 Get Client Secret
1. Go to "Clients" → "openwebui" → "Credentials" tab
2. Copy the "Secret" value
3. Verify it matches `OAUTH_CLIENT_SECRET` in your .env file

## Step 2: Test OIDC Discovery

### 2.1 Test Discovery Endpoint
```powershell
# Test OIDC discovery endpoint
$discoveryUrl = "http://localhost:9090/realms/openwebui-realm/.well-known/openid-configuration"
# Or for HTTPS: $discoveryUrl = "https://keycloak.aether-prod.gc1.myngc.com/realms/openwebui-realm/.well-known/openid-configuration"

$response = Invoke-WebRequest -Uri $discoveryUrl -UseBasicParsing
$config = $response.Content | ConvertFrom-Json

# Verify key endpoints
Write-Host "Authorization Endpoint: $($config.authorization_endpoint)"
Write-Host "Token Endpoint: $($config.token_endpoint)"
Write-Host "UserInfo Endpoint: $($config.userinfo_endpoint)"
Write-Host "Issuer: $($config.issuer)"
```

### 2.2 Verify Expected Endpoints
The discovery should return:
- `authorization_endpoint`: `http://localhost:9090/realms/openwebui-realm/protocol/openid-connect/auth`
- `token_endpoint`: `http://localhost:9090/realms/openwebui-realm/protocol/openid-connect/token`
- `userinfo_endpoint`: `http://localhost:9090/realms/openwebui-realm/protocol/openid-connect/userinfo`

## Step 3: Create Test Users

### 3.1 Create Test User in Keycloak
1. In Keycloak Admin Console, go to "Users"
2. Click "Add user"
3. Fill in:
   - Username: `testuser`
   - Email: `<EMAIL>`
   - First Name: `Test`
   - Last Name: `User`
   - Email Verified: ✅
   - Enabled: ✅

### 3.2 Set User Password
1. Go to "Users" → "testuser" → "Credentials" tab
2. Set password:
   - Password: `TestPassword123!`
   - Temporary: ❌ (uncheck)
3. Click "Set Password"

### 3.3 Assign User Roles
1. Go to "Users" → "testuser" → "Role Mappings" tab
2. Assign appropriate roles for OpenWebUI access

## Step 4: Test SSO Authentication Flow

### 4.1 Access OpenWebUI
```
URL: http://localhost:3000 (or https://openwebui.aether-prod.gc1.myngc.com)
```

### 4.2 Initiate SSO Login
1. On OpenWebUI login page, look for "Sign in with Keycloak SSO" button
2. Click the SSO login button
3. You should be redirected to Keycloak login page

### 4.3 Complete Authentication
1. Enter test user credentials:
   - Username: `testuser`
   - Password: `TestPassword123!`
2. Click "Sign In"
3. You should be redirected back to OpenWebUI
4. Verify you're logged in as the test user

## Step 5: Verify User Information

### 5.1 Check User Profile in OpenWebUI
1. In OpenWebUI, click on user profile/settings
2. Verify user information is populated from Keycloak:
   - Username: `testuser`
   - Email: `<EMAIL>`
   - Name: `Test User`

### 5.2 Test User Permissions
1. Verify the user has appropriate access to OpenWebUI features
2. Test creating a chat/conversation
3. Verify AI model access (if configured)

## Step 6: Test Logout Flow

### 6.1 Logout from OpenWebUI
1. Click logout in OpenWebUI
2. Verify you're logged out
3. Try accessing protected pages - should redirect to login

### 6.2 Test Single Logout (if configured)
1. Login again through SSO
2. Open Keycloak in another tab and logout there
3. Refresh OpenWebUI - should be logged out (if single logout is configured)

## Step 7: Troubleshooting Common Issues

### 7.1 OAuth Redirect URI Mismatch
**Error**: "Invalid redirect URI"
**Solution**: 
- Check OAUTH_REDIRECT_URI in .env matches Keycloak client config
- Ensure protocol (http/https) matches

### 7.2 Client Authentication Failed
**Error**: "Client authentication failed"
**Solution**:
- Verify OAUTH_CLIENT_SECRET matches Keycloak client secret
- Check client is set to "confidential" access type

### 7.3 OIDC Discovery Fails
**Error**: "Failed to fetch OIDC configuration"
**Solution**:
- Verify OPENID_PROVIDER_URL is correct
- Check Keycloak is accessible from OpenWebUI container
- Test discovery endpoint manually

### 7.4 User Not Created in OpenWebUI
**Error**: User authenticates but doesn't appear in OpenWebUI
**Solution**:
- Check ENABLE_OAUTH_SIGNUP is true
- Verify user email is provided by Keycloak
- Check OpenWebUI logs for errors

## Step 8: Advanced Testing

### 8.1 Test Multiple Users
1. Create additional test users in Keycloak
2. Test concurrent logins
3. Verify user isolation

### 8.2 Test Role-Based Access
1. Create different roles in Keycloak
2. Assign roles to users
3. Configure OpenWebUI to respect roles (if implemented)

### 8.3 Test Token Refresh
1. Login and wait for token to near expiration
2. Perform actions in OpenWebUI
3. Verify token is refreshed automatically

## Step 9: Performance Testing

### 9.1 Load Testing SSO
```powershell
# Simple load test script
for ($i = 1; $i -le 10; $i++) {
    Start-Job -ScriptBlock {
        Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing
    }
}

# Wait for jobs to complete
Get-Job | Wait-Job | Receive-Job
```

### 9.2 Monitor Performance
1. Check Keycloak response times
2. Monitor OpenWebUI authentication latency
3. Verify database performance during authentication

## Verification Checklist

- [ ] Keycloak admin console accessible
- [ ] Realm configuration correct
- [ ] Client configuration matches environment variables
- [ ] OIDC discovery endpoint responds correctly
- [ ] Test user created and configured
- [ ] SSO login redirects to Keycloak
- [ ] Authentication completes successfully
- [ ] User redirected back to OpenWebUI
- [ ] User profile populated correctly
- [ ] Logout works properly
- [ ] Error handling works for invalid credentials
- [ ] Multiple users can authenticate
- [ ] Performance is acceptable

## Success Criteria

✅ **SSO Integration is successful when**:
1. Users can login to OpenWebUI using Keycloak credentials
2. User information is correctly synchronized
3. Authentication flow is seamless and secure
4. Logout works properly
5. Error handling is appropriate
6. Performance meets requirements
