# NPM SSO Testing Script for NGC Aether
# This script tests the Nginx Proxy Manager configuration for SSO functionality

param(
    [switch]$Detailed,
    [switch]$SkipSSL
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[✓] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[⚠] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[✗] $Message" -ForegroundColor Red
}

# Test service connectivity
function Test-ServiceConnectivity {
    Write-Status "Testing internal service connectivity..."
    
    $services = @{
        "Keycloak" = @{
            "container" = "ai-keycloak"
            "url" = "http://keycloak:8080/health"
            "port" = 8080
        }
        "OpenWebUI" = @{
            "container" = "ai-open-webui"
            "url" = "http://open-webui:8080/health"
            "port" = 8080
        }
        "NPM" = @{
            "container" = "ai-nginx-proxy-manager"
            "url" = "http://localhost:81/api"
            "port" = 81
        }
    }
    
    $allHealthy = $true
    foreach ($service in $services.GetEnumerator()) {
        try {
            $result = docker exec $service.Value.container curl -s -o /dev/null -w "%{http_code}" $service.Value.url 2>$null
            if ($result -in @("200", "302", "401")) {
                Write-Success "$($service.Key) internal connectivity: OK"
            } else {
                Write-Warning "$($service.Key) returned status: $result"
                $allHealthy = $false
            }
        }
        catch {
            Write-Error "$($service.Key) connectivity test failed"
            $allHealthy = $false
        }
    }
    
    return $allHealthy
}

# Test external access
function Test-ExternalAccess {
    Write-Status "Testing external service access..."
    
    $endpoints = @{
        "NPM Admin" = "http://************:81"
        "Keycloak Direct" = "http://************:9090"
        "OpenWebUI Direct" = "http://************:3000"
    }
    
    $allAccessible = $true
    foreach ($endpoint in $endpoints.GetEnumerator()) {
        try {
            $response = Invoke-WebRequest -Uri $endpoint.Value -Method Head -TimeoutSec 10 -ErrorAction SilentlyContinue
            if ($response.StatusCode -in @(200, 302, 401)) {
                Write-Success "$($endpoint.Key): Accessible"
            } else {
                Write-Warning "$($endpoint.Key): Status $($response.StatusCode)"
                $allAccessible = $false
            }
        }
        catch {
            Write-Warning "$($endpoint.Key): Not accessible - $($_.Exception.Message)"
            $allAccessible = $false
        }
    }
    
    return $allAccessible
}

# Test DNS resolution
function Test-DnsResolution {
    Write-Status "Testing DNS resolution for NGC Aether domains..."
    
    $domains = @(
        "aether-prod.gc1.myngc.com",
        "openwebui.aether-prod.gc1.myngc.com",
        "keycloak.aether-prod.gc1.myngc.com",
        "npm.aether-prod.gc1.myngc.com"
    )
    
    $allResolved = $true
    foreach ($domain in $domains) {
        try {
            $result = Resolve-DnsName -Name $domain -ErrorAction SilentlyContinue
            if ($result -and $result.IPAddress -contains "************") {
                Write-Success "$domain → ************"
            } else {
                Write-Warning "$domain → $($result.IPAddress -join ', ') (expected: ************)"
                $allResolved = $false
            }
        }
        catch {
            Write-Error "$domain: DNS resolution failed"
            $allResolved = $false
        }
    }
    
    return $allResolved
}

# Test SSL certificates (if configured)
function Test-SslCertificates {
    if ($SkipSSL) {
        Write-Status "SSL testing skipped"
        return $true
    }
    
    Write-Status "Testing SSL certificates..."
    
    $sslDomains = @(
        "https://openwebui.aether-prod.gc1.myngc.com",
        "https://keycloak.aether-prod.gc1.myngc.com"
    )
    
    $allValid = $true
    foreach ($domain in $sslDomains) {
        try {
            $response = Invoke-WebRequest -Uri $domain -Method Head -TimeoutSec 10 -ErrorAction SilentlyContinue
            if ($response.StatusCode -in @(200, 302, 401)) {
                Write-Success "$domain: SSL certificate valid"
            } else {
                Write-Warning "$domain: Status $($response.StatusCode)"
                $allValid = $false
            }
        }
        catch {
            if ($_.Exception.Message -match "SSL") {
                Write-Warning "$domain: SSL certificate issue - $($_.Exception.Message)"
                $allValid = $false
            } else {
                Write-Warning "$domain: Not accessible (may be normal if NPM not configured yet)"
            }
        }
    }
    
    return $allValid
}

# Test NPM configuration readiness
function Test-NpmConfiguration {
    Write-Status "Testing NPM configuration readiness..."
    
    try {
        # Check if NPM is accessible
        $npmResponse = Invoke-WebRequest -Uri "http://************:81" -Method Head -TimeoutSec 10 -ErrorAction SilentlyContinue
        
        if ($npmResponse.StatusCode -eq 200) {
            Write-Success "NPM admin interface is accessible"
            Write-Status "NPM is ready for configuration at: http://************:81"
            Write-Status "Default credentials: <EMAIL> / changeme"
            return $true
        } else {
            Write-Warning "NPM returned status: $($npmResponse.StatusCode)"
            return $false
        }
    }
    catch {
        Write-Error "NPM admin interface not accessible: $($_.Exception.Message)"
        return $false
    }
}

# Test OAuth configuration
function Test-OAuthConfiguration {
    Write-Status "Testing OAuth configuration..."
    
    try {
        # Check if .env has correct OAuth URLs
        $envContent = Get-Content ".env" -Raw
        
        $checks = @{
            "HTTPS URLs" = $envContent -match "https://.*aether-prod\.gc1\.myngc\.com"
            "OAuth Client ID" = $envContent -match "OAUTH_CLIENT_ID=openwebui"
            "OAuth Redirect URI" = $envContent -match "oauth/oidc/callback"
            "Keycloak Realm URL" = $envContent -match "realms/openwebui-realm"
        }
        
        $allValid = $true
        foreach ($check in $checks.GetEnumerator()) {
            if ($check.Value) {
                Write-Success "$($check.Key): Configured"
            } else {
                Write-Warning "$($check.Key): Not properly configured"
                $allValid = $false
            }
        }
        
        return $allValid
    }
    catch {
        Write-Error "Could not read .env file"
        return $false
    }
}

# Generate NPM configuration summary
function Show-NpmConfigurationSummary {
    Write-Host ""
    Write-Host "📋 NPM Configuration Summary" -ForegroundColor Cyan
    Write-Host "============================" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "🔧 Required NPM Proxy Host Configurations:" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "1. Keycloak Proxy Host:" -ForegroundColor White
    Write-Host "   Domain: keycloak.aether-prod.gc1.myngc.com"
    Write-Host "   Forward to: keycloak:8080"
    Write-Host "   SSL: Request Let's Encrypt certificate"
    Write-Host ""
    
    Write-Host "2. OpenWebUI Proxy Host:" -ForegroundColor White
    Write-Host "   Domain: openwebui.aether-prod.gc1.myngc.com"
    Write-Host "   Forward to: open-webui:8080"
    Write-Host "   SSL: Request Let's Encrypt certificate"
    Write-Host ""
    
    Write-Host "🔗 Access URLs after NPM configuration:" -ForegroundColor Yellow
    Write-Host "   NPM Admin: http://************:81"
    Write-Host "   Keycloak:  https://keycloak.aether-prod.gc1.myngc.com"
    Write-Host "   OpenWebUI: https://openwebui.aether-prod.gc1.myngc.com"
    Write-Host ""
    
    Write-Host "⚠️  Important Notes:" -ForegroundColor Yellow
    Write-Host "   - Change NPM default credentials immediately"
    Write-Host "   - Ensure DNS records point to ************"
    Write-Host "   - SSL certificates will auto-renew"
    Write-Host "   - Test SSO flow after configuration"
}

# Main execution
Write-Host "🔍 NPM SSO Configuration Test" -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan
Write-Host ""

$testResults = @{}

# Run all tests
$testResults["ServiceConnectivity"] = Test-ServiceConnectivity
$testResults["ExternalAccess"] = Test-ExternalAccess
$testResults["DnsResolution"] = Test-DnsResolution
$testResults["SslCertificates"] = Test-SslCertificates
$testResults["NpmConfiguration"] = Test-NpmConfiguration
$testResults["OAuthConfiguration"] = Test-OAuthConfiguration

Write-Host ""
Write-Host "📊 Test Results Summary" -ForegroundColor Cyan
Write-Host "=======================" -ForegroundColor Cyan

$passedTests = ($testResults.Values | Where-Object { $_ -eq $true }).Count
$totalTests = $testResults.Count

Write-Host "Passed: $passedTests/$totalTests tests" -ForegroundColor Green

foreach ($test in $testResults.GetEnumerator()) {
    $status = if ($test.Value) { "✓ PASS" } else { "✗ FAIL" }
    $color = if ($test.Value) { "Green" } else { "Red" }
    Write-Host "  $($test.Key): $status" -ForegroundColor $color
}

if ($passedTests -eq $totalTests) {
    Write-Host ""
    Write-Host "🎉 All tests passed! Ready for NPM configuration." -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "⚠️  Some tests failed. Address issues before configuring NPM." -ForegroundColor Yellow
}

Show-NpmConfigurationSummary

Write-Host ""
Write-Host "🚀 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Access NPM admin: http://************:81"
Write-Host "2. Follow the NGINX_PROXY_MANAGER_SSO_SETUP.md guide"
Write-Host "3. Configure proxy hosts for Keycloak and OpenWebUI"
Write-Host "4. Request SSL certificates"
Write-Host "5. Test SSO authentication flow"
