<?xml version='1.0' encoding='UTF-8'?>
<!--
  ~ Copyright 2016 Red Hat, Inc. and/or its affiliates
  ~ and other contributors as indicated by the <AUTHOR>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.jboss</groupId>
        <artifactId>jboss-parent</artifactId>
        <version>39</version>
        <relativePath/>
    </parent>

    <groupId>org.keycloak.bom</groupId>
    <artifactId>keycloak-bom-parent</artifactId>
    <version>999.0.0-SNAPSHOT</version>

    <packaging>pom</packaging>

    <name>Keycloak BOM Parent</name>
    <description>Keycloak BOM Parent</description>

    <url>http://keycloak.org</url>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <properties>
        <central.publishing.plugin.version>0.7.0</central.publishing.plugin.version>
        <nexus3.staging.plugin.version>1.0.7</nexus3.staging.plugin.version>
    </properties>

    <distributionManagement>
        <repository>
            <id>keycloak-publish</id>
            <url>https://central.sonatype.com/</url>
        </repository>
        <snapshotRepository>
            <id>keycloak-publish</id>
            <url>https://central.sonatype.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <modules>
        <module>spi</module>
    </modules>
    
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.sonatype.plugins</groupId>
                    <artifactId>nexus-staging-maven-plugin</artifactId>
                    <version>1.6.13</version>
                    <extensions>true</extensions>
                    <configuration>
                        <serverId>${jboss.releases.repo.id}</serverId>
                        <nexusUrl>${jboss.repo.nexusUrl}</nexusUrl>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    
    <profiles>
        <profile>
            <id>gpg</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>3.0.1</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <gpgArguments>
                                        <arg>--batch</arg>
                                        <arg>--pinentry-mode</arg>
                                        <arg>loopback</arg>
                                    </gpgArguments>
                                </configuration>
                            </execution>
                        </executions>
                        <configuration>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>central-staging</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.sonatype.central</groupId>
                        <artifactId>central-publishing-maven-plugin</artifactId>
                        <version>${central.publishing.plugin.version}</version>
                        <extensions>true</extensions>
                        <configuration>
                            <autoPublish>true</autoPublish>
                            <checksums>all</checksums>
                            <deploymentName>keycloak-${project.version}</deploymentName>
                            <publishingServerId>keycloak-publish</publishingServerId>
                            <waitUntil>published</waitUntil>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>nexus3-staging</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nxrm3-maven-plugin</artifactId>
                        <version>${nexus3.staging.plugin.version}</version>
                        <extensions>true</extensions>
                        <configuration>
                            <serverId>${jboss.releases.repo.id}</serverId>
                            <nexusUrl>${jboss.repo.nexusUrl}</nexusUrl>
                            <repository>${jboss.releases.repo.name}</repository>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
