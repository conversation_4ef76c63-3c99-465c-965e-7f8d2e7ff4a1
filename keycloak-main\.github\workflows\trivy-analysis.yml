name: Trivy

on:
  workflow_dispatch:

defaults:
  run:
    shell: bash

permissions:
  contents: read
  
jobs:

  analysis:
    name: Vulnerability scanner for nightly containers
    runs-on: ubuntu-latest
    if: github.repository == 'keycloak/keycloak'
    strategy:
      matrix:
        container: [keycloak, keycloak-operator]
      fail-fast: false
    permissions:
      security-events: write # Required for SARIF uploads
    steps:
      - name: Checkout code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@76071ef0d7ec797419534a183b498b4d6366cf37 # 0.31.0
        with:
          image-ref: quay.io/keycloak/${{ matrix.container }}:nightly
          format: sarif
          output: trivy-results.sarif
          severity: MEDIUM,CRITICAL,HIGH
          ignore-unfixed: true
          version: v0.57.1
          timeout: 15m
        env:
          TRIVY_DB_REPOSITORY: public.ecr.aws/aquasecurity/trivy-db
          TRIVY_JAVA_DB_REPOSITORY: public.ecr.aws/aquasecurity/trivy-java-db

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858 # v3.29.0
        with:
          sarif_file: trivy-results.sarif
          category: ${{ matrix.container }}
