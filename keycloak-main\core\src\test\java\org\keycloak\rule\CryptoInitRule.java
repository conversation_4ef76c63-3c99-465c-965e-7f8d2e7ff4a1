package org.keycloak.rule;

import org.junit.rules.ExternalResource;
import org.keycloak.common.crypto.CryptoIntegration;
import org.keycloak.common.crypto.CryptoProvider;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 */
public class CryptoInitRule extends ExternalResource {

    @Override
    protected void before() throws Throwable {
        CryptoIntegration.init(CryptoProvider.class.getClassLoader());
    }
}
