name: CodeQL

on:
  push:
    branches-ignore:
      - main
      - dependabot/**
      - quarkus-next
  pull_request:
     branches: [main]
  workflow_dispatch:

env:
  MAVEN_ARGS: "-B -nsu -Daether.connector.http.connectionMaxTtl=25"

concurrency:
  # Only cancel jobs for PR updates
  group: codeql-analysis-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash

permissions:
  contents: read

jobs:
  conditional:
    name: Check conditional workflows and jobs
    runs-on: ubuntu-latest
    outputs:
      java: ${{ steps.conditional.outputs.codeql-java }}
      javascript: ${{ steps.conditional.outputs.codeql-javascript }}
      typescript: ${{ steps.conditional.outputs.codeql-typescript }}
    permissions:
      contents: read
      pull-requests: read
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - id: conditional
        uses: ./.github/actions/conditional
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

  java:
    name: CodeQL Java
    needs: conditional
    runs-on: ubuntu-latest
    permissions:
      security-events: write  # Required for SARIF upload
    if: needs.conditional.outputs.java == 'true'
    outputs:
      conclusion: ${{ steps.check.outputs.conclusion }}

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Initialize CodeQL
        uses: github/codeql-action/init@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858 # v3.29.0
        with:
          languages: java

      - name: Build Keycloak
        uses: ./.github/actions/build-keycloak

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858 # v3.29.0
        with:
          wait-for-processing: true
        env:
          CODEQL_ACTION_EXTRA_OPTIONS: '{"database":{"interpret-results":["--max-paths",0]}}'

  javascript:
    name: CodeQL JavaScript
    needs: conditional
    runs-on: ubuntu-latest
    permissions:
      security-events: write  # Required for SARIF upload
    if: needs.conditional.outputs.javascript == 'true'
    outputs:
      conclusion: ${{ steps.check.outputs.conclusion }}

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Initialize CodeQL
        uses: github/codeql-action/init@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858 # v3.29.0
        env:
          CODEQL_ACTION_EXTRA_OPTIONS: '{"database":{"finalize":["--no-run-unnecessary-builds"]}}'
        with:
          languages: javascript

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858 # v3.29.0
        with:
          wait-for-processing: true
        env:
          CODEQL_ACTION_EXTRA_OPTIONS: '{"database":{"interpret-results":["--max-paths",0]}}'

  typescript:
    name: CodeQL TypeScript
    needs: conditional
    runs-on: ubuntu-latest
    permissions:
      security-events: write  # Required for SARIF upload
    if: needs.conditional.outputs.typescript == 'true'
    outputs:
      conclusion: ${{ steps.check.outputs.conclusion }}

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Initialize CodeQL
        uses: github/codeql-action/init@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858 # v3.29.0
        env:
          CODEQL_ACTION_EXTRA_OPTIONS: '{"database":{"finalize":["--no-run-unnecessary-builds"]}}'
        with:
          languages: typescript

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858 # v3.29.0
        with:
          wait-for-processing: true
        env:
          CODEQL_ACTION_EXTRA_OPTIONS: '{"database":{"interpret-results":["--max-paths",0]}}'

  check:
    name: Status Check - CodeQL
    if: always()
    needs:
      - conditional
      - java
      - javascript
      - typescript
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: ./.github/actions/status-check
        with:
          jobs: ${{ toJSON(needs) }}
