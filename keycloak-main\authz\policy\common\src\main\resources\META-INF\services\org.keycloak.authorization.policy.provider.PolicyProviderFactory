#
#  Copyright 2016 Red Hat, Inc. and/or its affiliates
#  and other contributors as indicated by the <AUTHOR>
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
#

#
# JBoss, Home of Professional Open Source.
# Copyright 2016 Red Hat, Inc., and individual contributors
# as indicated by the <AUTHOR>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

org.keycloak.authorization.policy.provider.aggregated.AggregatePolicyProviderFactory
org.keycloak.authorization.policy.provider.js.JSPolicyProviderFactory
org.keycloak.authorization.policy.provider.permission.ResourcePolicyProviderFactory
org.keycloak.authorization.policy.provider.role.RolePolicyProviderFactory
org.keycloak.authorization.policy.provider.permission.ScopePolicyProviderFactory
org.keycloak.authorization.policy.provider.time.TimePolicyProviderFactory
org.keycloak.authorization.policy.provider.user.UserPolicyProviderFactory
org.keycloak.authorization.policy.provider.client.ClientPolicyProviderFactory
org.keycloak.authorization.policy.provider.group.GroupPolicyProviderFactory
org.keycloak.authorization.policy.provider.permission.UMAPolicyProviderFactory
org.keycloak.authorization.policy.provider.clientscope.ClientScopePolicyProviderFactory
org.keycloak.authorization.policy.provider.regex.RegexPolicyProviderFactory