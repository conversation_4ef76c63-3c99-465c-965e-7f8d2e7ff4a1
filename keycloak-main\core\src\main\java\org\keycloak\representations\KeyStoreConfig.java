/*
 * Copyright 2016 Red Hat Inc. and/or its affiliates and other contributors
 * as indicated by the <AUTHOR> All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package org.keycloak.representations;

/**
 * Configuration of KeyStore.
 *
 * <AUTHOR> <EMAIL> (C) 2016 Red Hat Inc.
 */
public class KeyStoreConfig {

    protected Boolean realmCertificate;
    protected String storePassword;
    protected String keyPassword;
    protected String keyAlias;
    protected String realmAlias;
    protected String format;
    protected Integer keySize;
    protected Integer validity;

    public Boolean isRealmCertificate() {
        return realmCertificate;
    }

    public void setRealmCertificate(Boolean realmCertificate) {
        this.realmCertificate = realmCertificate;
    }

    public String getStorePassword() {
        return storePassword;
    }

    public void setStorePassword(String storePassword) {
        this.storePassword = storePassword;
    }

    public String getKeyPassword() {
        return keyPassword;
    }

    public void setKeyPassword(String keyPassword) {
        this.keyPassword = keyPassword;
    }

    public String getKeyAlias() {
        return keyAlias;
    }

    public void setKeyAlias(String keyAlias) {
        this.keyAlias = keyAlias;
    }

    public String getRealmAlias() {
        return realmAlias;
    }

    public void setRealmAlias(String realmAlias) {
        this.realmAlias = realmAlias;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }
    
    public Integer getKeySize() {
        return keySize;
    }
    
    public void setKeySize(Integer keySize) {
        this.keySize = keySize;
    }
    
    public Integer getValidity() {
        return validity;
    }
    
    public void setValidity(Integer validity) {
        this.validity = validity;
    }
}
