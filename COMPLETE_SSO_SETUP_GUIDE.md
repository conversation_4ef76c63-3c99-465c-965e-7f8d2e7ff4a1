# Complete SSO Setup Guide
## NGC Aether OpenWebUI + Keycloak + Nginx Proxy Manager

### 🎯 Overview

This guide provides the complete step-by-step process to configure seamless SSO between OpenWebUI and Keycloak using Nginx Proxy Manager for SSL termination in your NGC Aether environment.

## 📋 Current Configuration Status

✅ **Your configuration is already optimized for SSO:**
- Keycloak configured with `KC_PROXY: edge` for reverse proxy support
- Environment variables set for HTTPS URLs
- Network segmentation implemented
- Security hardening applied

## 🚀 Step-by-Step Implementation

### **Phase 1: Deploy and Validate Services**

#### **Step 1: Deploy the Stack**
```powershell
# 1. Validate configuration
.\security-validation.ps1

# 2. Deploy services
docker-compose up -d

# 3. Wait for services to initialize
Start-Sleep -Seconds 60

# 4. Check service health
docker-compose ps
```

#### **Step 2: Test Internal Connectivity**
```powershell
# Run NPM SSO test
.\test-npm-sso.ps1

# Expected output: All services should be accessible internally
```

### **Phase 2: Configure Nginx Proxy Manager**

#### **Step 3: Access NPM Admin Interface**

1. **Navigate to NPM:**
   ```
   http://************:81
   ```

2. **Initial Login:**
   - Email: `<EMAIL>`
   - Password: `changeme`

3. **⚠️ CRITICAL: Change Default Credentials**
   - Go to Users → Edit admin user
   - Change email to: `<EMAIL>`
   - Set strong password
   - Save changes

#### **Step 4: Configure Keycloak Proxy Host**

1. **Add Proxy Host:**
   - Click "Proxy Hosts" → "Add Proxy Host"

2. **Details Tab:**
   ```
   Domain Names: keycloak.aether-prod.gc1.myngc.com
   Scheme: http
   Forward Hostname/IP: keycloak
   Forward Port: 8080
   Cache Assets: ✓
   Block Common Exploits: ✓
   Websockets Support: ✓
   ```

3. **SSL Tab:**
   ```
   SSL Certificate: Request a new SSL Certificate
   Force SSL: ✓
   HTTP/2 Support: ✓
   HSTS Enabled: ✓
   HSTS Subdomains: ✓
   ```

4. **Advanced Tab - Copy/Paste This Configuration:**
   ```nginx
   # Keycloak proxy configuration for SSO
   proxy_set_header Host $host;
   proxy_set_header X-Real-IP $remote_addr;
   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
   proxy_set_header X-Forwarded-Proto $scheme;
   proxy_set_header X-Forwarded-Host $host;
   proxy_set_header X-Forwarded-Port $server_port;
   
   # Buffer settings for Keycloak responses
   proxy_buffer_size 128k;
   proxy_buffers 4 256k;
   proxy_busy_buffers_size 256k;
   
   # Timeout settings
   proxy_connect_timeout 60s;
   proxy_send_timeout 60s;
   proxy_read_timeout 60s;
   
   # Security headers
   add_header X-Frame-Options "SAMEORIGIN" always;
   add_header X-Content-Type-Options "nosniff" always;
   add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
   ```

5. **Save Configuration**

#### **Step 5: Configure OpenWebUI Proxy Host**

1. **Add Proxy Host:**
   - Click "Proxy Hosts" → "Add Proxy Host"

2. **Details Tab:**
   ```
   Domain Names: openwebui.aether-prod.gc1.myngc.com
   Scheme: http
   Forward Hostname/IP: open-webui
   Forward Port: 8080
   Cache Assets: ✓
   Block Common Exploits: ✓
   Websockets Support: ✓
   ```

3. **SSL Tab:**
   ```
   SSL Certificate: Request a new SSL Certificate
   Force SSL: ✓
   HTTP/2 Support: ✓
   HSTS Enabled: ✓
   HSTS Subdomains: ✓
   ```

4. **Advanced Tab - Copy/Paste This Configuration:**
   ```nginx
   # OpenWebUI proxy configuration for SSO
   proxy_set_header Host $host;
   proxy_set_header X-Real-IP $remote_addr;
   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
   proxy_set_header X-Forwarded-Proto $scheme;
   proxy_set_header X-Forwarded-Host $host;
   proxy_set_header X-Forwarded-Port $server_port;
   
   # OAuth/OIDC headers
   proxy_set_header X-Forwarded-Uri $request_uri;
   proxy_set_header X-Original-URL $scheme://$http_host$request_uri;
   
   # WebSocket support
   proxy_http_version 1.1;
   proxy_set_header Upgrade $http_upgrade;
   proxy_set_header Connection "upgrade";
   
   # Timeout settings for AI processing
   proxy_connect_timeout 60s;
   proxy_send_timeout 300s;
   proxy_read_timeout 300s;
   
   # Disable buffering for real-time responses
   proxy_buffering off;
   proxy_request_buffering off;
   
   # Security headers
   add_header X-Frame-Options "SAMEORIGIN" always;
   add_header X-Content-Type-Options "nosniff" always;
   add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
   ```

5. **Save Configuration**

### **Phase 3: SSL Certificate Setup**

#### **Step 6: Request SSL Certificates**

For each proxy host (Keycloak and OpenWebUI):

1. **Edit Proxy Host → SSL Tab**
2. **Request New Certificate:**
   - Email: `<EMAIL>`
   - Domain: (auto-filled)
   - Accept Terms of Service: ✓
   - Click "Save"

3. **Wait for Certificate Generation:**
   - NPM will handle ACME challenge automatically
   - Certificates will appear in "SSL Certificates" section
   - Auto-renewal is enabled

### **Phase 4: Validation and Testing**

#### **Step 7: Test HTTPS Access**

1. **Test Keycloak:**
   ```
   https://keycloak.aether-prod.gc1.myngc.com
   ```
   - Should show Keycloak welcome page
   - SSL certificate should be valid
   - No security warnings

2. **Test OpenWebUI:**
   ```
   https://openwebui.aether-prod.gc1.myngc.com
   ```
   - Should show OpenWebUI login page
   - Should see "Sign in with Keycloak SSO" button
   - SSL certificate should be valid

#### **Step 8: Test SSO Authentication Flow**

1. **Access OpenWebUI:**
   ```
   https://openwebui.aether-prod.gc1.myngc.com
   ```

2. **Click "Sign in with Keycloak SSO"**
   - Should redirect to: `https://keycloak.aether-prod.gc1.myngc.com/realms/openwebui-realm/protocol/openid-connect/auth?...`

3. **Create Test User in Keycloak:**
   - Access Keycloak admin: `https://keycloak.aether-prod.gc1.myngc.com`
   - Login with admin credentials from .env
   - Go to Users → Add User
   - Create user with strong password
   - Assign role: `openwebui-user`

4. **Complete SSO Flow:**
   - Login with test user
   - Should redirect back to OpenWebUI
   - Should be logged in successfully

## 🔧 Configuration Validation Script

```powershell
# Test complete SSO setup
.\test-npm-sso.ps1 -Detailed

# Expected results:
# ✓ Service Connectivity: PASS
# ✓ External Access: PASS  
# ✓ DNS Resolution: PASS
# ✓ SSL Certificates: PASS
# ✓ NPM Configuration: PASS
# ✓ OAuth Configuration: PASS
```

## 🔍 Troubleshooting Common Issues

### **Issue 1: SSL Certificate Generation Fails**

**Symptoms:** Certificate request fails, HTTP 403 errors
**Solutions:**
1. Verify DNS resolution: `nslookup keycloak.aether-prod.gc1.myngc.com`
2. Ensure ports 80/443 are accessible from internet
3. Check Let's Encrypt rate limits
4. Try manual certificate upload if corporate certs required

### **Issue 2: Keycloak Redirect Loops**

**Symptoms:** Infinite redirects during SSO
**Solutions:**
1. Verify Keycloak proxy configuration:
   ```bash
   KC_PROXY=edge
   KC_HOSTNAME_STRICT=false
   ```
2. Check NPM proxy headers in Advanced tab
3. Verify realm redirect URIs include HTTPS URLs

### **Issue 3: CORS Errors**

**Symptoms:** Cross-origin request blocked
**Solutions:**
1. Add to NPM Advanced configuration:
   ```nginx
   add_header Access-Control-Allow-Origin "https://openwebui.aether-prod.gc1.myngc.com" always;
   ```

### **Issue 4: WebSocket Failures**

**Symptoms:** Real-time features not working
**Solutions:**
1. Ensure WebSocket headers in NPM Advanced tab:
   ```nginx
   proxy_http_version 1.1;
   proxy_set_header Upgrade $http_upgrade;
   proxy_set_header Connection "upgrade";
   ```

## 📋 Final Validation Checklist

- [ ] NPM admin credentials changed
- [ ] Keycloak proxy host configured with SSL
- [ ] OpenWebUI proxy host configured with SSL
- [ ] SSL certificates valid and trusted
- [ ] Keycloak accessible via HTTPS
- [ ] OpenWebUI accessible via HTTPS
- [ ] SSO button appears in OpenWebUI
- [ ] SSO authentication flow works end-to-end
- [ ] User can login and access OpenWebUI features
- [ ] WebSocket connections working (real-time chat)
- [ ] No browser security warnings

## 🎉 Success Criteria

When properly configured, you should have:

1. **Secure Access:**
   - All services accessible via HTTPS
   - Valid SSL certificates
   - No mixed content warnings

2. **Working SSO:**
   - Single sign-on between OpenWebUI and Keycloak
   - Proper user authentication and authorization
   - Role-based access control

3. **Production Ready:**
   - Enterprise-grade security
   - Proper SSL termination
   - Monitoring and logging capabilities

Your NGC Aether OpenWebUI + Keycloak SSO deployment is now complete and production-ready! 🚀
