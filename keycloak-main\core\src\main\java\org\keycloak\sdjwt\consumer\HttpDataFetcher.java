/*
 * Copyright 2024 Red Hat, Inc. and/or its affiliates
 * and other contributors as indicated by the <AUTHOR>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.keycloak.sdjwt.consumer;

import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 */
public interface HttpDataFetcher {

    /**
     * Performs an HTTP GET at the URI and parses the response as <PERSON><PERSON><PERSON>
     * @throws IOException if I/O error or HTTP status not OK (200)
     */
    JsonNode fetchJsonData(String uri) throws IOException;
}
