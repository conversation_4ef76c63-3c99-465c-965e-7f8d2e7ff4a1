# Comprehensive Documentation Extract

This document contains detailed instructions and documentation extracted from nginx and nginx proxy manager websites.

---

## 1. <PERSON><PERSON><PERSON>’s Guide

**Source:** https://nginx.org/en/docs/beginners_guide.html
**Word Count:** 1,848

### Content:

## Beginner’s Guide
This guide gives a basic introduction to nginx and describes some
simple tasks that can be done with it.
It is supposed that nginx is already installed on the reader’s machine.
If it is not, see theInstalling nginxpage.
This guide describes how to start and stop nginx, and reload its
configuration, explains the structure
of the configuration file and describes how to set up nginx
to serve out static content, how to configure nginx as a proxy
server, and how to connect it with a FastCGI application.

nginx has one master process and several worker processes.
The main purpose of the master process is to read and evaluate configuration,
and maintain worker processes.
Worker processes do actual processing of requests.
nginx employs event-based model and OS-dependent mechanisms to efficiently
distribute requests among worker processes.
The number of worker processes is defined in the configuration file and
may be fixed for a given configuration or automatically adjusted to the
number of available CPU cores (seeworker_processes).

The way nginx and its modules work is determined in the configuration file.
By default, the configuration file is namednginx.confand placed in the directory/usr/local/nginx/conf,/etc/nginx, or/usr/local/etc/nginx.

`nginx.conf``/usr/local/nginx/conf``/etc/nginx``/usr/local/etc/nginx`
#### Starting, Stopping, and Reloading Configuration
To start nginx, run the executable file.
Once nginx is started, it can be controlled by invoking the executable
with the-sparameter.
Use the following syntax:

`-s`
> nginx -ssignal

```
nginx -s signal
```
Wheresignalmay be one of the following:

• stop— fast shutdown
`stop`• quit— graceful shutdown
`quit`• reload— reloading the configuration file
`reload`• reopen— reopening the log files
`reopen`For example, to stop nginx processes with waiting for the worker processes
to finish serving current requests, the following command can be executed:

> nginx -s quit

```
nginx -s quit
```

> This command should be executed under the same user that
started nginx.
Changes made in the configuration file
will not be applied until the command to reload configuration is
sent to nginx or it is restarted.
To reload configuration, execute:

> nginx -s reload

```
nginx -s reload
```
Once the master process receives the signal to reload configuration,
it checks the syntax validity
of the new configuration file and tries to apply the configuration provided
in it.
If this is a success, the master process starts new worker processes
and sends messages to old worker processes, requesting them to
shut down.
Otherwise, the master process rolls back the changes and
continues to work with the old configuration.
Old worker processes, receiving a command to shut down,
stop accepting new connections and continue to service current requests until
all such requests are serviced.
After that, the old worker processes exit.

A signal may also be sent to nginx processes with the help of Unix tools
such as thekillutility.
In this case a signal is sent directly to a process with a given process ID.
The process ID of the nginx master process is written, by default, to thenginx.pidin the directory/usr/local/nginx/logsor/var/run.
For example, if the master process ID is 1628, to send the QUIT signal
resulting in nginx’s graceful shutdown, execute:

`kill``nginx.pid``/usr/local/nginx/logs``/var/run`
> kill -s QUIT 1628

```
kill -s QUIT 1628
```
For getting the list of all running nginx processes, thepsutility may be used, for example, in the following way:

`ps`
> ps -ax | grep nginx

```
ps -ax | grep nginx
```
For more information on sending signals to nginx, seeControlling nginx.

#### Configuration File’s Structure
nginx consists of modules which are controlled by directives specified
in the configuration file.
Directives are divided into simple directives and block directives.
A simple directive consists of the name and parameters separated by spaces
and ends with a semicolon (;).
A block directive has the same structure as a simple directive, but
instead of the semicolon it ends with a set of additional instructions
surrounded by braces ({and}).
If a block directive can have other directives inside braces,
it is called a context (examples:events,http,server,
andlocation).

`;``{``}`Directives placed in the configuration file outside
of any contexts are considered to be in themaincontext.
Theeventsandhttpdirectives
reside in themaincontext,serverinhttp, andlocationinserver.

`events``http``main``server``http``location``server`The rest of a line after the#sign is considered a comment.

`#`
#### Serving Static Content
An important web server task is serving out
files (such as images or static HTML pages).
You will implement an example where, depending on the request,
files will be served from different local directories:/data/www(which may contain HTML files) and/data/images(containing images).
This will require editing of the configuration file and setting up of aserverblock inside thehttpblock with twolocationblocks.

`/data/www``/data/images`First, create the/data/wwwdirectory and put anindex.htmlfile with any text content into it and
create the/data/imagesdirectory and place some
images in it.

`/data/www``index.html``/data/images`Next, open the configuration file.
The default configuration file already includes several examples of
theserverblock, mostly commented out.
For now comment out all such blocks and start a newserverblock:

`server``server`
> http {
 server {
 }
}

```
http {
 server {
 }
}
```
Generally, the configuration file may include severalserverblocksdistinguishedby ports on which
theylistento
and byserver names.
Once nginx decides whichserverprocesses a request,
it tests the URI specified in the request’s header against the parameters of thelocationdirectives defined inside theserverblock.

`server``server``location``server`Add the followinglocationblock to theserverblock:

`location``server`
> location / {
 root /data/www;
}

```
location / {
 root /data/www;
}
```
Thislocationblock specifies the
“/” prefix compared with the URI from the request.
For matching requests, the URI will be added to the path specified in therootdirective, that is, to/data/www,
to form the path to the requested file on the local file system.
If there are several matchinglocationblocks nginx
selects the one with the longest prefix.
Thelocationblock above provides the shortest
prefix, of length one,
and so only if all otherlocationblocks fail to provide a match, this block will be used.

`location``/``/data/www``location``location``location`Next, add the secondlocationblock:

`location`
> location /images/ {
 root /data;
}

```
location /images/ {
 root /data;
}
```
It will be a match for requests starting with/images/(location /also matches such requests,
but has shorter prefix).

`/images/``location /`The resulting configuration of theserverblock should
look like this:

`server`
> server {
 location / {
 root /data/www;
 }

 location /images/ {
 root /data;
 }
}

```
server {
 location / {
 root /data/www;
 }

 location /images/ {
 root /data;
 }
}
```
This is already a working configuration of a server that listens
on the standard port 80 and is accessible on the local machine athttp://localhost/.
In response to requests with URIs starting with/images/,
the server will send files from the/data/imagesdirectory.
For example, in response to thehttp://localhost/images/example.pngrequest nginx will
send the/data/images/example.pngfile.
If such file does not exist, nginx will send a response
indicating the 404 error.
Requests with URIs not starting with/images/will be
mapped onto the/data/wwwdirectory.
For example, in response to thehttp://localhost/some/example.htmlrequest nginx will
send the/data/www/some/example.htmlfile.

`http://localhost/``/images/``/data/images``http://localhost/images/example.png``/data/images/example.png``/images/``/data/www``http://localhost/some/example.html``/data/www/some/example.html`To apply the new configuration, start nginx if it is not yet started or
send thereloadsignal to the nginx’s master process,
by executing:

`reload`
> nginx -s reload

```
nginx -s reload
```

> In case something does not work as expected, you may try to find out
the reason inaccess.loganderror.logfiles in the directory/usr/local/nginx/logsor/var/log/nginx.
`access.log``error.log``/usr/local/nginx/logs``/var/log/nginx`
#### Setting Up a Simple Proxy Server
One of the frequent uses of nginx is setting it up as a proxy server, which
means a server that receives requests, passes them to the proxied servers,
retrieves responses from them, and sends them to the clients.

We will configure a basic proxy server, which serves requests of
images with files from the local directory and sends all other requests to a
proxied server.
In this example, both servers will be defined on a single nginx instance.

First, define the proxied server by adding one moreserverblock to the nginx’s configuration file with the following contents:

`server`
> server {
 listen 8080;
 root /data/up1;

 location / {
 }
}

```
server {
 listen 8080;
 root /data/up1;

 location / {
 }
}
```
This will be a simple server that listens on the port 8080
(previously, thelistendirective has not been specified
since the standard port 80 was used) and maps
all requests to the/data/up1directory on the local
file system.
Create this directory and put theindex.htmlfile into it.
Note that therootdirective is placed in theservercontext.
Suchrootdirective is used when thelocationblock selected for serving a request does not
include its ownrootdirective.

`listen``/data/up1``index.html``root``server``root``location``root`Next, use the server configuration from the previous section
and modify it to make it a proxy server configuration.
In the firstlocationblock, put theproxy_passdirective with the protocol, name and port of the proxied server specified
in the parameter (in our case, it ishttp://localhost:8080):

`location``http://localhost:8080`
> server {
 location / {
 proxy_pass http://localhost:8080;
 }

 location /images/ {
 root /data;
 }
}

```
server {
 location / {
 proxy_pass http://localhost:8080;
 }

 location /images/ {
 root /data;
 }
}
```
We will modify the secondlocationblock, which currently maps requests with the/images/prefix to the files under the/data/imagesdirectory,
to make it match the requests of images with typical file extensions.
The modifiedlocationblock looks like this:

`location``/images/``/data/images``location`
> location ~ \.(gif|jpg|png)$ {
 root /data/images;
}

```
location ~ \.(gif|jpg|png)$ {
 root /data/images;
}
```
The parameter is a regular expression matching all URIs ending
with.gif,.jpg, or.png.
A regular expression should be preceded with~.
The corresponding requests will be mapped to the/data/imagesdirectory.

`.gif``.jpg``.png``~``/data/images`When nginx selects alocationblock to serve a request
it first checkslocationdirectives that specify prefixes, rememberinglocationwith the longest prefix, and then checks regular expressions.
If there is a match with a regular expression, nginx picks thislocationor, otherwise, it picks the one remembered earlier.

`location``location``location`The resulting configuration of a proxy server will look like this:

> server {
 location / {
 proxy_pass http://localhost:8080/;
 }

 location ~ \.(gif|jpg|png)$ {
 root /data/images;
 }
}

```
server {
 location / {
 proxy_pass http://localhost:8080/;
 }

 location ~ \.(gif|jpg|png)$ {
 root /data/images;
 }
}
```
This server will filter requests ending with.gif,.jpg, or.pngand map them to the/data/imagesdirectory (by adding URI to therootdirective’s parameter) and pass all other requests
to the proxied server configured above.

`.gif``.jpg``.png``/data/images``root`To apply new configuration, send thereloadsignal to
nginx as described in the previous sections.

`reload`There are manymoredirectives that may be used to further configure a proxy connection.

#### Setting Up FastCGI Proxying
nginx can be used to route requests to FastCGI servers which run
applications built with various frameworks and programming languages
such as PHP.

The most basic nginx configuration to work with a FastCGI server
includes using thefastcgi_passdirective instead of theproxy_passdirective,
andfastcgi_paramdirectives to set parameters passed to a FastCGI server.
Suppose the FastCGI server is accessible onlocalhost:9000.
Taking the proxy configuration from the previous section as a basis,
replace theproxy_passdirective with thefastcgi_passdirective and change the parameter tolocalhost:9000.
In PHP, theSCRIPT_FILENAMEparameter is used for
determining the script name, and theQUERY_STRINGparameter is used to pass request parameters.
The resulting configuration would be:

`proxy_pass``localhost:9000``proxy_pass``fastcgi_pass``localhost:9000``SCRIPT_FILENAME``QUERY_STRING`
> server {
 location / {
 fastcgi_pass localhost:9000;
 fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
 fastcgi_param QUERY_STRING $query_string;
 }

 location ~ \.(gif|jpg|png)$ {
 root /data/images;
 }
}

```
server {
 location / {
 fastcgi_pass localhost:9000;
 fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
 fastcgi_param QUERY_STRING $query_string;
 }

 location ~ \.(gif|jpg|png)$ {
 root /data/images;
 }
}
```
This will set up a server that will route all requests except for
requests for static images to the proxied server operating onlocalhost:9000through the FastCGI protocol.

`localhost:9000`

---

## 2. Installing

**Source:** https://nginx.org/en/docs/install.html
**Word Count:** 107

### Content:

## Installing nginx
nginx can be installed differently, depending on the operating system.

#### Installation on Linux
For Linux, nginxpackagesfrom nginx.org can be used.

#### Installation on FreeBSD
On FreeBSD, nginx can be installed either from thepackagesor through theportssystem.
The ports system provides greater flexibility, allowing selection among
a wide range of options.
The port will compile nginx with the specified options and install it.

#### Building from Sources
If some special functionality is required, not available with packages and
ports, nginx can also be compiled from source files.
While more flexible, this approach may be complex for a beginner.
For more information, seeBuilding nginx from Sources.

---

## 3. Building  from Sources

**Source:** https://nginx.org/en/docs/configure.html
**Word Count:** 85

### Content:

## Building nginx from Sources
The build is configured using theconfigurecommand.
It defines various aspects of the system, including the methods nginx
is allowed to use for connection processing.
At the end it creates aMakefile.

`configure``Makefile`Theconfigurecommand supports the following parameters:

`configure``--help``--prefix=path``path``configure``nginx.conf``/usr/local/nginx``--sbin-path=path``path``prefix/sbin/nginx``prefix``--modules-path=path``path``prefix/modules``prefix``--conf-path=path``path``nginx.conf``-c file``file``prefix/conf/nginx.conf``prefix``--error-log-path=path``path``nginx.conf``prefix/logs/error.log``prefix``--pid-path=path``path``nginx.pid``nginx.conf``prefix/logs/nginx.pid``prefix``--lock-path=path``path``nginx.conf``prefix/logs/nginx.lock``prefix``--user=name``name``nginx.conf``--group=name``name``nginx.conf``--build=name``name``--builddir=path``path``--with-select_module``--without-select_module``select()``--with-poll_module``--without-poll_module``poll()``--with-threads``--with-file-aio``--with-http_ssl_module``--with-http_v2_module``--with-http_v3_module``--with-http_realip_module``--with-http_addition_module``--with-http_xslt_module``--with-http_xslt_module=dynamic``--with-http_image_filter_module``--with-http_image_filter_module=dynamic``--with-http_geoip_module``--with-http_geoip_module=dynamic``--with-http_sub_module``--with-http_dav_module``--with-http_flv_module``--with-http_mp4_module``--with-http_gunzip_module``Content-Encoding: gzip``--with-http_gzip_static_module``.gz``--with-http_auth_request_module``--with-http_random_index_module``/``--with-http_secure_link_module``--with-http_degradation_module``ngx_http_degradation_module``--with-http_slice_module``--with-http_stub_status_module``--without-http_charset_module``--without-http_gzip_module``--without-http_ssi_module``--without-http_userid_module``--without-http_access_module``--without-http_auth_basic_module``--without-http_mirror_module``--without-http_autoindex_module``/``--without-http_geo_module``--without-http_map_module``--without-http_split_clients_module``--without-http_referer_module``--without-http_rewrite_module``--without-http_proxy_module``--without-http_fastcgi_module``--without-http_uwsgi_module``--without-http_scgi_module``--without-http_grpc_module``--without-http_memcached_module``--without-http_limit_conn_module``--without-http_limit_req_module``--without-http_empty_gif_module``--without-http_browser_module``--without-http_upstream_hash_module``--without-http_upstream_ip_hash_module``--without-http_upstream_least_conn_module``--without-http_upstream_random_module``--without-http_upstream_keepalive_module``--without-http_upstream_zone_module``--with-http_perl_module``--with-http_perl_module=dynamic``--with-perl_modules_path=path``path``--with-perl=path``path``--http-log-path=path``path``nginx.conf``prefix/logs/access.log``prefix``--http-client-body-temp-path=path``path``nginx.conf``prefix/client_body_temp``prefix``--http-proxy-temp-path=path``path``nginx.conf``prefix/proxy_temp``prefix``--http-fastcgi-temp-path=path``path``nginx.conf``prefix/fastcgi_temp``prefix``--http-uwsgi-temp-path=path``path``nginx.conf``prefix/uwsgi_temp``prefix``--http-scgi-temp-path=path``path``nginx.conf``prefix/scgi_temp``prefix``--without-http``--without-http-cache``--with-mail``--with-mail=dynamic``--with-mail_ssl_module``--without-mail_pop3_module``--without-mail_imap_module``--without-mail_smtp_module``--with-stream``--with-stream=dynamic``--with-stream_ssl_module``--with-stream_realip_module``--with-stream_geoip_module``--with-stream_geoip_module=dynamic``--with-stream_ssl_preread_module``--without-stream_limit_conn_module``--without-stream_access_module``--without-stream_geo_module``--without-stream_map_module``--without-stream_split_clients_module``--without-stream_return_module``--without-stream_set_module``--without-stream_upstream_hash_module``--without-stream_upstream_least_conn_module``--without-stream_upstream_random_module``--without-stream_upstream_zone_module``--with-google_perftools_module``--with-cpp_test_module``ngx_cpp_test_module``--add-module=path``path``--add-dynamic-module=path``path``--with-compat``--with-cc=path``path``--with-cpp=path``path``--with-cc-opt=parameters``parameters``--with-cc-opt="-I /usr/local/include"``select()``--with-cc-opt="-D FD_SETSIZE=2048"``--with-ld-opt=parameters``parameters``--with-ld-opt="-L /usr/local/lib"``--with-cpu-opt=cpu``cpu``pentium``pentiumpro``pentium3``pentium4``athlon``opteron``sparc32``sparc64``ppc64``--without-pcre``--with-pcre``--with-pcre=path``path``./configure``make``--with-pcre-opt=parameters``parameters``--with-pcre-jit``--without-pcre2``--with-zlib=path``path``./configure``make``--with-zlib-opt=parameters``parameters``--with-zlib-asm=cpu``cpu``pentium``pentiumpro``--with-libatomic``--with-libatomic=path``path``--with-openssl=path``path``--with-openssl-opt=parameters``parameters``--with-debug`Example of parameters usage (all of this needs to be typed in one line):

> ./configure
 --sbin-path=/usr/local/nginx/nginx
 --conf-path=/usr/local/nginx/nginx.conf
 --pid-path=/usr/local/nginx/nginx.pid
 --with-http_ssl_module
 --with-pcre=../pcre2-10.39
 --with-zlib=../zlib-1.3

```
./configure
 --sbin-path=/usr/local/nginx/nginx
 --conf-path=/usr/local/nginx/nginx.conf
 --pid-path=/usr/local/nginx/nginx.pid
 --with-http_ssl_module
 --with-pcre=../pcre2-10.39
 --with-zlib=../zlib-1.3
```
After configuration,
nginx is compiled and installed usingmake.

`make`

---

## 4. Configuring HTTPS servers

**Source:** https://nginx.org/en/docs/http/configuring_https_servers.html
**Word Count:** 1,879

### Content:

## Configuring HTTPS servers
To configure an HTTPS server, thesslparameter
must be enabled onlistening socketsin theserverblock,
and the locations of theserver certificateandprivate keyfiles should be specified:

`ssl`
> server {
 listen 443ssl;
 server_name www.example.com;
 ssl_certificatewww.example.com.crt;
 ssl_certificate_keywww.example.com.key;
 ssl_protocols TLSv1.2 TLSv1.3;
 ssl_ciphers HIGH:!aNULL:!MD5;
 ...
}

```
server {
 listen 443 ssl;
 server_name www.example.com;
 ssl_certificate www.example.com.crt;
 ssl_certificate_key www.example.com.key;
 ssl_protocols TLSv1.2 TLSv1.3;
 ssl_ciphers HIGH:!aNULL:!MD5;
 ...
}
```
The server certificate is a public entity.
It is sent to every client that connects to the server.
The private key is a secure entity and should be stored in a file with
restricted access, however, it must be readable by nginx’s master process.
The private key may alternately be stored in the same file as the certificate:

> ssl_certificate www.example.com.cert;
 ssl_certificate_key www.example.com.cert;

```
ssl_certificate www.example.com.cert;
 ssl_certificate_key www.example.com.cert;
```
in which case the file access rights should also be restricted.
Although the certificate and the key are stored in one file,
only the certificate is sent to a client.

The directivesssl_protocolsandssl_cipherscan be used to limit connections
to include only the strong versions and ciphers of SSL/TLS.
By default nginx uses
“ssl_protocols TLSv1.2 TLSv1.3”
and “ssl_ciphers HIGH:!aNULL:!MD5”,
so configuring them explicitly is generally not needed.
Note that default values of these directives werechangedseveral times.

`ssl_protocols TLSv1.2 TLSv1.3``ssl_ciphers HIGH:!aNULL:!MD5`
#### HTTPS server optimization
SSL operations consume extra CPU resources.
On multi-processor systems severalworker processesshould be run,
no less than the number of available CPU cores.
The most CPU-intensive operation is the SSL handshake.
There are two ways to minimize the number of these operations per client:
the first is by enablingkeepaliveconnections to send several
requests via one connection and the second is to reuse SSL session
parameters to avoid SSL handshakes for parallel and subsequent connections.
The sessions are stored in an SSL session cache shared between workers
and configured by thessl_session_cachedirective.
One megabyte of the cache contains about 4000 sessions.
The default cache timeout is 5 minutes.
It can be increased by using thessl_session_timeoutdirective.
Here is a sample configuration optimized for a multi-core system
with 10 megabyte shared session cache:

> worker_processes auto;

http {ssl_session_cache shared:SSL:10m;ssl_session_timeout 10m;

 server {
 listen 443 ssl;
 server_name www.example.com;keepalive_timeout 70;

 ssl_certificate www.example.com.crt;
 ssl_certificate_key www.example.com.key;
 ssl_protocols TLSv1.2 TLSv1.3;
 ssl_ciphers HIGH:!aNULL:!MD5;
 ...

```
worker_processes auto;

http {
 ssl_session_cache shared:SSL:10m;
 ssl_session_timeout 10m;

 server {
 listen 443 ssl;
 server_name www.example.com;
 keepalive_timeout 70;

 ssl_certificate www.example.com.crt;
 ssl_certificate_key www.example.com.key;
 ssl_protocols TLSv1.2 TLSv1.3;
 ssl_ciphers HIGH:!aNULL:!MD5;
 ...
```

#### SSL certificate chains
Some browsers may complain about a certificate signed by a well-known
certificate authority, while other browsers may accept the certificate
without issues.
This occurs because the issuing authority has signed the server certificate
using an intermediate certificate that is not present in the certificate
base of well-known trusted certificate authorities which is distributed
with a particular browser.
In this case the authority provides a bundle of chained certificates
which should be concatenated to the signed server certificate.
The server certificate must appear before the chained certificates
in the combined file:

> $ cat www.example.com.crt bundle.crt > www.example.com.chained.crt

```
$ cat www.example.com.crt bundle.crt > www.example.com.chained.crt
```
The resulting file should be used in thessl_certificatedirective:

> server {
 listen 443 ssl;
 server_name www.example.com;
 ssl_certificate www.example.com.chained.crt;
 ssl_certificate_key www.example.com.key;
 ...
}

```
server {
 listen 443 ssl;
 server_name www.example.com;
 ssl_certificate www.example.com.chained.crt;
 ssl_certificate_key www.example.com.key;
 ...
}
```
If the server certificate and the bundle have been concatenated in the wrong
order, nginx will fail to start and will display the error message:

> SSL_CTX_use_PrivateKey_file(" ... /www.example.com.key") failed
 (SSL: error:05800074:x509 certificate routines::key values mismatch)

```
SSL_CTX_use_PrivateKey_file(" ... /www.example.com.key") failed
 (SSL: error:05800074:x509 certificate routines::key values mismatch)
```
because nginx has tried to use the private key with the bundle’s
first certificate instead of the server certificate.

Browsers usually store intermediate certificates which they receive
and which are signed by trusted authorities, so actively used browsers
may already have the required intermediate certificates and
may not complain about a certificate sent without a chained bundle.
To ensure the server sends the complete certificate chain,
theopensslcommand-line utility may be used, for example:

`openssl`
> $ openssl s_client -connect www.godaddy.com:443
...
Certificate chain
 0 s:/C=US/ST=Arizona/L=Scottsdale/*******.4.1.311.********=US
 /*******.4.1.311.********=AZ/O=GoDaddy.com, Inc
 /OU=MIS Department/CN=www.GoDaddy.com/serialNumber=0796928-7/********=V1.0, Clause 5.(b)
 i:/C=US/ST=Arizona/L=Scottsdale/O=GoDaddy.com, Inc.
 /OU=http://certificates.godaddy.com/repository
 /CN=Go Daddy Secure Certification Authority
 /serialNumber=07969287
 1 s:/C=US/ST=Arizona/L=Scottsdale/O=GoDaddy.com, Inc.
 /OU=http://certificates.godaddy.com/repository
 /CN=Go Daddy Secure Certification Authority
 /serialNumber=07969287
 i:/C=US/O=The Go Daddy Group, Inc.
 /OU=Go Daddy Class 2 Certification Authority
 2 s:/C=US/O=The Go Daddy Group, Inc.
 /OU=Go Daddy Class 2 Certification Authority
 i:/L=ValiCert Validation Network/O=ValiCert, Inc./OU=ValiCert Class 2 Policy Validation Authority
 /CN=http://www.valicert.com//emailAddress=<EMAIL>
...

```
$ openssl s_client -connect www.godaddy.com:443
...
Certificate chain
 0 s:/C=US/ST=Arizona/L=Scottsdale/*******.4.1.311.********=US
 /*******.4.1.311.********=AZ/O=GoDaddy.com, Inc
 /OU=MIS Department/CN=www.GoDaddy.com
 /serialNumber=0796928-7/********=V1.0, Clause 5.(b)
 i:/C=US/ST=Arizona/L=Scottsdale/O=GoDaddy.com, Inc.
 /OU=http://certificates.godaddy.com/repository
 /CN=Go Daddy Secure Certification Authority
 /serialNumber=07969287
 1 s:/C=US/ST=Arizona/L=Scottsdale/O=GoDaddy.com, Inc.
 /OU=http://certificates.godaddy.com/repository
 /CN=Go Daddy Secure Certification Authority
 /serialNumber=07969287
 i:/C=US/O=The Go Daddy Group, Inc.
 /OU=Go Daddy Class 2 Certification Authority
 2 s:/C=US/O=The Go Daddy Group, Inc.
 /OU=Go Daddy Class 2 Certification Authority
 i:/L=ValiCert Validation Network/O=ValiCert, Inc.
 /OU=ValiCert Class 2 Policy Validation Authority
 /CN=http://www.valicert.com//emailAddress=<EMAIL>
...
```
In this example the subject (“s”) of thewww.GoDaddy.comserver certificate #0 is signed by an issuer
(“i”) which itself is the subject of the certificate #1,
which is signed by an issuer which itself is the subject of the certificate #2,
which signed by the well-known issuerValiCert, Inc.whose certificate is stored in the browsers’ built-in
certificate base (that lay in the house that Jack built).

`www.GoDaddy.com`If a certificate bundle has not been added, only the server certificate #0
will be shown.

#### A single HTTP/HTTPS server
It is possible to configure a single server that handles both HTTP
and HTTPS requests:

> server {
 listen 80;
 listen 443 ssl;
 server_name www.example.com;
 ssl_certificate www.example.com.crt;
 ssl_certificate_key www.example.com.key;
 ...
}

```
server {
 listen 80;
 listen 443 ssl;
 server_name www.example.com;
 ssl_certificate www.example.com.crt;
 ssl_certificate_key www.example.com.key;
 ...
}
```

> Prior to 0.7.14 SSL could not be enabled selectively for
individual listening sockets, as shown above.
SSL could only be enabled for the entire server using thessldirective,
making it impossible to set up a single HTTP/HTTPS server.
Thesslparameter of thelistendirective
was added to solve this issue.
The use of thessldirective
in modern versions is thus discouraged;
it was removed in 1.25.1.
`ssl`
#### Name-based HTTPS servers
A common issue arises when configuring two or more HTTPS servers
listening on a single IP address:

> server {
 listen 443 ssl;
 server_name www.example.com;
 ssl_certificate www.example.com.crt;
 ...
}

server {
 listen 443 ssl;
 server_name www.example.org;
 ssl_certificate www.example.org.crt;
 ...
}

```
server {
 listen 443 ssl;
 server_name www.example.com;
 ssl_certificate www.example.com.crt;
 ...
}

server {
 listen 443 ssl;
 server_name www.example.org;
 ssl_certificate www.example.org.crt;
 ...
}
```
With this configuration a browser receives the default server’s certificate,
i.e.www.example.comregardless of the requested server name.
This is caused by SSL protocol behaviour.
The SSL connection is established before the browser sends an HTTP request
and nginx does not know the name of the requested server.
Therefore, it may only offer the default server’s certificate.

`www.example.com`The oldest and most robust method to resolve the issue
is to assign a separate IP address for every HTTPS server:

> server {
 listen ***********:443 ssl;
 server_name www.example.com;
 ssl_certificate www.example.com.crt;
 ...
}

server {
 listen ***********:443 ssl;
 server_name www.example.org;
 ssl_certificate www.example.org.crt;
 ...
}

```
server {
 listen ***********:443 ssl;
 server_name www.example.com;
 ssl_certificate www.example.com.crt;
 ...
}

server {
 listen ***********:443 ssl;
 server_name www.example.org;
 ssl_certificate www.example.org.crt;
 ...
}
```

#### An SSL certificate with several names
There are other ways that allow sharing a single IP address
between several HTTPS servers.
However, all of them have their drawbacks.
One way is to use a certificate with several names in
the SubjectAltName certificate field, for example,www.example.comandwww.example.org.
However, the SubjectAltName field length is limited.

`www.example.com``www.example.org`Another way is to use a certificate with a wildcard name, for example,*.example.org.
A wildcard certificate secures all subdomains of the specified domain,
but only on one level.
This certificate matcheswww.example.org, but does not matchexample.organdwww.sub.example.org.
These two methods can also be combined.
A certificate may contain exact and wildcard names in the
SubjectAltName field, for example,example.organd*.example.org.

`*.example.org``www.example.org``example.org``www.sub.example.org``example.org``*.example.org`It is better to place a certificate file with several names and
its private key file at thehttplevel of configuration
to inherit their single memory copy in all servers:

> ssl_certificate common.crt;
ssl_certificate_key common.key;

server {
 listen 443 ssl;
 server_name www.example.com;
 ...
}

server {
 listen 443 ssl;
 server_name www.example.org;
 ...
}

```
ssl_certificate common.crt;
ssl_certificate_key common.key;

server {
 listen 443 ssl;
 server_name www.example.com;
 ...
}

server {
 listen 443 ssl;
 server_name www.example.org;
 ...
}
```

#### Server Name Indication
A more generic solution for running several HTTPS servers on a single
IP address isTLS
Server Name Indication extension(SNI, RFC 6066),
which allows a browser to pass a requested server name during the SSL handshake
and, therefore, the server will know which certificate it should use
for the connection.
SNI is currentlysupportedby most modern browsers
and is a mandatory-to-implement extension in TLSv1.3,
though may not be used by some old or special clients.

> Only domain names can be passed in SNI,
however some browsers may erroneously pass an IP address of the server
as its name if a request includes literal IP address.
One should not rely on this.
In order to use SNI in nginx, it must be supported in both the
OpenSSL library with which the nginx binary has been built as well as
the library to which it is being dynamically linked at run time.
OpenSSL supports SNI since 0.9.8f version if it was built with config option“--enable-tlsext”.Since OpenSSL 0.9.8j this option is enabled by default.
If nginx was built with SNI support, then nginx will show this
when run with the “-V” switch:

> $ nginx -V
...
TLS SNI support enabled
...

```
$ nginx -V
...
TLS SNI support enabled
...
```
However, if the SNI-enabled nginx is linked dynamically to
an OpenSSL library without SNI support, nginx displays the warning:

> nginx was built with SNI support, however, now it is linked
dynamically to an OpenSSL library which has no tlsext support,
therefore SNI is not available

```
nginx was built with SNI support, however, now it is linked
dynamically to an OpenSSL library which has no tlsext support,
therefore SNI is not available
```

#### Compatibility
• The SNI support status has been shown by the “-V” switch
since 0.8.21 and 0.7.62.
• Thesslparameter of thelistendirective has been supported since 0.7.14.
Prior to 0.8.21 it could only be specified along with thedefaultparameter.
`ssl``default`• SNI has been supported since 0.5.23.
• The shared SSL session cache has been supported since 0.5.6.
• Version 1.27.3 and later: the default SSL protocols are
TLSv1.2 and TLSv1.3 (if supported by the OpenSSL library).
Otherwise, when OpenSSL 1.0.0 or older is used,
the default SSL protocols are TLSv1 and TLSv1.1.
• Version 1.23.4 and later: the default SSL protocols are TLSv1,
TLSv1.1, TLSv1.2, and TLSv1.3 (if supported by the OpenSSL library).
• Version 1.9.1 and later: the default SSL protocols are TLSv1,
TLSv1.1, and TLSv1.2 (if supported by the OpenSSL library).
• Version 0.7.65, 0.8.19 and later: the default SSL protocols are SSLv3, TLSv1,
TLSv1.1, and TLSv1.2 (if supported by the OpenSSL library).
• Version 0.7.64, 0.8.18 and earlier: the default SSL protocols are SSLv2,
SSLv3, and TLSv1.
• Version 1.0.5 and later: the default SSL ciphers are
“HIGH:!aNULL:!MD5”.
`HIGH:!aNULL:!MD5`• Version 0.7.65, 0.8.20 and later: the default SSL ciphers are
“HIGH:!ADH:!MD5”.
`HIGH:!ADH:!MD5`• Version 0.8.19: the default SSL ciphers are
“ALL:!ADH:RC4+RSA:+HIGH:+MEDIUM”.
`ALL:!ADH:RC4+RSA:+HIGH:+MEDIUM`• Version 0.7.64, 0.8.18 and earlier: the default SSL ciphers are“ALL:!ADH:RC4+RSA:+HIGH:+MEDIUM:+LOW:+SSLv2:+EXP”.
`ALL:!ADH:RC4+RSA:+HIGH:+MEDIUM:+LOW:+SSLv2:+EXP`

---

## 5. Server names

**Source:** https://nginx.org/en/docs/http/server_names.html
**Word Count:** 1,908

### Content:

## Server names
Server names are defined using theserver_namedirective
and determine whichserverblock
is used for a given request.
See also “How nginx processes a request”.
They may be defined using exact names, wildcard names, or regular expressions:

> server {
 listen 80;
 server_name example.org www.example.org;
 ...
}

server {
 listen 80;
 server_name *.example.org;
 ...
}

server {
 listen 80;
 server_name mail.*;
 ...
}

server {
 listen 80;
 server_name ~^(?<user>.+)\.example\.net$;
 ...
}

```
server {
 listen 80;
 server_name example.org www.example.org;
 ...
}

server {
 listen 80;
 server_name *.example.org;
 ...
}

server {
 listen 80;
 server_name mail.*;
 ...
}

server {
 listen 80;
 server_name ~^(?<user>.+)\.example\.net$;
 ...
}
```
When searching for a virtual server by name, if name matches more than one of
the specified variants, e.g. both wildcard name and regular expression match,
the first matching variant will be chosen, in the following order of precedence:

• exact name
• longest wildcard name starting with an asterisk, e.g.
“*.example.org”
`*.example.org`• longest wildcard name ending with an asterisk, e.g. “mail.*”
`mail.*`• first matching regular expression
(in order of appearance in a configuration file)

#### Wildcard names
A wildcard name may contain an asterisk only on the name’s start or end,
and only on a dot border. The names “www.*.example.org”
and “w*.example.org” are invalid.
However, these names can be specified using regular expressions,
for example, “~^www\..+\.example\.org$” and
“~^w.*\.example\.org$”.
An asterisk can match several name parts.
The name “*.example.org” matches not onlywww.example.orgbutwww.sub.example.orgas well.

`www.*.example.org``w*.example.org``~^www\..+\.example\.org$``~^w.*\.example\.org$``*.example.org``www.example.org``www.sub.example.org`A special wildcard name in the form “.example.org” can be
used to match both the exact name “example.org”
and the wildcard name “*.example.org”.

`.example.org``example.org``*.example.org`
#### Regular expressions names
The regular expressions used by nginx are compatible with those used
by the Perl programming language (PCRE).
To use a regular expression, the server name must start with the tilde
character:

> server_name ~^www\d+\.example\.net$;

```
server_name ~^www\d+\.example\.net$;
```
otherwise it will be treated as an exact name, or if the expression contains
an asterisk, as a wildcard name (and most likely as an invalid one).
Do not forget to set “^” and “$” anchors.
They are not required syntactically, but logically.
Also note that domain name dots should be escaped with a backslash.
A regular expression containing the characters “{”
and “}” should be quoted:

`^``$``{``}`
> server_name "~^(?<name>\w\d{1,3}+)\.example\.net$";

```
server_name "~^(?<name>\w\d{1,3}+)\.example\.net$";
```
otherwise nginx will fail to start and display the error message:

> directive "server_name" is not terminated by ";" in ...

```
directive "server_name" is not terminated by ";" in ...
```
A named regular expression capture can be used later as a variable:

> server {
 server_name ~^(www\.)?(?<domain>.+)$;

 location / {
 root /sites/$domain;
 }
}

```
server {
 server_name ~^(www\.)?(?<domain>.+)$;

 location / {
 root /sites/$domain;
 }
}
```
The PCRE library supports named captures using the following syntax:?<name>Perl 5.10 compatible syntax, supported since PCRE-7.0?'name'Perl 5.10 compatible syntax, supported since PCRE-7.0?P<name>Python compatible syntax, supported since PCRE-4.0If nginx fails to start and displays the error message:

> ?<name>Perl 5.10 compatible syntax, supported since PCRE-7.0?'name'Perl 5.10 compatible syntax, supported since PCRE-7.0?P<name>Python compatible syntax, supported since PCRE-4.0
`?<name>``name``?'name'``name``?P<name>``name`
> pcre_compile() failed: unrecognized character after (?< in ...

```
pcre_compile() failed: unrecognized character after (?< in ...
```
this means that the PCRE library is old and the syntax
“?P<name>” should be tried instead.
The captures can also be used in digital form:

`?P<name>``name`
> server {
 server_name ~^(www\.)?(.+)$;

 location / {
 root /sites/$2;
 }
}

```
server {
 server_name ~^(www\.)?(.+)$;

 location / {
 root /sites/$2;
 }
}
```
However, such usage should be limited to simple cases (like the above),
since the digital references can easily be overwritten.

#### Miscellaneous names
There are some server names that are treated specially.

If it is required to process requests without the “Host”
header field in aserverblock which is not the default, an empty name should be specified:

> server {
 listen 80;
 server_name example.org www.example.org "";
 ...
}

```
server {
 listen 80;
 server_name example.org www.example.org "";
 ...
}
```
If noserver_nameis defined in aserverblock
then nginx uses the empty name as the server name.

> nginx versions up to 0.8.48 used the machine’s hostname as the server name
in this case.
If a server name is defined as “$hostname” (0.9.4), the
machine’s hostname is used.

`$hostname`If someone makes a request using an IP address instead of a server name,
the “Host” request header field will contain the IP address
and the request can be handled using the IP address as the server name:

> server {
 listen 80;
 server_name example.org
 www.example.org
 ""***********;
 ...
}

```
server {
 listen 80;
 server_name example.org
 www.example.org
 ""
 ***********
 ;
 ...
}
```
In catch-all server examples the strange name “_” can
be seen:

`_`
> server {
 listen 80 default_server;
 server_name _;
 return 444;
}

```
server {
 listen 80 default_server;
 server_name _;
 return 444;
}
```
There is nothing special about this name, it is just one of a myriad
of invalid domain names which never intersect with any real name.
Other invalid names like “--” and “!@#”
may equally be used.

`--``!@#`nginx versions up to 0.6.25 supported the special name “*”
which was erroneously interpreted to be a catch-all name.
It never functioned as a catch-all or wildcard server name.
Instead, it supplied the functionality that is now provided
by theserver_name_in_redirectdirective.
The special name “*” is now deprecated
and theserver_name_in_redirectdirective should be used.
Note that there is no way to specify the catch-all name or
the default server using theserver_namedirective.
This is a property of thelistendirective
and not of theserver_namedirective.
See also “How nginx processes a request”.
It is possible to define servers listening on ports *:80 and *:8080,
and direct that one will be the default server for port *:8080,
while the other will be the default for port *:80:

`*``*`
> server {
 listen 80;
 listen 8080 default_server;
 server_name example.net;
 ...
}

server {
 listen 80 default_server;
 listen 8080;
 server_name example.org;
 ...
}

```
server {
 listen 80;
 listen 8080 default_server;
 server_name example.net;
 ...
}

server {
 listen 80 default_server;
 listen 8080;
 server_name example.org;
 ...
}
```

#### Internationalized names
Internationalized domain names
(IDNs)
should be specified using an ASCII (Punycode) representation
in theserver_namedirective:

> server {
 listen 80;
 server_name xn--e1afmkfd.xn--80akhbyknj4f; # пример.испытание
 ...
}

```
server {
 listen 80;
 server_name xn--e1afmkfd.xn--80akhbyknj4f; # пример.испытание
 ...
}
```

#### Virtual server selection
First, a connection is created in a default server context.
Then, the server name can be determined
in the following request processing stages,
each involved in server configuration selection:

• during SSL handshake, in advance, according toSNI
during SSL handshake, in advance, according toSNI

• after processing the request line
after processing the request line

• after processing theHostheader field
after processing theHostheader field

`Host`• if the server name was not determined after processing the request line or
from theHostheader field,
nginx will use the empty name as the server name.
if the server name was not determined after processing the request line or
from theHostheader field,
nginx will use the empty name as the server name.

`Host`At each of these stages, different server configurations can be applied.
As such, certain directives should be specified with caution:

• in case of thessl_protocolsdirective,
the protocol list is set by the OpenSSL library before
the server configuration could be applied according to the name
requested through SNI,
thus, protocols should be specified only for a default server;
• theclient_header_buffer_sizeandmerge_slashesdirectives
are involved before reading the request line,
thus, such directives use a default server configuration or
the server configuration chosen by SNI;
• in case of theignore_invalid_headers,large_client_header_buffers,
andunderscores_in_headersdirectives
involved in processing request header fields,
it additionally depends
whether the server configuration was updated
according to the request line or theHostheader field;
`Host`• an error response will be handled with
theerror_pagedirective
in the server that currently fulfills the request.

#### Optimization
Exact names, wildcard names starting with an asterisk,
and wildcard names ending with an asterisk are stored
in three hash tables bound to the listen ports.
The sizes of hash tables are optimized at the configuration phase
so that a name can be found with the fewest CPU cache misses.
The details of setting up hash tables are provided in a separatedocument.

The exact names hash table is searched first.
If a name is not found, the hash table with wildcard names
starting with an asterisk is searched.
If the name is not found there, the hash table with wildcard names
ending with an asterisk is searched.

Searching wildcard names hash table is slower than searching exact names hash
table because names are searched by domain parts.
Note that the special wildcard form “.example.org”
is stored in a wildcard names hash table and not in an exact names hash table.

`.example.org`Regular expressions are tested sequentially
and therefore are the slowest method and are non-scalable.

For these reasons, it is better to use exact names where possible.
For example, if the most frequently requested names of a server
areexample.organdwww.example.org,
it is more efficient to define them explicitly:

`example.org``www.example.org`
> server {
 listen 80;
 server_name example.org www.example.org *.example.org;
 ...
}

```
server {
 listen 80;
 server_name example.org www.example.org *.example.org;
 ...
}
```
than to use the simplified form:

> server {
 listen 80;
 server_name .example.org;
 ...
}

```
server {
 listen 80;
 server_name .example.org;
 ...
}
```
If a large number of server names are defined,
or unusually long server names are defined, tuning
theserver_names_hash_max_sizeandserver_names_hash_bucket_sizedirectives at thehttplevel may become necessary.
The default value of theserver_names_hash_bucket_sizedirective may be equal to 32, or 64, or another value,
depending on CPU cache line size.
If the default value is 32 and server name is defined as
“too.long.server.name.example.org”
then nginx will fail to start and display the error message:

`too.long.server.name.example.org`
> could not build the server_names_hash,
you should increase server_names_hash_bucket_size: 32

```
could not build the server_names_hash,
you should increase server_names_hash_bucket_size: 32
```
In this case, the directive value should be increased to the next power of two:

> http {
 server_names_hash_bucket_size 64;
 ...

```
http {
 server_names_hash_bucket_size 64;
 ...
```
If a large number of server names are defined,
another error message will appear:

> could not build the server_names_hash,
you should increase either server_names_hash_max_size: 512
or server_names_hash_bucket_size: 32

```
could not build the server_names_hash,
you should increase either server_names_hash_max_size: 512
or server_names_hash_bucket_size: 32
```
In such a case, first try to setserver_names_hash_max_sizeto a number close to the number of server names.
Only if this does not help,
or if nginx’s start time is unacceptably long, try to increaseserver_names_hash_bucket_size.

If a server is the only server for a listen port, then nginx will not test
server names at all (and will not build the hash tables for the listen port).
However, there is one exception.
If a server name is a regular expression with captures,
then nginx has to execute the expression to get the captures.

#### Compatibility
• The special server name “$hostname” has been supported
since 0.9.4.
`$hostname`• A default server name value is an empty name “” since 0.8.48.
• Named regular expression server name captures have been supported since 0.8.25.
• Regular expression server name captures have been supported since 0.7.40.
• An empty server name “” has been supported since 0.7.12.
• A wildcard server name or regular expression has been supported for use
as the first server name since 0.6.25.
• Regular expression server names have been supported since 0.6.7.
• Wildcard formexample.*has been supported since 0.6.0.
`example.*`• The special form.example.orghas been supported since 0.3.18.
`.example.org`• Wildcard form*.example.orghas been supported since 0.1.13.
`*.example.org`

---

## 6. How  processes a request

**Source:** https://nginx.org/en/docs/http/request_processing.html
**Word Count:** 1,104

### Content:

## How nginx processes a request

#### Name-based virtual servers
nginx first decides whichservershould process the request.
Let’s start with a simple configuration
where all three virtual servers listen on port *:80:

> server {
 listen 80;
 server_name example.org www.example.org;
 ...
}

server {
 listen 80;
 server_name example.net www.example.net;
 ...
}

server {
 listen 80;
 server_name example.com www.example.com;
 ...
}

```
server {
 listen 80;
 server_name example.org www.example.org;
 ...
}

server {
 listen 80;
 server_name example.net www.example.net;
 ...
}

server {
 listen 80;
 server_name example.com www.example.com;
 ...
}
```
In this configuration nginx tests only the request’s header field
“Host” to determine which server the request should be routed to.
If its value does not match any server name,
or the request does not contain this header field at all,
then nginx will route the request to the default server for this port.
In the configuration above, the default server is the first
one — which is nginx’s standard default behaviour.
It can also be set explicitly which server should be default,
with thedefault_serverparameter
in thelistendirective:

`default_server`
> server {
 listen 80default_server;
 server_name example.net www.example.net;
 ...
}

```
server {
 listen 80 default_server;
 server_name example.net www.example.net;
 ...
}
```

> Thedefault_serverparameter has been available since
version 0.8.21.
In earlier versions thedefaultparameter should be used
instead.
`default_server``default`Note that the default server is a property of the listen port
and not of the server name.
More about this later.

#### How to prevent processing requests with undefined server names
If requests without the “Host” header field should not be
allowed, a server that just drops the requests can be defined:

> server {
 listen 80;
 server_name "";
 return 444;
}

```
server {
 listen 80;
 server_name "";
 return 444;
}
```
Here, the server name is set to an empty string that will match
requests without the “Host” header field,
and a special nginx’s non-standard code 444
is returned that closes the connection.

> Since version 0.8.48, this is the default setting for the
server name, so theserver_name ""can be omitted.
In earlier versions, the machine’shostnamewas used as
a default server name.
`server_name ""`
#### Mixed name-based and IP-based virtual servers
Let’s look at a more complex configuration
where some virtual servers listen on different addresses:

> server {
 listen ***********:80;
 server_name example.org www.example.org;
 ...
}

server {
 listen ***********:80;
 server_name example.net www.example.net;
 ...
}

server {
 listen ***********:80;
 server_name example.com www.example.com;
 ...
}

```
server {
 listen ***********:80;
 server_name example.org www.example.org;
 ...
}

server {
 listen ***********:80;
 server_name example.net www.example.net;
 ...
}

server {
 listen ***********:80;
 server_name example.com www.example.com;
 ...
}
```
In this configuration, nginx first tests the IP address and port
of the request against thelistendirectives
of theserverblocks.
It then tests the “Host”
header field of the request against theserver_nameentries of theserverblocks that matched
the IP address and port.
If the server name is not found, the request will be processed by
the default server.
For example, a request forwww.example.comreceived on
the ***********:80 port will be handled by the default server
of the ***********:80 port, i.e., by the first server,
since there is nowww.example.comdefined for this port.

`www.example.com``www.example.com`As already stated, a default server is a property of the listen port,
and different default servers may be defined for different ports:

> server {
 listen ***********:80;
 server_name example.org www.example.org;
 ...
}

server {
 listen ***********:80default_server;
 server_name example.net www.example.net;
 ...
}

server {
 listen ***********:80default_server;
 server_name example.com www.example.com;
 ...
}

```
server {
 listen ***********:80;
 server_name example.org www.example.org;
 ...
}

server {
 listen ***********:80 default_server;
 server_name example.net www.example.net;
 ...
}

server {
 listen ***********:80 default_server;
 server_name example.com www.example.com;
 ...
}
```

#### A simple PHP site configuration
Now let’s look at how nginx chooses alocationto process a request
for a typical, simple PHP site:

> server {
 listen 80;
 server_name example.org www.example.org;
 root /data/www;

 location / {
 index index.html index.php;
 }

 location ~* \.(gif|jpg|png)$ {
 expires 30d;
 }

 location ~ \.php$ {
 fastcgi_pass localhost:9000;
 fastcgi_param SCRIPT_FILENAME
 $document_root$fastcgi_script_name;
 include fastcgi_params;
 }
}

```
server {
 listen 80;
 server_name example.org www.example.org;
 root /data/www;

 location / {
 index index.html index.php;
 }

 location ~* \.(gif|jpg|png)$ {
 expires 30d;
 }

 location ~ \.php$ {
 fastcgi_pass localhost:9000;
 fastcgi_param SCRIPT_FILENAME
 $document_root$fastcgi_script_name;
 include fastcgi_params;
 }
}
```
nginx first searches for the most specific prefix location given by
literal strings regardless of the listed order.
In the configuration above
the only prefix location is “/” and since it matches
any request it will be used as a last resort.
Then nginx checks locations given by
regular expression in the order listed in the configuration file.
The first matching expression stops the search and nginx will use this
location.
If no regular expression matches a request, then nginx uses
the most specific prefix location found earlier.

`/`Note that locations of all types test only a URI part of request line
without arguments.
This is done because arguments in the query string may be given in
several ways, for example:

> /index.php?user=john&page=1
/index.php?page=1&user=john

```
/index.php?user=john&page=1
/index.php?page=1&user=john
```
Besides, anyone may request anything in the query string:

> /index.php?page=1&something+else&user=john

```
/index.php?page=1&something+else&user=john
```
Now let’s look at how requests would be processed
in the configuration above:

• A request “/logo.gif” is matched by the prefix location
“/” first and then by the regular expression
“\.(gif|jpg|png)$”,
therefore, it is handled by the latter location.
Using the directive “root /data/www” the request
is mapped to the file/data/www/logo.gif, and the file
is sent to the client.
`/logo.gif``/``\.(gif|jpg|png)$``root /data/www``/data/www/logo.gif`• A request “/index.php” is also matched by the prefix location
“/” first and then by the regular expression
“\.(php)$”.
Therefore, it is handled by the latter location
and the request is passed to a FastCGI server listening on localhost:9000.
Thefastcgi_paramdirective sets the FastCGI parameterSCRIPT_FILENAMEto “/data/www/index.php”,
and the FastCGI server executes the file.
The variable$document_rootis equal to
the value of therootdirective and the variable$fastcgi_script_nameis equal to
the request URI, i.e. “/index.php”.
`/index.php``/``\.(php)$``SCRIPT_FILENAME``/data/www/index.php``$document_root``$fastcgi_script_name``/index.php`• A request “/about.html” is matched by the prefix location
“/” only, therefore, it is handled in this location.
Using the directive “root /data/www” the request is mapped
to the file/data/www/about.html, and the file is sent
to the client.
`/about.html``/``root /data/www``/data/www/about.html`• Handling a request “/” is more complex.
It is matched by the prefix location “/” only,
therefore, it is handled by this location.
Then theindexdirective tests for the existence
of index files according to its parameters and
the “root /data/www” directive.
If the file/data/www/index.htmldoes not exist,
and the file/data/www/index.phpexists,
then the directive does an internal redirect to “/index.php”,
and nginx searches the locations again
as if the request had been sent by a client.
As we saw before, the redirected request will eventually be handled
by the FastCGI server.
`/``/``root /data/www``/data/www/index.html``/data/www/index.php``/index.php`

---

## 7. Using  as HTTP load balancer

**Source:** https://nginx.org/en/docs/http/load_balancing.html
**Word Count:** 940

### Content:

## Using nginx as HTTP load balancer

#### Introduction
Load balancing across multiple application instances is a commonly used
technique for optimizing resource utilization, maximizing throughput,
reducing latency, and ensuring fault-tolerant configurations.

It is possible to use nginx as a very efficient HTTP load balancer to
distribute traffic to several application servers and to improve
performance, scalability and reliability of web applications with nginx.

#### Load balancing methods
The following load balancing mechanisms (or methods) are supported in
nginx:

• round-robin — requests to the application servers are distributed
in a round-robin fashion,
• least-connected — next request is assigned to the server with the
least number of active connections,
• ip-hash — a hash-function is used to determine what server should
be selected for the next request (based on the client’s IP address).

#### Default load balancing configuration
The simplest configuration for load balancing with nginx may look
like the following:

> http {
 upstream myapp1 {
 server srv1.example.com;
 server srv2.example.com;
 server srv3.example.com;
 }

 server {
 listen 80;

 location / {
 proxy_pass http://myapp1;
 }
 }
}

```
http {
 upstream myapp1 {
 server srv1.example.com;
 server srv2.example.com;
 server srv3.example.com;
 }

 server {
 listen 80;

 location / {
 proxy_pass http://myapp1;
 }
 }
}
```
In the example above, there are 3 instances of the same application
running on srv1-srv3.
When the load balancing method is not specifically configured,
it defaults to round-robin.
All requests areproxiedto the server group myapp1, and nginx applies HTTP load
balancing to distribute the requests.

Reverse proxy implementation in nginx includes load balancing for HTTP,
HTTPS, FastCGI, uwsgi, SCGI, memcached, and gRPC.

To configure load balancing for HTTPS instead of HTTP, just use “https”
as the protocol.

When setting up load balancing for FastCGI, uwsgi, SCGI, memcached, or gRPC, usefastcgi_pass,uwsgi_pass,scgi_pass,memcached_pass, andgrpc_passdirectives respectively.

#### Least connected load balancing
Another load balancing discipline is least-connected.
Least-connected allows controlling the load on application
instances more fairly in a situation when some of the requests
take longer to complete.

With the least-connected load balancing, nginx will try not to overload a
busy application server with excessive requests, distributing the new
requests to a less busy server instead.

Least-connected load balancing in nginx is activated when theleast_conndirective is used as part of the server group configuration:

> upstream myapp1 {
 least_conn;
 server srv1.example.com;
 server srv2.example.com;
 server srv3.example.com;
 }

```
upstream myapp1 {
 least_conn;
 server srv1.example.com;
 server srv2.example.com;
 server srv3.example.com;
 }
```

#### Session persistence
Please note that with round-robin or least-connected load
balancing, each subsequent client’s request can be potentially
distributed to a different server.
There is no guarantee that the same client will be always
directed to the same server.

If there is the need to tie a client to a particular application server —
in other words, make the client’s session “sticky” or “persistent” in
terms of always trying to select a particular server — the ip-hash load
balancing mechanism can be used.

With ip-hash, the client’s IP address is used as a hashing key to
determine what server in a server group should be selected for the
client’s requests.
This method ensures that the requests from the same client
will always be directed to the same server
except when this server is unavailable.

To configure ip-hash load balancing, just add theip_hashdirective to the server (upstream) group configuration:

> upstream myapp1 {
 ip_hash;
 server srv1.example.com;
 server srv2.example.com;
 server srv3.example.com;
}

```
upstream myapp1 {
 ip_hash;
 server srv1.example.com;
 server srv2.example.com;
 server srv3.example.com;
}
```

#### Weighted load balancing
It is also possible to influence nginx load balancing algorithms even
further by using server weights.

In the examples above, the server weights are not configured which means
that all specified servers are treated as equally qualified for a
particular load balancing method.

With the round-robin in particular it also means a more or less equal
distribution of requests across the servers — provided there are enough
requests, and when the requests are processed in a uniform manner and
completed fast enough.

When theweightparameter is specified for a server, the weight is accounted as part
of the load balancing decision.

> upstream myapp1 {
 server srv1.example.com weight=3;
 server srv2.example.com;
 server srv3.example.com;
 }

```
upstream myapp1 {
 server srv1.example.com weight=3;
 server srv2.example.com;
 server srv3.example.com;
 }
```
With this configuration, every 5 new requests will be distributed across
the application instances as the following: 3 requests will be directed
to srv1, one request will go to srv2, and another one — to srv3.

It is similarly possible to use weights with the least-connected and
ip-hash load balancing in the recent versions of nginx.

#### Health checks
Reverse proxy implementation in nginx includes in-band (or passive)
server health checks.
If the response from a particular server fails with an error,
nginx will mark this server as failed, and will try to
avoid selecting this server for subsequent inbound requests for a while.

Themax_failsdirective sets the number of consecutive unsuccessful attempts to
communicate with the server that should happen duringfail_timeout.
By default,max_failsis set to 1.
When it is set to 0, health checks are disabled for this server.
Thefail_timeoutparameter also defines how long the server will be marked as failed.
Afterfail_timeoutinterval following the server failure, nginx will start to gracefully
probe the server with the live client’s requests.
If the probes have been successful, the server is marked as a live one.

#### Further reading
In addition, there are more directives and parameters that control server
load balancing in nginx, e.g.proxy_next_upstream,backup,down, andkeepalive.
For more information please check ourreference documentation.

Last but not least,
application load balancing,
application health checks,
activity monitoring and
on-the-fly reconfiguration of server groups are available
as part of our paid NGINX Plus subscriptions.

---

## 8. Module ngx_http_proxy_module

**Source:** https://nginx.org/en/docs/http/ngx_http_proxy_module.html
**Word Count:** 6,572

### Content:

## Module ngx_http_proxy_module
Thengx_http_proxy_modulemodule allows passing
requests to another server.

`ngx_http_proxy_module`
#### Example Configuration

> location / {
 proxy_pass http://localhost:8000;
 proxy_set_header Host $host;
 proxy_set_header X-Real-IP $remote_addr;
}

```
location / {
 proxy_pass http://localhost:8000;
 proxy_set_header Host $host;
 proxy_set_header X-Real-IP $remote_addr;
}
```

#### Directives
Syntax:proxy_bindaddress[transparent] |off;Default:—Context:http,server,locationThis directive appeared in version 0.8.22.

`proxy_bind
address
 [transparent] |
 off;``address``transparent``off``http``server``location`This directive appeared in version 0.8.22.

Makes outgoing connections to a proxied server originate
from the specified local IP address with an optional port (1.11.2).
Parameter value can contain variables (1.3.12).
The special valueoff(1.3.12) cancels the effect
of theproxy_binddirective
inherited from the previous configuration level, which allows the
system to auto-assign the local IP address and port.

`off``proxy_bind`Thetransparentparameter (1.11.0) allows
outgoing connections to a proxied server originate
from a non-local IP address,
for example, from a real IP address of a client:

`transparent`
> proxy_bind $remote_addr transparent;

```
proxy_bind $remote_addr transparent;
```
In order for this parameter to work,
it is usually necessary to run nginx worker processes with thesuperuserprivileges.
On Linux it is not required (1.13.8) as if
thetransparentparameter is specified, worker processes
inherit theCAP_NET_RAWcapability from the master process.
It is also necessary to configure kernel routing table
to intercept network traffic from the proxied server.

`transparent``CAP_NET_RAW`Syntax:proxy_buffer_sizesize;Default:proxy_buffer_size 4k|8k;Context:http,server,location

`proxy_buffer_size size;``size`
```
proxy_buffer_size 4k|8k;
```
`http``server``location`Sets thesizeof the buffer used for reading the first part
of the response received from the proxied server.
This part usually contains a small response header.
By default, the buffer size is equal to one memory page.
This is either 4K or 8K, depending on a platform.
It can be made smaller, however.

`size`Syntax:proxy_bufferingon|off;Default:proxy_buffering on;Context:http,server,location

`proxy_buffering on | off;``on``off`
```
proxy_buffering on;
```
`http``server``location`Enables or disables buffering of responses from the proxied server.

When buffering is enabled, nginx receives a response from the proxied server
as soon as possible, saving it into the buffers set by theproxy_buffer_sizeandproxy_buffersdirectives.
If the whole response does not fit into memory, a part of it can be saved
to atemporary fileon the disk.
Writing to temporary files is controlled by theproxy_max_temp_file_sizeandproxy_temp_file_write_sizedirectives.

When buffering is disabled, the response is passed to a client synchronously,
immediately as it is received.
nginx will not try to read the whole response from the proxied server.
The maximum size of the data that nginx can receive from the server
at a time is set by theproxy_buffer_sizedirective.

Buffering can also be enabled or disabled by passing
“yes” or “no” in the
“X-Accel-Buffering” response header field.
This capability can be disabled using theproxy_ignore_headersdirective.

`yes``no`Syntax:proxy_buffersnumbersize;Default:proxy_buffers 8 4k|8k;Context:http,server,location

`proxy_buffers number size;``number``size`
```
proxy_buffers 8 4k|8k;
```
`http``server``location`Sets thenumberandsizeof the
buffers used for reading a response from the proxied server,
for a single connection.
By default, the buffer size is equal to one memory page.
This is either 4K or 8K, depending on a platform.

`number``size`Syntax:proxy_busy_buffers_sizesize;Default:proxy_busy_buffers_size 8k|16k;Context:http,server,location

`proxy_busy_buffers_size size;``size`
```
proxy_busy_buffers_size 8k|16k;
```
`http``server``location`Whenbufferingof responses from the proxied
server is enabled, limits the totalsizeof buffers that
can be busy sending a response to the client while the response is not
yet fully read.
In the meantime, the rest of the buffers can be used for reading the response
and, if needed, buffering part of the response to a temporary file.
By default,sizeis limited by the size of two buffers set by theproxy_buffer_sizeandproxy_buffersdirectives.

`size``size`Syntax:proxy_cachezone|off;Default:proxy_cache off;Context:http,server,location

`proxy_cache zone | off;``zone``off`
```
proxy_cache off;
```
`http``server``location`Defines a shared memory zone used for caching.
The same zone can be used in several places.
Parameter value can contain variables (1.7.9).
Theoffparameter disables caching inherited
from the previous configuration level.

`off`Syntax:proxy_cache_background_updateon|off;Default:proxy_cache_background_update off;Context:http,server,locationThis directive appeared in version 1.11.10.

`proxy_cache_background_update on | off;``on``off`
```
proxy_cache_background_update off;
```
`http``server``location`This directive appeared in version 1.11.10.

Allows starting a background subrequest
to update an expired cache item,
while a stale cached response is returned to the client.
Note that it is necessary toallowthe usage of a stale cached response when it is being updated.

Syntax:proxy_cache_bypassstring...;Default:—Context:http,server,location

`proxy_cache_bypass string ...;``string``http``server``location`Defines conditions under which the response will not be taken from a cache.
If at least one value of the string parameters is not empty and is not
equal to “0” then the response will not be taken from the cache:

> proxy_cache_bypass $cookie_nocache $arg_nocache$arg_comment;
proxy_cache_bypass $http_pragma $http_authorization;

```
proxy_cache_bypass $cookie_nocache $arg_nocache$arg_comment;
proxy_cache_bypass $http_pragma $http_authorization;
```
Can be used along with theproxy_no_cachedirective.

Syntax:proxy_cache_convert_headon|off;Default:proxy_cache_convert_head on;Context:http,server,locationThis directive appeared in version 1.9.7.

`proxy_cache_convert_head on | off;``on``off`
```
proxy_cache_convert_head on;
```
`http``server``location`This directive appeared in version 1.9.7.

Enables or disables the conversion of the “HEAD” method
to “GET” for caching.
When the conversion is disabled, thecache keyshould be configured
to include the$request_method.

`HEAD``GET``$request_method`Syntax:proxy_cache_keystring;Default:proxy_cache_key $scheme$proxy_host$request_uri;Context:http,server,location

`proxy_cache_key string;``string`
```
proxy_cache_key $scheme$proxy_host$request_uri;
```
`http``server``location`Defines a key for caching, for example

> proxy_cache_key "$host$request_uri $cookie_user";

```
proxy_cache_key "$host$request_uri $cookie_user";
```
By default, the directive’s value is close to the string

> proxy_cache_key $scheme$proxy_host$uri$is_args$args;

```
proxy_cache_key $scheme$proxy_host$uri$is_args$args;
```
Syntax:proxy_cache_lockon|off;Default:proxy_cache_lock off;Context:http,server,locationThis directive appeared in version 1.1.12.

`proxy_cache_lock on | off;``on``off`
```
proxy_cache_lock off;
```
`http``server``location`This directive appeared in version 1.1.12.

When enabled, only one request at a time will be allowed to populate
a new cache element identified according to theproxy_cache_keydirective by passing a request to a proxied server.
Other requests of the same cache element will either wait
for a response to appear in the cache or the cache lock for
this element to be released, up to the time set by theproxy_cache_lock_timeoutdirective.

Syntax:proxy_cache_lock_agetime;Default:proxy_cache_lock_age 5s;Context:http,server,locationThis directive appeared in version 1.7.8.

`proxy_cache_lock_age time;``time`
```
proxy_cache_lock_age 5s;
```
`http``server``location`This directive appeared in version 1.7.8.

If the last request passed to the proxied server
for populating a new cache element
has not completed for the specifiedtime,
one more request may be passed to the proxied server.

`time`Syntax:proxy_cache_lock_timeouttime;Default:proxy_cache_lock_timeout 5s;Context:http,server,locationThis directive appeared in version 1.1.12.

`proxy_cache_lock_timeout time;``time`
```
proxy_cache_lock_timeout 5s;
```
`http``server``location`This directive appeared in version 1.1.12.

Sets a timeout forproxy_cache_lock.
When thetimeexpires,
the request will be passed to the proxied server,
however, the response will not be cached.

`time`
> Before 1.7.8, the response could be cached.
Syntax:proxy_cache_max_range_offsetnumber;Default:—Context:http,server,locationThis directive appeared in version 1.11.6.

`proxy_cache_max_range_offset number;``number``http``server``location`This directive appeared in version 1.11.6.

Sets an offset in bytes for byte-range requests.
If the range is beyond the offset,
the range request will be passed to the proxied server
and the response will not be cached.

Syntax:proxy_cache_methodsGET|HEAD|POST...;Default:proxy_cache_methods GET HEAD;Context:http,server,locationThis directive appeared in version 0.7.59.

`proxy_cache_methods
GET |
 HEAD |
 POST
 ...;``GET``HEAD``POST`
```
proxy_cache_methods GET HEAD;
```
`http``server``location`This directive appeared in version 0.7.59.

If the client request method is listed in this directive then
the response will be cached.
“GET” and “HEAD” methods are always
added to the list, though it is recommended to specify them explicitly.
See also theproxy_no_cachedirective.

`GET``HEAD`Syntax:proxy_cache_min_usesnumber;Default:proxy_cache_min_uses 1;Context:http,server,location

`proxy_cache_min_uses number;``number`
```
proxy_cache_min_uses 1;
```
`http``server``location`Sets thenumberof requests after which the response
will be cached.

`number`Syntax:proxy_cache_pathpath[levels=levels]
 [use_temp_path=on|off]keys_zone=name:size[inactive=time]
 [max_size=size]
 [min_free=size]
 [manager_files=number]
 [manager_sleep=time]
 [manager_threshold=time]
 [loader_files=number]
 [loader_sleep=time]
 [loader_threshold=time]
 [purger=on|off]
 [purger_files=number]
 [purger_sleep=time]
 [purger_threshold=time];Default:—Context:http

`proxy_cache_path
path
 [levels=levels]
 [use_temp_path=on|off]
 keys_zone=name:size
 [inactive=time]
 [max_size=size]
 [min_free=size]
 [manager_files=number]
 [manager_sleep=time]
 [manager_threshold=time]
 [loader_files=number]
 [loader_sleep=time]
 [loader_threshold=time]
 [purger=on|off]
 [purger_files=number]
 [purger_sleep=time]
 [purger_threshold=time];``path``levels``levels``use_temp_path``on``off``keys_zone``name``size``inactive``time``max_size``size``min_free``size``manager_files``number``manager_sleep``time``manager_threshold``time``loader_files``number``loader_sleep``time``loader_threshold``time``purger``on``off``purger_files``number``purger_sleep``time``purger_threshold``time``http`Sets the path and other parameters of a cache.
Cache data are stored in files.
The file name in a cache is a result of
applying the MD5 function to thecache key.
Thelevelsparameter defines hierarchy levels of a cache:
from 1 to 3, each level accepts values 1 or 2.
For example, in the following configuration

`levels`
> proxy_cache_path /data/nginx/cache levels=1:2 keys_zone=one:10m;

```
proxy_cache_path /data/nginx/cache levels=1:2 keys_zone=one:10m;
```
file names in a cache will look like this:

> /data/nginx/cache/c/29/b7f54b2df7773722d382f4809d65029c

```
/data/nginx/cache/c/29/b7f54b2df7773722d382f4809d65029c
```
A cached response is first written to a temporary file,
and then the file is renamed.
Starting from version 0.8.9, temporary files and the cache can be put on
different file systems.
However, be aware that in this case a file is copied
across two file systems instead of the cheap renaming operation.
It is thus recommended that for any given location both cache and a directory
holding temporary files
are put on the same file system.
The directory for temporary files is set based on
theuse_temp_pathparameter (1.7.10).
If this parameter is omitted or set to the valueon,
the directory set by theproxy_temp_pathdirective
for the given location will be used.
If the value is set tooff,
temporary files will be put directly in the cache directory.

`use_temp_path``on``off`In addition, all active keys and information about data are stored
in a shared memory zone, whosenameandsizeare configured by thekeys_zoneparameter.
One megabyte zone can store about 8 thousand keys.

`name``size``keys_zone`
> As part ofcommercial subscription,
the shared memory zone also stores extended
cacheinformation,
thus, it is required to specify a larger zone size for the same number of keys.
For example,
one megabyte zone can store about 4 thousand keys.
Cached data that are not accessed during the time specified by theinactiveparameter get removed from the cache
regardless of their freshness.
By default,inactiveis set to 10 minutes.

`inactive``inactive`The special “cache manager” process monitors the maximum cache size set
by themax_sizeparameter,
and the minimum amount of free space set
by themin_free(1.19.1) parameter
on the file system with cache.
When the size is exceeded or there is not enough free space,
it removes the least recently used data.
The data is removed in iterations configured bymanager_files,manager_threshold, andmanager_sleepparameters (1.11.5).
During one iteration no more thanmanager_filesitems
are deleted (by default, 100).
The duration of one iteration is limited by themanager_thresholdparameter (by default, 200 milliseconds).
Between iterations, a pause configured by themanager_sleepparameter (by default, 50 milliseconds) is made.

`max_size``min_free``manager_files``manager_threshold``manager_sleep``manager_files``manager_threshold``manager_sleep`A minute after the start the special “cache loader” process is activated.
It loads information about previously cached data stored on file system
into a cache zone.
The loading is also done in iterations.
During one iteration no more thanloader_filesitems
are loaded (by default, 100).
Besides, the duration of one iteration is limited by theloader_thresholdparameter (by default, 200 milliseconds).
Between iterations, a pause configured by theloader_sleepparameter (by default, 50 milliseconds) is made.

`loader_files``loader_threshold``loader_sleep`Additionally,
the following parameters are available as part of ourcommercial subscription:

`purger``on``off``on``off``purger_files``number``purger_files``purger_threshold``number``purger_threshold``purger_sleep``number``purger_sleep`
> In versions 1.7.3, 1.7.7, and 1.11.10 cache header format has been changed.
Previously cached responses will be considered invalid
after upgrading to a newer nginx version.
Syntax:proxy_cache_purgestring ...;Default:—Context:http,server,locationThis directive appeared in version 1.5.7.

`proxy_cache_purge string ...;``http``server``location`This directive appeared in version 1.5.7.

Defines conditions under which the request will be considered a cache
purge request.
If at least one value of the string parameters is not empty and is not equal
to “0” then the cache entry with a correspondingcache keyis removed.
The result of successful operation is indicated by returning
the 204 (No Content) response.

If thecache keyof a purge request ends
with an asterisk (“*”), all cache entries matching the
wildcard key will be removed from the cache.
However, these entries will remain on the disk until they are deleted
for eitherinactivity,
or processed by thecache purger(1.7.12),
or a client attempts to access them.

`*`Example configuration:

> proxy_cache_path /data/nginx/cache keys_zone=cache_zone:10m;

map $request_method $purge_method {
 PURGE 1;
 default 0;
}

server {
 ...
 location / {
 proxy_pass http://backend;
 proxy_cache cache_zone;
 proxy_cache_key $uri;
 proxy_cache_purge $purge_method;
 }
}

```
proxy_cache_path /data/nginx/cache keys_zone=cache_zone:10m;

map $request_method $purge_method {
 PURGE 1;
 default 0;
}

server {
 ...
 location / {
 proxy_pass http://backend;
 proxy_cache cache_zone;
 proxy_cache_key $uri;
 proxy_cache_purge $purge_method;
 }
}
```

> This functionality is available as part of ourcommercial subscription.
Syntax:proxy_cache_revalidateon|off;Default:proxy_cache_revalidate off;Context:http,server,locationThis directive appeared in version 1.5.7.

`proxy_cache_revalidate on | off;``on``off`
```
proxy_cache_revalidate off;
```
`http``server``location`This directive appeared in version 1.5.7.

Enables revalidation of expired cache items using conditional requests with
the “If-Modified-Since” and “If-None-Match”
header fields.

Syntax:proxy_cache_use_staleerror|timeout|invalid_header|updating|http_500|http_502|http_503|http_504|http_403|http_404|http_429|off...;Default:proxy_cache_use_stale off;Context:http,server,location

`proxy_cache_use_stale
error |
 timeout |
 invalid_header |
 updating |
 http_500 |
 http_502 |
 http_503 |
 http_504 |
 http_403 |
 http_404 |
 http_429 |
 off
 ...;``error``timeout``invalid_header``updating``http_500``http_502``http_503``http_504``http_403``http_404``http_429``off`
```
proxy_cache_use_stale off;
```
`http``server``location`Determines in which cases a stale cached response can be used
during communication with the proxied server.
The directive’s parameters match the parameters of theproxy_next_upstreamdirective.

Theerrorparameter also permits
using a stale cached response if a proxied server to process a request
cannot be selected.

`error`Additionally, theupdatingparameter permits
using a stale cached response if it is currently being updated.
This allows minimizing the number of accesses to proxied servers
when updating cached data.

`updating`Using a stale cached response
can also be enabled directly in the response header
for a specified number of seconds after the response became stale (1.11.10).
This has lower priority than using the directive parameters.

• The
“stale-while-revalidate”
extension of the “Cache-Control” header field permits
using a stale cached response if it is currently being updated.
• The
“stale-if-error”
extension of the “Cache-Control” header field permits
using a stale cached response in case of an error.
To minimize the number of accesses to proxied servers when
populating a new cache element, theproxy_cache_lockdirective can be used.

Syntax:proxy_cache_valid[code...]time;Default:—Context:http,server,location

`proxy_cache_valid [code ...] time;``code``time``http``server``location`Sets caching time for different response codes.
For example, the following directives

> proxy_cache_valid 200 302 10m;
proxy_cache_valid 404 1m;

```
proxy_cache_valid 200 302 10m;
proxy_cache_valid 404 1m;
```
set 10 minutes of caching for responses with codes 200 and 302
and 1 minute for responses with code 404.

If only cachingtimeis specified

`time`
> proxy_cache_valid 5m;

```
proxy_cache_valid 5m;
```
then only 200, 301, and 302 responses are cached.

In addition, theanyparameter can be specified
to cache any responses:

`any`
> proxy_cache_valid 200 302 10m;
proxy_cache_valid 301 1h;
proxy_cache_valid any 1m;

```
proxy_cache_valid 200 302 10m;
proxy_cache_valid 301 1h;
proxy_cache_valid any 1m;
```
Parameters of caching can also be set directly
in the response header.
This has higher priority than setting of caching time using the directive.

• The “X-Accel-Expires” header field sets caching time of a
response in seconds.
The zero value disables caching for a response.
If the value starts with the@prefix, it sets an absolute
time in seconds since Epoch, up to which the response may be cached.
`@`• If the header does not include the “X-Accel-Expires” field,
parameters of caching may be set in the header fields
“Expires” or “Cache-Control”.
• If the header includes the “Set-Cookie” field, such a
response will not be cached.
• If the header includes the “Vary” field
with the special value “*”, such a
response will not be cached (1.7.7).
If the header includes the “Vary” field
with another value, such a response will be cached
taking into account the corresponding request header fields (1.7.7).
`*`Processing of one or more of these response header fields can be disabled
using theproxy_ignore_headersdirective.

Syntax:proxy_connect_timeouttime;Default:proxy_connect_timeout 60s;Context:http,server,location

`proxy_connect_timeout time;``time`
```
proxy_connect_timeout 60s;
```
`http``server``location`Defines a timeout for establishing a connection with a proxied server.
It should be noted that this timeout cannot usually exceed 75 seconds.

Syntax:proxy_cookie_domainoff;proxy_cookie_domaindomainreplacement;Default:proxy_cookie_domain off;Context:http,server,locationThis directive appeared in version 1.1.15.

`proxy_cookie_domain off;``off``proxy_cookie_domain domain replacement;``domain``replacement`
```
proxy_cookie_domain off;
```
`http``server``location`This directive appeared in version 1.1.15.

Sets a text that should be changed in thedomainattribute of the “Set-Cookie” header fields of a
proxied server response.
Suppose a proxied server returned the “Set-Cookie”
header field with the attribute
“domain=localhost”.
The directive

`domain``domain=localhost`
> proxy_cookie_domain localhost example.org;

```
proxy_cookie_domain localhost example.org;
```
will rewrite this attribute to
“domain=example.org”.

`domain=example.org`A dot at the beginning of thedomainandreplacementstrings and thedomainattribute is ignored.
Matching is case-insensitive.

`domain``replacement``domain`Thedomainandreplacementstrings
can contain variables:

`domain``replacement`
> proxy_cookie_domain www.$host $host;

```
proxy_cookie_domain www.$host $host;
```
The directive can also be specified using regular expressions.
In this case,domainshould start from
the “~” symbol.
A regular expression can contain named and positional captures,
andreplacementcan reference them:

`domain``~``replacement`
> proxy_cookie_domain ~\.(?P<sl_domain>[-0-9a-z]+\.[a-z]+)$ $sl_domain;

```
proxy_cookie_domain ~\.(?P<sl_domain>[-0-9a-z]+\.[a-z]+)$ $sl_domain;
```
Severalproxy_cookie_domaindirectives
can be specified on the same level:

`proxy_cookie_domain`
> proxy_cookie_domain localhost example.org;
proxy_cookie_domain ~\.([a-z]+\.[a-z]+)$ $1;

```
proxy_cookie_domain localhost example.org;
proxy_cookie_domain ~\.([a-z]+\.[a-z]+)$ $1;
```
If several directives can be applied to the cookie,
the first matching directive will be chosen.

Theoffparameter cancels the effect
of theproxy_cookie_domaindirectives
inherited from the previous configuration level.

`off``proxy_cookie_domain`Syntax:proxy_cookie_flagsoff|cookie[flag...];Default:proxy_cookie_flags off;Context:http,server,locationThis directive appeared in version 1.19.3.

`proxy_cookie_flags
off |
 cookie
 [flag ...];``off``cookie``flag`
```
proxy_cookie_flags off;
```
`http``server``location`This directive appeared in version 1.19.3.

Sets one or more flags for the cookie.
Thecookiecan contain text, variables, and their combinations.
Theflagcan contain text, variables, and their combinations (1.19.8).
Thesecure,httponly,samesite=strict,samesite=lax,samesite=noneparameters add the corresponding flags.
Thenosecure,nohttponly,nosamesiteparameters remove the corresponding flags.

`cookie``flag``secure``httponly``samesite=strict``samesite=lax``samesite=none``nosecure``nohttponly``nosamesite`The cookie can also be specified using regular expressions.
In this case,cookieshould start from
the “~” symbol.

`cookie``~`Severalproxy_cookie_flagsdirectives
can be specified on the same configuration level:

`proxy_cookie_flags`
> proxy_cookie_flags one httponly;
proxy_cookie_flags ~ nosecure samesite=strict;

```
proxy_cookie_flags one httponly;
proxy_cookie_flags ~ nosecure samesite=strict;
```
If several directives can be applied to the cookie,
the first matching directive will be chosen.
In the example, thehttponlyflag
is added to the cookieone,
for all other cookies
thesamesite=strictflag is added and
thesecureflag is deleted.

`httponly``one``samesite=strict``secure`Theoffparameter cancels the effect
of theproxy_cookie_flagsdirectives
inherited from the previous configuration level.

`off``proxy_cookie_flags`Syntax:proxy_cookie_pathoff;proxy_cookie_pathpathreplacement;Default:proxy_cookie_path off;Context:http,server,locationThis directive appeared in version 1.1.15.

`proxy_cookie_path off;``off``proxy_cookie_path path replacement;``path``replacement`
```
proxy_cookie_path off;
```
`http``server``location`This directive appeared in version 1.1.15.

Sets a text that should be changed in thepathattribute of the “Set-Cookie” header fields of a
proxied server response.
Suppose a proxied server returned the “Set-Cookie”
header field with the attribute
“path=/two/some/uri/”.
The directive

`path``path=/two/some/uri/`
> proxy_cookie_path /two/ /;

```
proxy_cookie_path /two/ /;
```
will rewrite this attribute to
“path=/some/uri/”.

`path=/some/uri/`Thepathandreplacementstrings
can contain variables:

`path``replacement`
> proxy_cookie_path $uri /some$uri;

```
proxy_cookie_path $uri /some$uri;
```
The directive can also be specified using regular expressions.
In this case,pathshould either start from
the “~” symbol for a case-sensitive matching,
or from the “~*” symbols for case-insensitive
matching.
The regular expression can contain named and positional captures,
andreplacementcan reference them:

`path``~``~*``replacement`
> proxy_cookie_path ~*^/user/([^/]+) /u/$1;

```
proxy_cookie_path ~*^/user/([^/]+) /u/$1;
```
Severalproxy_cookie_pathdirectives
can be specified on the same level:

`proxy_cookie_path`
> proxy_cookie_path /one/ /;
proxy_cookie_path / /two/;

```
proxy_cookie_path /one/ /;
proxy_cookie_path / /two/;
```
If several directives can be applied to the cookie,
the first matching directive will be chosen.

Theoffparameter cancels the effect
of theproxy_cookie_pathdirectives
inherited from the previous configuration level.

`off``proxy_cookie_path`Syntax:proxy_force_rangeson|off;Default:proxy_force_ranges off;Context:http,server,locationThis directive appeared in version 1.7.7.

`proxy_force_ranges on | off;``on``off`
```
proxy_force_ranges off;
```
`http``server``location`This directive appeared in version 1.7.7.

Enables byte-range support
for both cached and uncached responses from the proxied server
regardless of the “Accept-Ranges” field in these responses.

Syntax:proxy_headers_hash_bucket_sizesize;Default:proxy_headers_hash_bucket_size 64;Context:http,server,location

`proxy_headers_hash_bucket_size size;``size`
```
proxy_headers_hash_bucket_size 64;
```
`http``server``location`Sets the bucketsizefor hash tables
used by theproxy_hide_headerandproxy_set_headerdirectives.
The details of setting up hash tables are provided in a separatedocument.

`size`Syntax:proxy_headers_hash_max_sizesize;Default:proxy_headers_hash_max_size 512;Context:http,server,location

`proxy_headers_hash_max_size size;``size`
```
proxy_headers_hash_max_size 512;
```
`http``server``location`Sets the maximumsizeof hash tables
used by theproxy_hide_headerandproxy_set_headerdirectives.
The details of setting up hash tables are provided in a separatedocument.

`size`Syntax:proxy_hide_headerfield;Default:—Context:http,server,location

`proxy_hide_header field;``field``http``server``location`By default,
nginx does not pass the header fields “Date”,
“Server”, “X-Pad”, and
“X-Accel-...” from the response of a proxied
server to a client.
Theproxy_hide_headerdirective sets additional fields
that will not be passed.
If, on the contrary, the passing of fields needs to be permitted,
theproxy_pass_headerdirective can be used.

`proxy_hide_header`Syntax:proxy_http_version1.0|1.1;Default:proxy_http_version 1.0;Context:http,server,locationThis directive appeared in version 1.1.4.

`proxy_http_version 1.0 | 1.1;``1.0``1.1`
```
proxy_http_version 1.0;
```
`http``server``location`This directive appeared in version 1.1.4.

Sets the HTTP protocol version for proxying.
By default, version 1.0 is used.
Version 1.1 is recommended for use withkeepaliveconnections andNTLM authentication.

Syntax:proxy_ignore_client_aborton|off;Default:proxy_ignore_client_abort off;Context:http,server,location

`proxy_ignore_client_abort on | off;``on``off`
```
proxy_ignore_client_abort off;
```
`http``server``location`Determines whether the connection with a proxied server should be
closed when a client closes the connection without waiting
for a response.

Syntax:proxy_ignore_headersfield...;Default:—Context:http,server,location

`proxy_ignore_headers field ...;``field``http``server``location`Disables processing of certain response header fields from the proxied server.
The following fields can be ignored: “X-Accel-Redirect”,
“X-Accel-Expires”, “X-Accel-Limit-Rate” (1.1.6),
“X-Accel-Buffering” (1.1.6),
“X-Accel-Charset” (1.1.6), “Expires”,
“Cache-Control”, “Set-Cookie” (0.8.44),
and “Vary” (1.7.7).

If not disabled, processing of these header fields has the following
effect:

• “X-Accel-Expires”, “Expires”,
“Cache-Control”, “Set-Cookie”,
and “Vary”
set the parameters of responsecaching;
• “X-Accel-Redirect” performs aninternal
redirectto the specified URI;
• “X-Accel-Limit-Rate” sets therate
limitfor transmission of a response to a client;
• “X-Accel-Buffering” enables or disablesbufferingof a response;
• “X-Accel-Charset” sets the desiredcharsetof a response.
Syntax:proxy_intercept_errorson|off;Default:proxy_intercept_errors off;Context:http,server,location

`proxy_intercept_errors on | off;``on``off`
```
proxy_intercept_errors off;
```
`http``server``location`Determines whether proxied responses with codes greater than or equal
to 300 should be passed to a client
or be intercepted and redirected to nginx for processing
with theerror_pagedirective.

Syntax:proxy_limit_raterate;Default:proxy_limit_rate 0;Context:http,server,locationThis directive appeared in version 1.7.7.

`proxy_limit_rate rate;``rate`
```
proxy_limit_rate 0;
```
`http``server``location`This directive appeared in version 1.7.7.

Limits the speed of reading the response from the proxied server.
Therateis specified in bytes per second.
The zero value disables rate limiting.
The limit is set per a request, and so if nginx simultaneously opens
two connections to the proxied server,
the overall rate will be twice as much as the specified limit.
The limitation works only ifbufferingof responses from the proxied
server is enabled.
Parameter value can contain variables (1.27.0).

`rate`Syntax:proxy_max_temp_file_sizesize;Default:proxy_max_temp_file_size 1024m;Context:http,server,location

`proxy_max_temp_file_size size;``size`
```
proxy_max_temp_file_size 1024m;
```
`http``server``location`Whenbufferingof responses from the proxied
server is enabled, and the whole response does not fit into the buffers
set by theproxy_buffer_sizeandproxy_buffersdirectives, a part of the response can be saved to a temporary file.
This directive sets the maximumsizeof the temporary file.
The size of data written to the temporary file at a time is set
by theproxy_temp_file_write_sizedirective.

`size`The zero value disables buffering of responses to temporary files.

> This restriction does not apply to responses
that will becachedorstoredon disk.
Syntax:proxy_methodmethod;Default:—Context:http,server,location

`proxy_method method;``method``http``server``location`Specifies the HTTPmethodto use in requests forwarded
to the proxied server instead of the method from the client request.
Parameter value can contain variables (1.11.6).

`method`Syntax:proxy_next_upstreamerror|timeout|invalid_header|http_500|http_502|http_503|http_504|http_403|http_404|http_429|non_idempotent|off...;Default:proxy_next_upstream error timeout;Context:http,server,location

`proxy_next_upstream
error |
 timeout |
 invalid_header |
 http_500 |
 http_502 |
 http_503 |
 http_504 |
 http_403 |
 http_404 |
 http_429 |
 non_idempotent |
 off
 ...;``error``timeout``invalid_header``http_500``http_502``http_503``http_504``http_403``http_404``http_429``non_idempotent``off`
```
proxy_next_upstream error timeout;
```
`http``server``location`Specifies in which cases a request should be passed to the next server:

`error``timeout``invalid_header``http_500``http_502``http_503``http_504``http_403``http_404``http_429``non_idempotent``POST``LOCK``PATCH``off`One should bear in mind that passing a request to the next server is
only possible if nothing has been sent to a client yet.
That is, if an error or timeout occurs in the middle of the
transferring of a response, fixing this is impossible.

The directive also defines what is considered anunsuccessful
attemptof communication with a server.
The cases oferror,timeoutandinvalid_headerare always considered unsuccessful attempts,
even if they are not specified in the directive.
The cases ofhttp_500,http_502,http_503,http_504,
andhttp_429are
considered unsuccessful attempts only if they are specified in the directive.
The cases ofhttp_403andhttp_404are never considered unsuccessful attempts.

`error``timeout``invalid_header``http_500``http_502``http_503``http_504``http_429``http_403``http_404`Passing a request to the next server can be limited bythe number of triesand bytime.

Syntax:proxy_next_upstream_timeouttime;Default:proxy_next_upstream_timeout 0;Context:http,server,locationThis directive appeared in version 1.7.5.

`proxy_next_upstream_timeout time;``time`
```
proxy_next_upstream_timeout 0;
```
`http``server``location`This directive appeared in version 1.7.5.

Limits the time during which a request can be passed to thenext server.
The0value turns off this limitation.

`0`Syntax:proxy_next_upstream_triesnumber;Default:proxy_next_upstream_tries 0;Context:http,server,locationThis directive appeared in version 1.7.5.

`proxy_next_upstream_tries number;``number`
```
proxy_next_upstream_tries 0;
```
`http``server``location`This directive appeared in version 1.7.5.

Limits the number of possible tries for passing a request to thenext server.
The0value turns off this limitation.

`0`Syntax:proxy_no_cachestring...;Default:—Context:http,server,location

`proxy_no_cache string ...;``string``http``server``location`Defines conditions under which the response will not be saved to a cache.
If at least one value of the string parameters is not empty and is not
equal to “0” then the response will not be saved:

> proxy_no_cache $cookie_nocache $arg_nocache$arg_comment;
proxy_no_cache $http_pragma $http_authorization;

```
proxy_no_cache $cookie_nocache $arg_nocache$arg_comment;
proxy_no_cache $http_pragma $http_authorization;
```
Can be used along with theproxy_cache_bypassdirective.

Syntax:proxy_passURL;Default:—Context:location,if in location,limit_except

`proxy_pass URL;``URL``location``if in location``limit_except`Sets the protocol and address of a proxied server and an optional URI
to which a location should be mapped.
As a protocol, “http” or “https”
can be specified.
The address can be specified as a domain name or IP address,
and an optional port:

`http``https`
> proxy_pass http://localhost:8000/uri/;

```
proxy_pass http://localhost:8000/uri/;
```
or as a UNIX-domain socket path specified after the word
“unix” and enclosed in colons:

`unix`
> proxy_pass http://unix:/tmp/backend.socket:/uri/;

```
proxy_pass http://unix:/tmp/backend.socket:/uri/;
```
If a domain name resolves to several addresses, all of them will be
used in a round-robin fashion.
In addition, an address can be specified as aserver group.

Parameter value can contain variables.
In this case, if an address is specified as a domain name,
the name is searched among the described server groups,
and, if not found, is determined using aresolver.

A request URI is passed to the server as follows:

• If theproxy_passdirective is specified with a URI,
then when a request is passed to the server, the part of anormalizedrequest URI matching the location is replaced by a URI
specified in the directive:location /name/ {
 proxy_pass http://127.0.0.1/remote/;
}
`proxy_pass`
> location /name/ {
 proxy_pass http://127.0.0.1/remote/;
}

```
location /name/ {
 proxy_pass http://127.0.0.1/remote/;
}
```
• Ifproxy_passis specified without a URI,
the request URI is passed to the server in the same form
as sent by a client when the original request is processed,
or the full normalized request URI is passed
when processing the changed URI:location /some/path/ {
 proxy_pass http://127.0.0.1;
}Before version 1.1.12,
ifproxy_passis specified without a URI,
the original request URI might be passed
instead of the changed URI in some cases.
`proxy_pass`
> location /some/path/ {
 proxy_pass http://127.0.0.1;
}

```
location /some/path/ {
 proxy_pass http://127.0.0.1;
}
```

> Before version 1.1.12,
ifproxy_passis specified without a URI,
the original request URI might be passed
instead of the changed URI in some cases.
`proxy_pass`In some cases, the part of a request URI to be replaced cannot be determined:

• When location is specified using a regular expression,
and also inside named locations.In these cases,proxy_passshould be specified without a URI.
In these cases,proxy_passshould be specified without a URI.

`proxy_pass`• When the URI is changed inside a proxied location using therewritedirective,
and this same configuration will be used to process a request
(break):location /name/ {
 rewrite /name/([^/]+) /users?name=$1 break;
 proxy_pass http://127.0.0.1;
}In this case, the URI specified in the directive is ignored and
the full changed request URI is passed to the server.
`break`
> location /name/ {
 rewrite /name/([^/]+) /users?name=$1 break;
 proxy_pass http://127.0.0.1;
}

```
location /name/ {
 rewrite /name/([^/]+) /users?name=$1 break;
 proxy_pass http://127.0.0.1;
}
```
In this case, the URI specified in the directive is ignored and
the full changed request URI is passed to the server.

• When variables are used inproxy_pass:location /name/ {
 proxy_pass http://127.0.0.1$request_uri;
}In this case, if URI is specified in the directive,
it is passed to the server as is,
replacing the original request URI.
`proxy_pass`
> location /name/ {
 proxy_pass http://127.0.0.1$request_uri;
}

```
location /name/ {
 proxy_pass http://127.0.0.1$request_uri;
}
```
WebSocketproxying requires special
configuration and is supported since version 1.3.13.

Syntax:proxy_pass_headerfield;Default:—Context:http,server,location

`proxy_pass_header field;``field``http``server``location`Permits passingotherwise disabledheader
fields from a proxied server to a client.

Syntax:proxy_pass_request_bodyon|off;Default:proxy_pass_request_body on;Context:http,server,location

`proxy_pass_request_body on | off;``on``off`
```
proxy_pass_request_body on;
```
`http``server``location`Indicates whether the original request body is passed
to the proxied server.

> location /x-accel-redirect-here/ {
 proxy_method GET;
 proxy_pass_request_body off;
 proxy_set_header Content-Length "";

 proxy_pass ...
}

```
location /x-accel-redirect-here/ {
 proxy_method GET;
 proxy_pass_request_body off;
 proxy_set_header Content-Length "";

 proxy_pass ...
}
```
See also theproxy_set_headerandproxy_pass_request_headersdirectives.

Syntax:proxy_pass_request_headerson|off;Default:proxy_pass_request_headers on;Context:http,server,location

`proxy_pass_request_headers on | off;``on``off`
```
proxy_pass_request_headers on;
```
`http``server``location`Indicates whether the header fields of the original request are passed
to the proxied server.

> location /x-accel-redirect-here/ {
 proxy_method GET;
 proxy_pass_request_headers off;
 proxy_pass_request_body off;

 proxy_pass ...
}

```
location /x-accel-redirect-here/ {
 proxy_method GET;
 proxy_pass_request_headers off;
 proxy_pass_request_body off;

 proxy_pass ...
}
```
See also theproxy_set_headerandproxy_pass_request_bodydirectives.

Syntax:proxy_pass_trailerson|off;Default:proxy_pass_trailers off;Context:http,server,locationThis directive appeared in version 1.27.2.

`proxy_pass_trailers on | off;``on``off`
```
proxy_pass_trailers off;
```
`http``server``location`This directive appeared in version 1.27.2.

Permits passing trailer fields from a proxied server to a client.

> A trailer section in HTTP/1.1 isexplicitly
enabled.

> location / {
 proxy_http_version 1.1;
 proxy_set_header Connection "te";
 proxy_set_header TE "trailers";
 proxy_pass_trailers on;

 proxy_pass ...
}

```
location / {
 proxy_http_version 1.1;
 proxy_set_header Connection "te";
 proxy_set_header TE "trailers";
 proxy_pass_trailers on;

 proxy_pass ...
}
```
Syntax:proxy_read_timeouttime;Default:proxy_read_timeout 60s;Context:http,server,location

`proxy_read_timeout time;``time`
```
proxy_read_timeout 60s;
```
`http``server``location`Defines a timeout for reading a response from the proxied server.
The timeout is set only between two successive read operations,
not for the transmission of the whole response.
If the proxied server does not transmit anything within this time,
the connection is closed.

Syntax:proxy_redirectdefault;proxy_redirectoff;proxy_redirectredirectreplacement;Default:proxy_redirect default;Context:http,server,location

`proxy_redirect default;``default``proxy_redirect off;``off``proxy_redirect redirect replacement;``redirect``replacement`
```
proxy_redirect default;
```
`http``server``location`Sets the text that should be changed in the “Location”
and “Refresh” header fields of a proxied server response.
Suppose a proxied server returned the header field
“Location: http://localhost:8000/two/some/uri/”.
The directive

`Location: http://localhost:8000/two/some/uri/`
> proxy_redirect http://localhost:8000/two/ http://frontend/one/;

```
proxy_redirect http://localhost:8000/two/ http://frontend/one/;
```
will rewrite this string to
“Location: http://frontend/one/some/uri/”.

`Location: http://frontend/one/some/uri/`A server name may be omitted in thereplacementstring:

`replacement`
> proxy_redirect http://localhost:8000/two/ /;

```
proxy_redirect http://localhost:8000/two/ /;
```
then the primary server’s name and port, if different from 80,
will be inserted.

The default replacement specified by thedefaultparameter
uses the parameters of thelocationandproxy_passdirectives.
Hence, the two configurations below are equivalent:

`default`
> location /one/ {
 proxy_pass http://upstream:port/two/;
 proxy_redirect default;

```
location /one/ {
 proxy_pass http://upstream:port/two/;
 proxy_redirect default;
```

> location /one/ {
 proxy_pass http://upstream:port/two/;
 proxy_redirect http://upstream:port/two/ /one/;

```
location /one/ {
 proxy_pass http://upstream:port/two/;
 proxy_redirect http://upstream:port/two/ /one/;
```
Thedefaultparameter is not permitted ifproxy_passis specified using variables.

`default`Areplacementstring can contain variables:

`replacement`
> proxy_redirect http://localhost:8000/ http://$host:$server_port/;

```
proxy_redirect http://localhost:8000/ http://$host:$server_port/;
```
Aredirectcan also contain (1.1.11) variables:

`redirect`
> proxy_redirect http://$proxy_host:8000/ /;

```
proxy_redirect http://$proxy_host:8000/ /;
```
The directive can be specified (1.1.11) using regular expressions.
In this case,redirectshould either start with
the “~” symbol for a case-sensitive matching,
or with the “~*” symbols for case-insensitive
matching.
The regular expression can contain named and positional captures,
andreplacementcan reference them:

`redirect``~``~*``replacement`
> proxy_redirect ~^(http://[^:]+):\d+(/.+)$ $1$2;
proxy_redirect ~*/user/([^/]+)/(.+)$ http://$1.example.com/$2;

```
proxy_redirect ~^(http://[^:]+):\d+(/.+)$ $1$2;
proxy_redirect ~*/user/([^/]+)/(.+)$ http://$1.example.com/$2;
```
Severalproxy_redirectdirectives
can be specified on the same level:

`proxy_redirect`
> proxy_redirect default;
proxy_redirect http://localhost:8000/ /;
proxy_redirect http://www.example.com/ /;

```
proxy_redirect default;
proxy_redirect http://localhost:8000/ /;
proxy_redirect http://www.example.com/ /;
```
If several directives can be applied to
the header fields of a proxied server response,
the first matching directive will be chosen.

Theoffparameter cancels the effect
of theproxy_redirectdirectives
inherited from the previous configuration level.

`off``proxy_redirect`Using this directive, it is also possible to add host names to relative
redirects issued by a proxied server:

> proxy_redirect / /;

```
proxy_redirect / /;
```
Syntax:proxy_request_bufferingon|off;Default:proxy_request_buffering on;Context:http,server,locationThis directive appeared in version 1.7.11.

`proxy_request_buffering on | off;``on``off`
```
proxy_request_buffering on;
```
`http``server``location`This directive appeared in version 1.7.11.

Enables or disables buffering of a client request body.

When buffering is enabled, the entire request body isreadfrom the client before sending the request to a proxied server.

When buffering is disabled, the request body is sent to the proxied server
immediately as it is received.
In this case, the request cannot be passed to thenext serverif nginx already started sending the request body.

When HTTP/1.1 chunked transfer encoding is used
to send the original request body,
the request body will be buffered regardless of the directive value unless
HTTP/1.1 isenabledfor proxying.

Syntax:proxy_send_lowatsize;Default:proxy_send_lowat 0;Context:http,server,location

`proxy_send_lowat size;``size`
```
proxy_send_lowat 0;
```
`http``server``location`If the directive is set to a non-zero value, nginx will try to
minimize the number
of send operations on outgoing connections to a proxied server by using eitherNOTE_LOWATflag of thekqueuemethod,
or theSO_SNDLOWATsocket option,
with the specifiedsize.

`NOTE_LOWAT``SO_SNDLOWAT``size`This directive is ignored on Linux, Solaris, and Windows.

Syntax:proxy_send_timeouttime;Default:proxy_send_timeout 60s;Context:http,server,location

`proxy_send_timeout time;``time`
```
proxy_send_timeout 60s;
```
`http``server``location`Sets a timeout for transmitting a request to the proxied server.
The timeout is set only between two successive write operations,
not for the transmission of the whole request.
If the proxied server does not receive anything within this time,
the connection is closed.

Syntax:proxy_set_bodyvalue;Default:—Context:http,server,location

`proxy_set_body value;``value``http``server``location`Allows redefining the request body passed to the proxied server.
Thevaluecan contain text, variables, and their combination.

`value`Syntax:proxy_set_headerfieldvalue;Default:proxy_set_header Host $proxy_host;proxy_set_header Connection close;Context:http,server,location

`proxy_set_header field value;``field``value`
```
proxy_set_header Host $proxy_host;
```

```
proxy_set_header Connection close;
```
`http``server``location`Allows redefining or appending fields to the request headerpassedto the proxied server.
Thevaluecan contain text, variables, and their combinations.
These directives are inherited from the previous configuration level
if and only if there are noproxy_set_headerdirectives
defined on the current level.
By default, only two fields are redefined:

`value``proxy_set_header`
> proxy_set_header Host $proxy_host;
proxy_set_header Connection close;

```
proxy_set_header Host $proxy_host;
proxy_set_header Connection close;
```
If caching is enabled, the header fields
“If-Modified-Since”,
“If-Unmodified-Since”,
“If-None-Match”,
“If-Match”,
“Range”,
and
“If-Range”
from the original request are not passed to the proxied server.

An unchanged “Host” request header field can be passed like this:

> proxy_set_header Host $http_host;

```
proxy_set_header Host $http_host;
```
However, if this field is not present in a client request header then
nothing will be passed.
In such a case it is better to use the$hostvariable - its
value equals the server name in the “Host” request header
field or the primary server name if this field is not present:

`$host`
> proxy_set_header Host $host;

```
proxy_set_header Host $host;
```
In addition, the server name can be passed together with the port of the
proxied server:

> proxy_set_header Host $host:$proxy_port;

```
proxy_set_header Host $host:$proxy_port;
```
If the value of a header field is an empty string then this
field will not be passed to a proxied server:

> proxy_set_header Accept-Encoding "";

```
proxy_set_header Accept-Encoding "";
```
Syntax:proxy_socket_keepaliveon|off;Default:proxy_socket_keepalive off;Context:http,server,locationThis directive appeared in version 1.15.6.

`proxy_socket_keepalive on | off;``on``off`
```
proxy_socket_keepalive off;
```
`http``server``location`This directive appeared in version 1.15.6.

Configures the “TCP keepalive” behavior
for outgoing connections to a proxied server.
By default, the operating system’s settings are in effect for the socket.
If the directive is set to the value “on”, theSO_KEEPALIVEsocket option is turned on for the socket.

`on``SO_KEEPALIVE`Syntax:proxy_ssl_certificatefile;Default:—Context:http,server,locationThis directive appeared in version 1.7.8.

`proxy_ssl_certificate file;``file``http``server``location`This directive appeared in version 1.7.8.

Specifies afilewith the certificate in the PEM format
used for authentication to a proxied HTTPS server.

`file`Since version 1.21.0, variables can be used in thefilename.

`file`Syntax:proxy_ssl_certificate_cacheoff;proxy_ssl_certificate_cachemax=N[inactive=time]
 [valid=time];Default:proxy_ssl_certificate_cache off;Context:http,server,locationThis directive appeared in version 1.27.4.

`proxy_ssl_certificate_cache off;``off``proxy_ssl_certificate_cache
max=N
 [inactive=time]
 [valid=time];``max``N``inactive``time``valid``time`
```
proxy_ssl_certificate_cache off;
```
`http``server``location`This directive appeared in version 1.27.4.

Defines a cache that storesSSL certificatesandsecret keysspecified withvariables.

The directive has the following parameters:

`max``inactive``valid``off`
> proxy_ssl_certificate $proxy_ssl_server_name.crt;
proxy_ssl_certificate_key $proxy_ssl_server_name.key;
proxy_ssl_certificate_cache max=1000 inactive=20s valid=1m;

```
proxy_ssl_certificate $proxy_ssl_server_name.crt;
proxy_ssl_certificate_key $proxy_ssl_server_name.key;
proxy_ssl_certificate_cache max=1000 inactive=20s valid=1m;
```
Syntax:proxy_ssl_certificate_keyfile;Default:—Context:http,server,locationThis directive appeared in version 1.7.8.

`proxy_ssl_certificate_key file;``file``http``server``location`This directive appeared in version 1.7.8.

Specifies afilewith the secret key in the PEM format
used for authentication to a proxied HTTPS server.

`file`The valueengine:name:idcan be specified instead of thefile(1.7.9),
which loads a secret key with a specifiedidfrom the OpenSSL enginename.

`engine``name``id``file``id``name`The valuestore:scheme:idcan be specified instead of thefile(1.29.0),
which is used to load a secret key with a specifiedidand OpenSSL provider registered URIscheme, such aspkcs11.

`store``scheme``id``file``id``scheme``pkcs11`Since version 1.21.0, variables can be used in thefilename.

`file`Syntax:proxy_ssl_ciphersciphers;Default:proxy_ssl_ciphers DEFAULT;Context:http,server,locationThis directive appeared in version 1.5.6.

`proxy_ssl_ciphers ciphers;``ciphers`
```
proxy_ssl_ciphers DEFAULT;
```
`http``server``location`This directive appeared in version 1.5.6.

Specifies the enabled ciphers for requests to a proxied HTTPS server.
The ciphers are specified in the format understood by the OpenSSL library.

The full list can be viewed using the
“openssl ciphers” command.

`openssl ciphers`Syntax:proxy_ssl_conf_commandnamevalue;Default:—Context:http,server,locationThis directive appeared in version 1.19.4.

`proxy_ssl_conf_command name value;``name``value``http``server``location`This directive appeared in version 1.19.4.

Sets arbitrary OpenSSL configurationcommandswhen establishing a connection with the proxied HTTPS server.

> The directive is supported when using OpenSSL 1.0.2 or higher.
Severalproxy_ssl_conf_commanddirectives
can be specified on the same level.
These directives are inherited from the previous configuration level
if and only if there are
noproxy_ssl_conf_commanddirectives
defined on the current level.

`proxy_ssl_conf_command``proxy_ssl_conf_command`
> Note that configuring OpenSSL directly
might result in unexpected behavior.
Syntax:proxy_ssl_crlfile;Default:—Context:http,server,locationThis directive appeared in version 1.7.0.

`proxy_ssl_crl file;``file``http``server``location`This directive appeared in version 1.7.0.

Specifies afilewith revoked certificates (CRL)
in the PEM format used toverifythe certificate of the proxied HTTPS server.

`file`Syntax:proxy_ssl_key_logpath;Default:—Context:http,server,locationThis directive appeared in version 1.27.2.

`proxy_ssl_key_log path;``http``server``location`This directive appeared in version 1.27.2.

Enables logging of proxied HTTPS server connection SSL keys
and specifies the path to the key log file.
Keys are logged in theSSLKEYLOGFILEformat compatible with Wireshark.

> This directive is available as part of ourcommercial subscription.
Syntax:proxy_ssl_namename;Default:proxy_ssl_name $proxy_host;Context:http,server,locationThis directive appeared in version 1.7.0.

`proxy_ssl_name name;``name`
```
proxy_ssl_name $proxy_host;
```
`http``server``location`This directive appeared in version 1.7.0.

Allows overriding the server name used toverifythe certificate of the proxied HTTPS server and to bepassed through SNIwhen establishing a connection with the proxied HTTPS server.

By default, the host part of theproxy_passURL is used.

Syntax:proxy_ssl_password_filefile;Default:—Context:http,server,locationThis directive appeared in version 1.7.8.

`proxy_ssl_password_file file;``file``http``server``location`This directive appeared in version 1.7.8.

Specifies afilewith passphrases forsecret keyswhere each passphrase is specified on a separate line.
Passphrases are tried in turn when loading the key.

`file`Syntax:proxy_ssl_protocols[SSLv2]
 [SSLv3]
 [TLSv1]
 [TLSv1.1]
 [TLSv1.2]
 [TLSv1.3];Default:proxy_ssl_protocols TLSv1.2 TLSv1.3;Context:http,server,locationThis directive appeared in version 1.5.6.

`proxy_ssl_protocols 
 [SSLv2]
 [SSLv3]
 [TLSv1]
 [TLSv1.1]
 [TLSv1.2]
 [TLSv1.3];``SSLv2``SSLv3``TLSv1``TLSv1.1``TLSv1.2``TLSv1.3`
```
proxy_ssl_protocols TLSv1.2 TLSv1.3;
```
`http``server``location`This directive appeared in version 1.5.6.

Enables the specified protocols for requests to a proxied HTTPS server.

> TheTLSv1.3parameter is used by default
since 1.23.4.
`TLSv1.3`Syntax:proxy_ssl_server_nameon|off;Default:proxy_ssl_server_name off;Context:http,server,locationThis directive appeared in version 1.7.0.

`proxy_ssl_server_name on | off;``on``off`
```
proxy_ssl_server_name off;
```
`http``server``location`This directive appeared in version 1.7.0.

Enables or disables passing of the server name throughTLS
Server Name Indication extension(SNI, RFC 6066)
when establishing a connection with the proxied HTTPS server.

Syntax:proxy_ssl_session_reuseon|off;Default:proxy_ssl_session_reuse on;Context:http,server,location

`proxy_ssl_session_reuse on | off;``on``off`
```
proxy_ssl_session_reuse on;
```
`http``server``location`Determines whether SSL sessions can be reused when working with
the proxied server.
If the errors
“digest check failed”
appear in the logs, try disabling session reuse.

`digest check failed`Syntax:proxy_ssl_trusted_certificatefile;Default:—Context:http,server,locationThis directive appeared in version 1.7.0.

`proxy_ssl_trusted_certificate file;``file``http``server``location`This directive appeared in version 1.7.0.

Specifies afilewith trusted CA certificates in the PEM format
used toverifythe certificate of the proxied HTTPS server.

`file`Syntax:proxy_ssl_verifyon|off;Default:proxy_ssl_verify off;Context:http,server,locationThis directive appeared in version 1.7.0.

`proxy_ssl_verify on | off;``on``off`
```
proxy_ssl_verify off;
```
`http``server``location`This directive appeared in version 1.7.0.

Enables or disables verification of the proxied HTTPS server certificate.

Syntax:proxy_ssl_verify_depthnumber;Default:proxy_ssl_verify_depth 1;Context:http,server,locationThis directive appeared in version 1.7.0.

`proxy_ssl_verify_depth number;``number`
```
proxy_ssl_verify_depth 1;
```
`http``server``location`This directive appeared in version 1.7.0.

Sets the verification depth in the proxied HTTPS server certificates chain.

Syntax:proxy_storeon|off|string;Default:proxy_store off;Context:http,server,location

`proxy_store
on |
 off |
 string;``on``off``string`
```
proxy_store off;
```
`http``server``location`Enables saving of files to a disk.
Theonparameter saves files with paths
corresponding to the directivesaliasorroot.
Theoffparameter disables saving of files.
In addition, the file name can be set explicitly using thestringwith variables:

`on``off``string`
> proxy_store /data/www$original_uri;

```
proxy_store /data/www$original_uri;
```
The modification time of files is set according to the received
“Last-Modified” response header field.
The response is first written to a temporary file,
and then the file is renamed.
Starting from version 0.8.9, temporary files and the persistent store
can be put on different file systems.
However, be aware that in this case a file is copied
across two file systems instead of the cheap renaming operation.
It is thus recommended that for any given location both saved files and a
directory holding temporary files, set by theproxy_temp_pathdirective, are put on the same file system.

This directive can be used to create local copies of static unchangeable
files, e.g.:

> location /images/ {
 root /data/www;
 error_page 404 = /fetch$uri;
}

location /fetch/ {
 internal;

 proxy_pass http://backend/;
 proxy_store on;
 proxy_store_access user:rw group:rw all:r;
 proxy_temp_path /data/temp;

 alias /data/www/;
}

```
location /images/ {
 root /data/www;
 error_page 404 = /fetch$uri;
}

location /fetch/ {
 internal;

 proxy_pass http://backend/;
 proxy_store on;
 proxy_store_access user:rw group:rw all:r;
 proxy_temp_path /data/temp;

 alias /data/www/;
}
```
or like this:

> location /images/ {
 root /data/www;
 error_page 404 = @fetch;
}

location @fetch {
 internal;

 proxy_pass http://backend;
 proxy_store on;
 proxy_store_access user:rw group:rw all:r;
 proxy_temp_path /data/temp;

 root /data/www;
}

```
location /images/ {
 root /data/www;
 error_page 404 = @fetch;
}

location @fetch {
 internal;

 proxy_pass http://backend;
 proxy_store on;
 proxy_store_access user:rw group:rw all:r;
 proxy_temp_path /data/temp;

 root /data/www;
}
```
Syntax:proxy_store_accessusers:permissions...;Default:proxy_store_access user:rw;Context:http,server,location

`proxy_store_access users:permissions ...;``users``permissions`
```
proxy_store_access user:rw;
```
`http``server``location`Sets access permissions for newly created files and directories, e.g.:

> proxy_store_access user:rw group:rw all:r;

```
proxy_store_access user:rw group:rw all:r;
```
If anygrouporallaccess permissions
are specified thenuserpermissions may be omitted:

`group``all``user`
> proxy_store_access group:rw all:r;

```
proxy_store_access group:rw all:r;
```
Syntax:proxy_temp_file_write_sizesize;Default:proxy_temp_file_write_size 8k|16k;Context:http,server,location

`proxy_temp_file_write_size size;``size`
```
proxy_temp_file_write_size 8k|16k;
```
`http``server``location`Limits thesizeof data written to a temporary file
at a time, when buffering of responses from the proxied server
to temporary files is enabled.
By default,sizeis limited by two buffers set by theproxy_buffer_sizeandproxy_buffersdirectives.
The maximum size of a temporary file is set by theproxy_max_temp_file_sizedirective.

`size``size`Syntax:proxy_temp_pathpath[level1[level2[level3]]];Default:proxy_temp_path proxy_temp;Context:http,server,location

`proxy_temp_path
path
 [level1
 [level2
 [level3]]];``path``level1``level2``level3`
```
proxy_temp_path proxy_temp;
```
`http``server``location`Defines a directory for storing temporary files
with data received from proxied servers.
Up to three-level subdirectory hierarchy can be used underneath the specified
directory.
For example, in the following configuration

> proxy_temp_path /spool/nginx/proxy_temp 1 2;

```
proxy_temp_path /spool/nginx/proxy_temp 1 2;
```
a temporary file might look like this:

> /spool/nginx/proxy_temp/7/45/00000123457

```
/spool/nginx/proxy_temp/7/45/00000123457
```
See also theuse_temp_pathparameter of theproxy_cache_pathdirective.

`use_temp_path`
#### Embedded Variables
Thengx_http_proxy_modulemodule supports embedded variables
that can be used to compose headers using theproxy_set_headerdirective:

`ngx_http_proxy_module``$proxy_host``$proxy_port``$proxy_add_x_forwarded_for``$remote_addr``$proxy_add_x_forwarded_for``$remote_addr`

---

## 9. Module ngx_http_core_module

**Source:** https://nginx.org/en/docs/http/ngx_http_core_module.html
**Word Count:** 7,805

### Content:

## Module ngx_http_core_module

#### Directives
Syntax:absolute_redirecton|off;Default:absolute_redirect on;Context:http,server,locationThis directive appeared in version 1.11.8.

`absolute_redirect on | off;``on``off`
```
absolute_redirect on;
```
`http``server``location`This directive appeared in version 1.11.8.

If disabled, redirects issued by nginx will be relative.

See alsoserver_name_in_redirectandport_in_redirectdirectives.

Syntax:aioon|off|threads[=pool];Default:aio off;Context:http,server,locationThis directive appeared in version 0.8.11.

`aio
on |
 off |
 threads[=pool];``on``off``threads``=``pool`
```
aio off;
```
`http``server``location`This directive appeared in version 0.8.11.

Enables or disables the use of asynchronous file I/O (AIO)
on FreeBSD and Linux:

> location /video/ {
 aio on;
 output_buffers 1 64k;
}

```
location /video/ {
 aio on;
 output_buffers 1 64k;
}
```
On FreeBSD, AIO can be used starting from FreeBSD 4.3.
Prior to FreeBSD 11.0,
AIO can either be linked statically into a kernel:

> options VFS_AIO

```
options VFS_AIO
```
or loaded dynamically as a kernel loadable module:

> kldload aio

```
kldload aio
```
On Linux, AIO can be used starting from kernel version 2.6.22.
Also, it is necessary to enabledirectio,
or otherwise reading will be blocking:

> location /video/ {
 aio on;
 directio 512;
 output_buffers 1 128k;
}

```
location /video/ {
 aio on;
 directio 512;
 output_buffers 1 128k;
}
```
On Linux,directiocan only be used for reading blocks that are aligned on 512-byte
boundaries (or 4K for XFS).
File’s unaligned end is read in blocking mode.
The same holds true for byte range requests and for FLV requests
not from the beginning of a file: reading of unaligned data at the
beginning and end of a file will be blocking.

When both AIO andsendfileare enabled on Linux,
AIO is used for files that are larger than or equal to
the size specified in thedirectiodirective,
whilesendfileis used for files of smaller sizes
or whendirectiois disabled.

> location /video/ {
 sendfile on;
 aio on;
 directio 8m;
}

```
location /video/ {
 sendfile on;
 aio on;
 directio 8m;
}
```
Finally, files can be read andsentusing multi-threading (1.7.11),
without blocking a worker process:

> location /video/ {
 sendfile on;
 aio threads;
}

```
location /video/ {
 sendfile on;
 aio threads;
}
```
Read and send file operations are offloaded to threads of the specifiedpool.
If the pool name is omitted,
the pool with the name “default” is used.
The pool name can also be set with variables:

`default`
> aio threads=pool$disk;

```
aio threads=pool$disk;
```
By default, multi-threading is disabled, it should be
enabled with the--with-threadsconfiguration parameter.
Currently, multi-threading is compatible only with theepoll,kqueue,
andeventportmethods.
Multi-threaded sending of files is only supported on Linux.

`--with-threads`See also thesendfiledirective.

Syntax:aio_writeon|off;Default:aio_write off;Context:http,server,locationThis directive appeared in version 1.9.13.

`aio_write on | off;``on``off`
```
aio_write off;
```
`http``server``location`This directive appeared in version 1.9.13.

Ifaiois enabled, specifies whether it is used for writing files.
Currently, this only works when usingaio threadsand is limited to writing temporary files
with data received from proxied servers.

`aio threads`Syntax:aliaspath;Default:—Context:location

`alias path;``path``location`Defines a replacement for the specified location.
For example, with the following configuration

> location /i/ {
 alias /data/w3/images/;
}

```
location /i/ {
 alias /data/w3/images/;
}
```
on request of
“/i/top.gif”, the file/data/w3/images/top.gifwill be sent.

`/i/top.gif``/data/w3/images/top.gif`Thepathvalue can contain variables,
except$document_rootand$realpath_root.

`path``$document_root``$realpath_root`Ifaliasis used inside a location defined
with a regular expression then such regular expression should
contain captures andaliasshould refer to
these captures (0.7.40), for example:

`alias``alias`
> location ~ ^/users/(.+\.(?:gif|jpe?g|png))$ {
 alias /data/w3/images/$1;
}

```
location ~ ^/users/(.+\.(?:gif|jpe?g|png))$ {
 alias /data/w3/images/$1;
}
```
When location matches the last part of the directive’s value:

> location /images/ {
 alias /data/w3/images/;
}

```
location /images/ {
 alias /data/w3/images/;
}
```
it is better to use therootdirective instead:

> location /images/ {
 root /data/w3;
}

```
location /images/ {
 root /data/w3;
}
```
Syntax:auth_delaytime;Default:auth_delay 0s;Context:http,server,locationThis directive appeared in version 1.17.10.

`auth_delay time;``time`
```
auth_delay 0s;
```
`http``server``location`This directive appeared in version 1.17.10.

Delays processing of unauthorized requests with 401 response code
to prevent timing attacks when access is limited bypassword, by theresult of subrequest,
or byJWT.

Syntax:chunked_transfer_encodingon|off;Default:chunked_transfer_encoding on;Context:http,server,location

`chunked_transfer_encoding on | off;``on``off`
```
chunked_transfer_encoding on;
```
`http``server``location`Allows disabling chunked transfer encoding in HTTP/1.1.
It may come in handy when using a software failing to support
chunked encoding despite the standard’s requirement.

Syntax:client_body_buffer_sizesize;Default:client_body_buffer_size 8k|16k;Context:http,server,location

`client_body_buffer_size size;``size`
```
client_body_buffer_size 8k|16k;
```
`http``server``location`Sets buffer size for reading client request body.
In case the request body is larger than the buffer,
the whole body or only its part is written to atemporary file.
By default, buffer size is equal to two memory pages.
This is 8K on x86, other 32-bit platforms, and x86-64.
It is usually 16K on other 64-bit platforms.

Syntax:client_body_in_file_onlyon|clean|off;Default:client_body_in_file_only off;Context:http,server,location

`client_body_in_file_only
on |
 clean |
 off;``on``clean``off`
```
client_body_in_file_only off;
```
`http``server``location`Determines whether nginx should save the entire client request body
into a file.
This directive can be used during debugging, or when using the$request_body_filevariable, or the$r->request_body_filemethod of the modulengx_http_perl_module.

`$request_body_file`When set to the valueon, temporary files are not
removed after request processing.

`on`The valuecleanwill cause the temporary files
left after request processing to be removed.

`clean`Syntax:client_body_in_single_bufferon|off;Default:client_body_in_single_buffer off;Context:http,server,location

`client_body_in_single_buffer on | off;``on``off`
```
client_body_in_single_buffer off;
```
`http``server``location`Determines whether nginx should save the entire client request body
in a single buffer.
The directive is recommended when using the$request_bodyvariable, to save the number of copy operations involved.

`$request_body`Syntax:client_body_temp_pathpath[level1[level2[level3]]];Default:client_body_temp_path client_body_temp;Context:http,server,location

`client_body_temp_path
path
 [level1
 [level2
 [level3]]];``path``level1``level2``level3`
```
client_body_temp_path client_body_temp;
```
`http``server``location`Defines a directory for storing temporary files holding client request bodies.
Up to three-level subdirectory hierarchy can be used under the specified
directory.
For example, in the following configuration

> client_body_temp_path /spool/nginx/client_temp 1 2;

```
client_body_temp_path /spool/nginx/client_temp 1 2;
```
a path to a temporary file might look like this:

> /spool/nginx/client_temp/7/45/00000123457

```
/spool/nginx/client_temp/7/45/00000123457
```
Syntax:client_body_timeouttime;Default:client_body_timeout 60s;Context:http,server,location

`client_body_timeout time;``time`
```
client_body_timeout 60s;
```
`http``server``location`Defines a timeout for reading client request body.
The timeout is set only for a period between two successive read operations,
not for the transmission of the whole request body.
If a client does not transmit anything within this time, the
request is terminated with the
408 (Request Time-out)
error.

Syntax:client_header_buffer_sizesize;Default:client_header_buffer_size 1k;Context:http,server

`client_header_buffer_size size;``size`
```
client_header_buffer_size 1k;
```
`http``server`Sets buffer size for reading client request header.
For most requests, a buffer of 1K bytes is enough.
However, if a request includes long cookies, or comes from a WAP client,
it may not fit into 1K.
If a request line or a request header field does not fit into
this buffer then larger buffers, configured by thelarge_client_header_buffersdirective,
are allocated.

If the directive is specified on theserverlevel,
the value from the default server can be used.
Details are provided in the
“Virtual
server selection” section.

Syntax:client_header_timeouttime;Default:client_header_timeout 60s;Context:http,server

`client_header_timeout time;``time`
```
client_header_timeout 60s;
```
`http``server`Defines a timeout for reading client request header.
If a client does not transmit the entire header within this time, the
request is terminated with the
408 (Request Time-out)
error.

Syntax:client_max_body_sizesize;Default:client_max_body_size 1m;Context:http,server,location

`client_max_body_size size;``size`
```
client_max_body_size 1m;
```
`http``server``location`Sets the maximum allowed size of the client request body.
If the size in a request exceeds the configured value, the
413 (Request Entity Too Large)
error is returned to the client.
Please be aware that
browsers cannot correctly display
this error.
Settingsizeto 0 disables checking of client
request body size.

`size`Syntax:connection_pool_sizesize;Default:connection_pool_size 256|512;Context:http,server

`connection_pool_size size;``size`
```
connection_pool_size 256|512;
```
`http``server`Allows accurate tuning of per-connection memory allocations.
This directive has minimal impact on performance
and should not generally be used.
By default, the size is equal to
256 bytes on 32-bit platforms and 512 bytes on 64-bit platforms.

> Prior to version 1.9.8, the default value was 256 on all platforms.
Syntax:default_typemime-type;Default:default_type text/plain;Context:http,server,location

`default_type mime-type;``mime-type`
```
default_type text/plain;
```
`http``server``location`Defines the default MIME type of a response.
Mapping of file name extensions to MIME types can be set
with thetypesdirective.

Syntax:directiosize|off;Default:directio off;Context:http,server,locationThis directive appeared in version 0.7.7.

`directio size | off;``size``off`
```
directio off;
```
`http``server``location`This directive appeared in version 0.7.7.

Enables the use of
theO_DIRECTflag (FreeBSD, Linux),
theF_NOCACHEflag (macOS),
or thedirectio()function (Solaris),
when reading files that are larger than or equal to
the specifiedsize.
The directive automatically disables (0.7.15) the use ofsendfilefor a given request.
It can be useful for serving large files:

`O_DIRECT``F_NOCACHE``directio()``size`
> directio 4m;

```
directio 4m;
```
or when usingaioon Linux.

Syntax:directio_alignmentsize;Default:directio_alignment 512;Context:http,server,locationThis directive appeared in version 0.8.11.

`directio_alignment size;``size`
```
directio_alignment 512;
```
`http``server``location`This directive appeared in version 0.8.11.

Sets the alignment fordirectio.
In most cases, a 512-byte alignment is enough.
However, when using XFS under Linux, it needs to be increased to 4K.

Syntax:disable_symlinksoff;disable_symlinkson|if_not_owner[from=part];Default:disable_symlinks off;Context:http,server,locationThis directive appeared in version 1.1.15.

`disable_symlinks off;``off``disable_symlinks
on |
 if_not_owner
 [from=part];``on``if_not_owner``from``part`
```
disable_symlinks off;
```
`http``server``location`This directive appeared in version 1.1.15.

Determines how symbolic links should be treated when opening files:

`off``on``if_not_owner``from``part``on``if_not_owner``from``part`
> disable_symlinks on from=$document_root;

```
disable_symlinks on from=$document_root;
```
This directive is only available on systems that have theopenat()andfstatat()interfaces.
Such systems include modern versions of FreeBSD, Linux, and Solaris.

`openat()``fstatat()`Parametersonandif_not_owneradd a processing overhead.

`on``if_not_owner`
> On systems that do not support opening of directories only for search,
to use these parameters it is required that worker processes
have read permissions for all directories being checked.

> Thengx_http_autoindex_module,ngx_http_random_index_module,
andngx_http_dav_modulemodules currently ignore this directive.
Syntax:early_hintsstring...;Default:—Context:http,server,locationThis directive appeared in version 1.29.0.

`early_hints string ...;``string``http``server``location`This directive appeared in version 1.29.0.

Defines conditions under which
the 103 (Early Hints) response
will be passed to a client.
If at least one value of the string parameters is not empty and is not
equal to “0” then the response will be passed:

> map $http_sec_fetch_mode $early_hints {
 navigate $http2$http3;
}

server {
 ...
 location / {
 early_hints $early_hints;
 proxy_pass http://example.com;
 }
}

```
map $http_sec_fetch_mode $early_hints {
 navigate $http2$http3;
}

server {
 ...
 location / {
 early_hints $early_hints;
 proxy_pass http://example.com;
 }
}
```
Syntax:error_pagecode...
 [=[response]]uri;Default:—Context:http,server,location,if in location

`error_page
code ...
 [=[response]]
 uri;``code``=``response``uri``http``server``location``if in location`Defines the URI that will be shown for the specified errors.
Aurivalue can contain variables.

`uri`
> error_page 404 /404.html;
error_page *********** 504 /50x.html;

```
error_page 404 /404.html;
error_page *********** 504 /50x.html;
```
This causes an internal redirect to the specifieduriwith the client request method changed to “GET”
(for all methods other than
“GET” and “HEAD”).

`uri``GET``GET``HEAD`Furthermore, it is possible to change the response code to another
using the “=response” syntax, for example:

`=``response`
> error_page 404 =200 /empty.gif;

```
error_page 404 =200 /empty.gif;
```
If an error response is processed by a proxied server
or a FastCGI/uwsgi/SCGI/gRPC server,
and the server may return different response codes (e.g., 200, 302, 401
or 404), it is possible to respond with the code it returns:

> error_page 404 = /404.php;

```
error_page 404 = /404.php;
```
If there is no need to change URI and method during internal redirection
it is possible to pass error processing into a named location:

> location / {
 error_page 404 = @fallback;
}

location @fallback {
 proxy_pass http://backend;
}

```
location / {
 error_page 404 = @fallback;
}

location @fallback {
 proxy_pass http://backend;
}
```

> Ifuriprocessing leads to an error,
the status code of the last occurred error is returned to the client.
`uri`It is also possible to use URL redirects for error processing:

> error_page 403 http://example.com/forbidden.html;
error_page 404 =301 http://example.com/notfound.html;

```
error_page 403 http://example.com/forbidden.html;
error_page 404 =301 http://example.com/notfound.html;
```
In this case, by default, the response code 302 is returned to the client.
It can only be changed to one of the redirect status
codes (301, 302, 303, 307, and 308).

> The code 307 was not treated as a redirect until versions 1.1.16 and 1.0.13.

> The code 308 was not treated as a redirect until version 1.13.0.
These directives are inherited from the previous configuration level
if and only if there are noerror_pagedirectives
defined on the current level.

`error_page`Syntax:etagon|off;Default:etag on;Context:http,server,locationThis directive appeared in version 1.3.3.

`etag on | off;``on``off`
```
etag on;
```
`http``server``location`This directive appeared in version 1.3.3.

Enables or disables automatic generation of the “ETag”
response header field for static resources.

Syntax:http{ ... }Default:—Context:main

`http { ... }``main`Provides the configuration file context in which the HTTP server directives
are specified.

Syntax:if_modified_sinceoff|exact|before;Default:if_modified_since exact;Context:http,server,locationThis directive appeared in version 0.7.24.

`if_modified_since
off |
 exact |
 before;``off``exact``before`
```
if_modified_since exact;
```
`http``server``location`This directive appeared in version 0.7.24.

Specifies how to compare modification time of a response
with the time in the
“If-Modified-Since”
request header field:

`off``exact``before`Syntax:ignore_invalid_headerson|off;Default:ignore_invalid_headers on;Context:http,server

`ignore_invalid_headers on | off;``on``off`
```
ignore_invalid_headers on;
```
`http``server`Controls whether header fields with invalid names should be ignored.
Valid names are composed of English letters, digits, hyphens, and possibly
underscores (as controlled by theunderscores_in_headersdirective).

If the directive is specified on theserverlevel,
the value from the default server can be used.
Details are provided in the
“Virtual
server selection” section.

Syntax:internal;Default:—Context:location

`internal;``location`Specifies that a given location can only be used for internal requests.
For external requests, the client error
404 (Not Found)
is returned.
Internal requests are the following:

• requests redirected by theerror_page,index,internal_redirect,random_index, andtry_filesdirectives;
• requests redirected by the “X-Accel-Redirect”
response header field from an upstream server;
• subrequests formed by the
“include virtual”
command of thengx_http_ssi_modulemodule, by thengx_http_addition_modulemodule directives, and byauth_requestandmirrordirectives;
`include virtual`• requests changed by therewritedirective.

> error_page 404 /404.html;

location = /404.html {
 internal;
}

```
error_page 404 /404.html;

location = /404.html {
 internal;
}
```

> There is a limit of 10 internal redirects per request to prevent
request processing cycles that can occur in incorrect configurations.
If this limit is reached, the error
500 (Internal Server Error) is returned.
In such cases, the “rewrite or internal redirection cycle” message
can be seen in the error log.
Syntax:keepalive_disablenone|browser...;Default:keepalive_disable msie6;Context:http,server,location

`keepalive_disable none | browser ...;``none``browser`
```
keepalive_disable msie6;
```
`http``server``location`Disables keep-alive connections with misbehaving browsers.
Thebrowserparameters specify which
browsers will be affected.
The valuemsie6disables keep-alive connections
with old versions of MSIE, once a POST request is received.
The valuesafaridisables keep-alive connections
with Safari and Safari-like browsers on macOS and macOS-like
operating systems.
The valuenoneenables keep-alive connections
with all browsers.

`browser``msie6``safari``none`
> Prior to version 1.1.18, the valuesafarimatched
all Safari and Safari-like browsers on all operating systems, and
keep-alive connections with them were disabled by default.
`safari`Syntax:keepalive_min_timeouttimeout;Default:keepalive_min_timeout 0;Context:http,server,locationThis directive appeared in version 1.27.4.

`keepalive_min_timeout timeout;``timeout`
```
keepalive_min_timeout 0;
```
`http``server``location`This directive appeared in version 1.27.4.

Sets a timeout during which a keep-alive
client connection will not be closed on the server side
for connection reuse or on graceful shutdown of worker processes.

Syntax:keepalive_requestsnumber;Default:keepalive_requests 1000;Context:http,server,locationThis directive appeared in version 0.8.0.

`keepalive_requests number;``number`
```
keepalive_requests 1000;
```
`http``server``location`This directive appeared in version 0.8.0.

Sets the maximum number of requests that can be
served through one keep-alive connection.
After the maximum number of requests are made, the connection is closed.

Closing connections periodically is necessary to free
per-connection memory allocations.
Therefore, using too high maximum number of requests
could result in excessive memory usage and not recommended.

> Prior to version 1.19.10, the default value was 100.
Syntax:keepalive_timetime;Default:keepalive_time 1h;Context:http,server,locationThis directive appeared in version 1.19.10.

`keepalive_time time;``time`
```
keepalive_time 1h;
```
`http``server``location`This directive appeared in version 1.19.10.

Limits the maximum time during which
requests can be processed through one keep-alive connection.
After this time is reached, the connection is closed
following the subsequent request processing.

Syntax:keepalive_timeouttimeout[header_timeout];Default:keepalive_timeout 75s;Context:http,server,location

`keepalive_timeout
timeout
 [header_timeout];``timeout``header_timeout`
```
keepalive_timeout 75s;
```
`http``server``location`The first parameter sets a timeout during which a keep-alive
client connection will stay open on the server side.
The zero value disables keep-alive client connections.
The optional second parameter sets a value in the
“Keep-Alive: timeout=time”
response header field.
Two parameters may differ.

`time`The
“Keep-Alive: timeout=time”
header field is recognized by Mozilla and Konqueror.
MSIE closes keep-alive connections by itself in about 60 seconds.

`time`Syntax:large_client_header_buffersnumbersize;Default:large_client_header_buffers 4 8k;Context:http,server

`large_client_header_buffers number size;``number``size`
```
large_client_header_buffers 4 8k;
```
`http``server`Sets the maximumnumberandsizeof
buffers used for reading large client request header.
A request line cannot exceed the size of one buffer, or the
414 (Request-URI Too Large)
error is returned to the client.
A request header field cannot exceed the size of one buffer as well, or the
400 (Bad Request)
error is returned to the client.
Buffers are allocated only on demand.
By default, the buffer size is equal to 8K bytes.
If after the end of request processing a connection is transitioned
into the keep-alive state, these buffers are released.

`number``size`If the directive is specified on theserverlevel,
the value from the default server can be used.
Details are provided in the
“Virtual
server selection” section.

Syntax:limit_exceptmethod... { ... }Default:—Context:location

`limit_except method ... { ... }``method``location`Limits allowed HTTP methods inside a location.
Themethodparameter can be one of the following:GET,HEAD,POST,PUT,DELETE,MKCOL,COPY,MOVE,OPTIONS,PROPFIND,PROPPATCH,LOCK,UNLOCK,
orPATCH.
Allowing theGETmethod makes theHEADmethod also allowed.
Access to other methods can be limited using thengx_http_access_module,ngx_http_auth_basic_module,
andngx_http_auth_jwt_module(1.13.10)
modules directives:

`method``GET``HEAD``POST``PUT``DELETE``MKCOL``COPY``MOVE``OPTIONS``PROPFIND``PROPPATCH``LOCK``UNLOCK``PATCH``GET``HEAD`
> limit_except GET {
 allow 192.168.1.0/32;
 deny all;
}

```
limit_except GET {
 allow 192.168.1.0/32;
 deny all;
}
```
Please note that this will limit access to all methodsexceptGET and HEAD.

Syntax:limit_raterate;Default:limit_rate 0;Context:http,server,location,if in location

`limit_rate rate;``rate`
```
limit_rate 0;
```
`http``server``location``if in location`Limits the rate of response transmission to a client.
Therateis specified in bytes per second.
The zero value disables rate limiting.

The limit is set per a request, and so if a client simultaneously opens
two connections, the overall rate will be twice as much
as the specified limit.

`rate`Parameter value can contain variables (1.17.0).
It may be useful in cases where rate should be limited
depending on a certain condition:

> map $slow $rate {
 1 4k;
 2 8k;
}

limit_rate $rate;

```
map $slow $rate {
 1 4k;
 2 8k;
}

limit_rate $rate;
```
Rate limit can also be set in the$limit_ratevariable,
however, since version 1.17.0, this method is not recommended:

`$limit_rate`
> server {

 if ($slow) {
 set $limit_rate 4k;
 }

 ...
}

```
server {

 if ($slow) {
 set $limit_rate 4k;
 }

 ...
}
```
Rate limit can also be set in the
“X-Accel-Limit-Rate” header field of a proxied server response.
This capability can be disabled using theproxy_ignore_headers,fastcgi_ignore_headers,uwsgi_ignore_headers,
andscgi_ignore_headersdirectives.

Syntax:limit_rate_aftersize;Default:limit_rate_after 0;Context:http,server,location,if in locationThis directive appeared in version 0.8.0.

`limit_rate_after size;``size`
```
limit_rate_after 0;
```
`http``server``location``if in location`This directive appeared in version 0.8.0.

Sets the initial amount after which the further transmission
of a response to a client will be rate limited.
Parameter value can contain variables (1.17.0).

> location /flv/ {
 flv;
 limit_rate_after 500k;
 limit_rate 50k;
}

```
location /flv/ {
 flv;
 limit_rate_after 500k;
 limit_rate 50k;
}
```
Syntax:lingering_closeoff|on|always;Default:lingering_close on;Context:http,server,locationThis directive appeared in versions 1.1.0 and 1.0.6.

`lingering_close
off |
 on |
 always;``off``on``always`
```
lingering_close on;
```
`http``server``location`This directive appeared in versions 1.1.0 and 1.0.6.

Controls how nginx closes client connections.

The default value “on” instructs nginx towait forandprocessadditional data from a client
before fully closing a connection, but only
if heuristics suggests that a client may be sending more data.

`on`The value “always” will cause nginx to unconditionally
wait for and process additional client data.

`always`The value “off” tells nginx to never wait for
more data and close the connection immediately.
This behavior breaks the protocol and should not be used under normal
circumstances.

`off`To control closingHTTP/2connections,
the directive must be specified on theserverlevel (1.19.1).

Syntax:lingering_timetime;Default:lingering_time 30s;Context:http,server,location

`lingering_time time;``time`
```
lingering_time 30s;
```
`http``server``location`Whenlingering_closeis in effect,
this directive specifies the maximum time during which nginx
will process (read and ignore) additional data coming from a client.
After that, the connection will be closed, even if there will be
more data.

Syntax:lingering_timeouttime;Default:lingering_timeout 5s;Context:http,server,location

`lingering_timeout time;``time`
```
lingering_timeout 5s;
```
`http``server``location`Whenlingering_closeis in effect, this directive specifies
the maximum waiting time for more client data to arrive.
If data are not received during this time, the connection is closed.
Otherwise, the data are read and ignored, and nginx starts waiting
for more data again.
The “wait-read-ignore” cycle is repeated, but no longer than specified by thelingering_timedirective.

Syntax:listenaddress[:port]
 [default_server]
 [ssl]
 [http2|quic]
 [proxy_protocol]
 [setfib=number]
 [fastopen=number]
 [backlog=number]
 [rcvbuf=size]
 [sndbuf=size]
 [accept_filter=filter]
 [deferred]
 [bind]
 [ipv6only=on|off]
 [reuseport]
 [so_keepalive=on|off|[keepidle]:[keepintvl]:[keepcnt]];listenport[default_server]
 [ssl]
 [http2|quic]
 [proxy_protocol]
 [setfib=number]
 [fastopen=number]
 [backlog=number]
 [rcvbuf=size]
 [sndbuf=size]
 [accept_filter=filter]
 [deferred]
 [bind]
 [ipv6only=on|off]
 [reuseport]
 [so_keepalive=on|off|[keepidle]:[keepintvl]:[keepcnt]];listenunix:path[default_server]
 [ssl]
 [http2|quic]
 [proxy_protocol]
 [backlog=number]
 [rcvbuf=size]
 [sndbuf=size]
 [accept_filter=filter]
 [deferred]
 [bind]
 [so_keepalive=on|off|[keepidle]:[keepintvl]:[keepcnt]];Default:listen *:80 | *:8000;Context:server

`listen
address[:port]
 [default_server]
 [ssl]
 [http2 |
 quic]
 [proxy_protocol]
 [setfib=number]
 [fastopen=number]
 [backlog=number]
 [rcvbuf=size]
 [sndbuf=size]
 [accept_filter=filter]
 [deferred]
 [bind]
 [ipv6only=on|off]
 [reuseport]
 [so_keepalive=on|off|[keepidle]:[keepintvl]:[keepcnt]];``address``port``default_server``ssl``http2``quic``proxy_protocol``setfib``number``fastopen``number``backlog``number``rcvbuf``size``sndbuf``size``accept_filter``filter``deferred``bind``ipv6only``on``off``reuseport``so_keepalive``on``off``keepidle``keepintvl``keepcnt``listen
port
 [default_server]
 [ssl]
 [http2 |
 quic]
 [proxy_protocol]
 [setfib=number]
 [fastopen=number]
 [backlog=number]
 [rcvbuf=size]
 [sndbuf=size]
 [accept_filter=filter]
 [deferred]
 [bind]
 [ipv6only=on|off]
 [reuseport]
 [so_keepalive=on|off|[keepidle]:[keepintvl]:[keepcnt]];``port``default_server``ssl``http2``quic``proxy_protocol``setfib``number``fastopen``number``backlog``number``rcvbuf``size``sndbuf``size``accept_filter``filter``deferred``bind``ipv6only``on``off``reuseport``so_keepalive``on``off``keepidle``keepintvl``keepcnt``listen
unix:path
 [default_server]
 [ssl]
 [http2 |
 quic]
 [proxy_protocol]
 [backlog=number]
 [rcvbuf=size]
 [sndbuf=size]
 [accept_filter=filter]
 [deferred]
 [bind]
 [so_keepalive=on|off|[keepidle]:[keepintvl]:[keepcnt]];``unix:``path``default_server``ssl``http2``quic``proxy_protocol``backlog``number``rcvbuf``size``sndbuf``size``accept_filter``filter``deferred``bind``so_keepalive``on``off``keepidle``keepintvl``keepcnt`
```
listen *:80 | *:8000;
```
`server`Sets theaddressandportfor IP,
or thepathfor a UNIX-domain socket on which
the server will accept requests.
Bothaddressandport,
or onlyaddressor onlyportcan be specified.
Anaddressmay also be a hostname, for example:

`address``port``path``address``port``address``port``address`
> listen 127.0.0.1:8000;
listen 127.0.0.1;
listen 8000;
listen *:8000;
listen localhost:8000;

```
listen 127.0.0.1:8000;
listen 127.0.0.1;
listen 8000;
listen *:8000;
listen localhost:8000;
```
IPv6 addresses (0.7.36) are specified in square brackets:

> listen [::]:8000;
listen [::1];

```
listen [::]:8000;
listen [::1];
```
UNIX-domain sockets (0.8.21) are specified with the “unix:”
prefix:

`unix:`
> listen unix:/var/run/nginx.sock;

```
listen unix:/var/run/nginx.sock;
```
If onlyaddressis given, the port 80 is used.

`address`If the directive is not present then either*:80is used
if nginx runs with the superuser privileges, or*:8000otherwise.

`*:80``*:8000`Thedefault_serverparameter, if present,
will cause the server to become the default server for the specifiedaddress:portpair.
If none of the directives have thedefault_serverparameter then the first server with theaddress:portpair will be
the default server for this pair.

`default_server``address``port``default_server``address``port`
> In versions prior to 0.8.21 this parameter is named simplydefault.
`default`Thesslparameter (0.7.14) allows specifying that all
connections accepted on this port should work in SSL mode.
This allows for a more compactconfigurationfor the server that
handles both HTTP and HTTPS requests.

`ssl`Thehttp2parameter (1.9.5) configures the port to acceptHTTP/2connections.
Normally, for this to work thesslparameter should be
specified as well, but nginx can also be configured to accept HTTP/2
connections without SSL.

`http2``ssl`
> The parameter is deprecated,
thehttp2directive
should be used instead.
Thequicparameter (1.25.0) configures the port to acceptQUICconnections.

`quic`Theproxy_protocolparameter (1.5.12)
allows specifying that all connections accepted on this port should use thePROXY
protocol.

`proxy_protocol`
> The PROXY protocol version 2 is supported since version 1.13.11.
Thelistendirective
can have several additional parameters specific to socket-related system calls.
These parameters can be specified in anylistendirective, but only once for a givenaddress:portpair.

`listen``listen``address``port`
> In versions prior to 0.8.21, they could only be
specified in thelistendirective together with thedefaultparameter.
`listen``default``setfib``number``SO_SETFIB``fastopen``number`
> Do not enable this feature unless the server can handle
receiving thesame SYN packet with datamore than once.
`backlog``number``backlog``listen()``backlog``rcvbuf``size``SO_RCVBUF``sndbuf``size``SO_SNDBUF``accept_filter``filter``SO_ACCEPTFILTER``accept()``deferred``accept()``TCP_DEFER_ACCEPT``bind``bind()``address``port``listen``listen``*:``port``bind()``*:``port``getsockname()``setfib``fastopen``backlog``rcvbuf``sndbuf``accept_filter``deferred``ipv6only``reuseport``so_keepalive``address``port``bind()``ipv6only``on``off``IPV6_V6ONLY``[::]`
> Prior to version 1.3.4,
if this parameter was omitted then the operating system’s settings were
in effect for the socket.
`reuseport``SO_REUSEPORT``SO_REUSEPORT_LB`
> Inappropriate use of this option may have its securityimplications.
`so_keepalive``on``off``keepidle``keepintvl``keepcnt``on``SO_KEEPALIVE``off``SO_KEEPALIVE``TCP_KEEPIDLE``TCP_KEEPINTVL``TCP_KEEPCNT``keepidle``keepintvl``keepcnt`
> so_keepalive=30m::10

```
so_keepalive=30m::10
```
`TCP_KEEPIDLE``TCP_KEEPINTVL``TCP_KEEPCNT`
> listen 127.0.0.1 default_server accept_filter=dataready backlog=1024;

```
listen 127.0.0.1 default_server accept_filter=dataready backlog=1024;
```
Syntax:location[=|~|~*|^~]uri{ ... }location@name{ ... }Default:—Context:server,location

`location [
 = |
 ~ |
 ~* |
 ^~
 ] uri { ... }``=``~``~*``^~``uri``location @name { ... }``@``name``server``location`Sets configuration depending on a request URI.

The matching is performed against a normalized URI,
after decoding the text encoded in the “%XX” form,
resolving references to relative path components “.”
and “..”, and possiblecompressionof two or more
adjacent slashes into a single slash.

`%XX``.``..`A location can either be defined by a prefix string, or by a regular expression.
Regular expressions are specified with the preceding
“~*” modifier (for case-insensitive matching), or the
“~” modifier (for case-sensitive matching).
To find location matching a given request, nginx first checks
locations defined using the prefix strings (prefix locations).
Among them, the location with the longest matching
prefix is selected and remembered.
Then regular expressions are checked, in the order of their appearance
in the configuration file.
The search of regular expressions terminates on the first match,
and the corresponding configuration is used.
If no match with a regular expression is found then the
configuration of the prefix location remembered earlier is used.

`~*``~`locationblocks can be nested, with some exceptions
mentioned below.

`location`For case-insensitive operating systems such as macOS and Cygwin,
matching with prefix strings ignores a case (0.7.7).
However, comparison is limited to one-byte locales.

Regular expressions can contain captures (0.7.40) that can later
be used in other directives.

If the longest matching prefix location has the “^~” modifier
then regular expressions are not checked.

`^~`Also, using the “=” modifier it is possible to define
an exact match of URI and location.
If an exact match is found, the search terminates.
For example, if a “/” request happens frequently,
defining “location = /” will speed up the processing
of these requests, as search terminates right after the first
comparison.
Such a location cannot obviously contain nested locations.

`=``/``location = /`
> In versions from 0.7.1 to 0.8.41, if a request matched the prefix
location without the “=” and “^~”
modifiers, the search also terminated and regular expressions were
not checked.
`=``^~`Let’s illustrate the above by an example:

> location = / {
 [ configuration A ]
}

location / {
 [ configuration B ]
}

location /documents/ {
 [ configuration C ]
}

location ^~ /images/ {
 [ configuration D ]
}

location ~* \.(gif|jpg|jpeg)$ {
 [ configuration E ]
}

```
location = / {
 [ configuration A ]
}

location / {
 [ configuration B ]
}

location /documents/ {
 [ configuration C ]
}

location ^~ /images/ {
 [ configuration D ]
}

location ~* \.(gif|jpg|jpeg)$ {
 [ configuration E ]
}
```
The “/” request will match configuration A,
the “/index.html” request will match configuration B,
the “/documents/document.html” request will match
configuration C,
the “/images/1.gif” request will match configuration D, and
the “/documents/1.jpg” request will match configuration E.

`/``/index.html``/documents/document.html``/images/1.gif``/documents/1.jpg`The “@” prefix defines a named location.
Such a location is not used for a regular request processing, but instead
used for request redirection.
They cannot be nested, and cannot contain nested locations.

`@`If a location is defined by a prefix string that ends with the slash character,
and requests are processed by one ofproxy_pass,fastcgi_pass,uwsgi_pass,scgi_pass,memcached_pass, orgrpc_pass,
then the special processing is performed.
In response to a request with URI equal to this string,
but without the trailing slash,
a permanent redirect with the code 301 will be returned to the requested URI
with the slash appended.
If this is not desired, an exact match of the URI and location could be
defined like this:

> location /user/ {
 proxy_pass http://user.example.com;
}

location = /user {
 proxy_pass http://login.example.com;
}

```
location /user/ {
 proxy_pass http://user.example.com;
}

location = /user {
 proxy_pass http://login.example.com;
}
```
Syntax:log_not_foundon|off;Default:log_not_found on;Context:http,server,location

`log_not_found on | off;``on``off`
```
log_not_found on;
```
`http``server``location`Enables or disables logging of errors about not found files intoerror_log.

Syntax:log_subrequeston|off;Default:log_subrequest off;Context:http,server,location

`log_subrequest on | off;``on``off`
```
log_subrequest off;
```
`http``server``location`Enables or disables logging of subrequests intoaccess_log.

Syntax:max_rangesnumber;Default:—Context:http,server,locationThis directive appeared in version 1.1.2.

`max_ranges number;``number``http``server``location`This directive appeared in version 1.1.2.

Limits the maximum allowed number of ranges in byte-range requests.
Requests that exceed the limit are processed as if there were no
byte ranges specified.
By default, the number of ranges is not limited.
The zero value disables the byte-range support completely.

Syntax:merge_slasheson|off;Default:merge_slashes on;Context:http,server

`merge_slashes on | off;``on``off`
```
merge_slashes on;
```
`http``server`Enables or disables compression of two or more adjacent slashes
in a URI into a single slash.

Note that compression is essential for the correct matching of prefix string
and regular expression locations.
Without it, the “//scripts/one.php” request would not match

`//scripts/one.php`
> location /scripts/ {
 ...
}

```
location /scripts/ {
 ...
}
```
and might be processed as a static file.
So it gets converted to “/scripts/one.php”.

`/scripts/one.php`Turning the compressionoffcan become necessary if a URI
contains base64-encoded names, since base64 uses the “/”
character internally.
However, for security considerations, it is better to avoid turning
the compression off.

`off``/`If the directive is specified on theserverlevel,
the value from the default server can be used.
Details are provided in the
“Virtual
server selection” section.

Syntax:msie_paddingon|off;Default:msie_padding on;Context:http,server,location

`msie_padding on | off;``on``off`
```
msie_padding on;
```
`http``server``location`Enables or disables adding comments to responses for MSIE clients with status
greater than 400 to increase the response size to 512 bytes.

Syntax:msie_refreshon|off;Default:msie_refresh off;Context:http,server,location

`msie_refresh on | off;``on``off`
```
msie_refresh off;
```
`http``server``location`Enables or disables issuing refreshes instead of redirects for MSIE clients.

Syntax:open_file_cacheoff;open_file_cachemax=N[inactive=time];Default:open_file_cache off;Context:http,server,location

`open_file_cache off;``off``open_file_cache
max=N
[inactive=time];``max``N``inactive``time`
```
open_file_cache off;
```
`http``server``location`Configures a cache that can store:

• open file descriptors, their sizes and modification times;
• information on existence of directories;
• file lookup errors, such as “file not found”, “no read permission”,
and so on.Caching of errors should be enabled separately by theopen_file_cache_errorsdirective.

> Caching of errors should be enabled separately by theopen_file_cache_errorsdirective.
The directive has the following parameters:

`max``inactive``off`
> open_file_cache max=1000 inactive=20s;
open_file_cache_valid 30s;
open_file_cache_min_uses 2;
open_file_cache_errors on;

```
open_file_cache max=1000 inactive=20s;
open_file_cache_valid 30s;
open_file_cache_min_uses 2;
open_file_cache_errors on;
```
Syntax:open_file_cache_errorson|off;Default:open_file_cache_errors off;Context:http,server,location

`open_file_cache_errors on | off;``on``off`
```
open_file_cache_errors off;
```
`http``server``location`Enables or disables caching of file lookup errors byopen_file_cache.

Syntax:open_file_cache_min_usesnumber;Default:open_file_cache_min_uses 1;Context:http,server,location

`open_file_cache_min_uses number;``number`
```
open_file_cache_min_uses 1;
```
`http``server``location`Sets the minimumnumberof file accesses during
the period configured by theinactiveparameter
of theopen_file_cachedirective, required for a file
descriptor to remain open in the cache.

`number``inactive`Syntax:open_file_cache_validtime;Default:open_file_cache_valid 60s;Context:http,server,location

`open_file_cache_valid time;``time`
```
open_file_cache_valid 60s;
```
`http``server``location`Sets a time after whichopen_file_cacheelements should be validated.

Syntax:output_buffersnumbersize;Default:output_buffers 2 32k;Context:http,server,location

`output_buffers number size;``number``size`
```
output_buffers 2 32k;
```
`http``server``location`Sets thenumberandsizeof the
buffers used for reading a response from a disk.

`number``size`
> Prior to version 1.9.5, the default value was 1 32k.
Syntax:port_in_redirecton|off;Default:port_in_redirect on;Context:http,server,location

`port_in_redirect on | off;``on``off`
```
port_in_redirect on;
```
`http``server``location`Enables or disables specifying the port inabsoluteredirects issued by nginx.

The use of the primary server name in redirects is controlled by
theserver_name_in_redirectdirective.

Syntax:postpone_outputsize;Default:postpone_output 1460;Context:http,server,location

`postpone_output size;``size`
```
postpone_output 1460;
```
`http``server``location`If possible, the transmission of client data will be postponed until
nginx has at leastsizebytes of data to send.
The zero value disables postponing data transmission.

`size`Syntax:read_aheadsize;Default:read_ahead 0;Context:http,server,location

`read_ahead size;``size`
```
read_ahead 0;
```
`http``server``location`Sets the amount of pre-reading for the kernel when working with file.

On Linux, theposix_fadvise(0, 0, 0, POSIX_FADV_SEQUENTIAL)system call is used, and so thesizeparameter is ignored.

`posix_fadvise(0, 0, 0, POSIX_FADV_SEQUENTIAL)``size`On FreeBSD, thefcntl(O_READAHEAD,size)system call, supported since FreeBSD 9.0-CURRENT, is used.
FreeBSD 7 has to bepatched.

`fcntl(O_READAHEAD,``size``)`Syntax:recursive_error_pageson|off;Default:recursive_error_pages off;Context:http,server,location

`recursive_error_pages on | off;``on``off`
```
recursive_error_pages off;
```
`http``server``location`Enables or disables doing several redirects using theerror_pagedirective.
The number of such redirects islimited.

Syntax:request_pool_sizesize;Default:request_pool_size 4k;Context:http,server

`request_pool_size size;``size`
```
request_pool_size 4k;
```
`http``server`Allows accurate tuning of per-request memory allocations.
This directive has minimal impact on performance
and should not generally be used.

Syntax:reset_timedout_connectionon|off;Default:reset_timedout_connection off;Context:http,server,location

`reset_timedout_connection on | off;``on``off`
```
reset_timedout_connection off;
```
`http``server``location`Enables or disables resetting timed out connections
and connectionsclosedwith the non-standard code 444 (1.15.2).
The reset is performed as follows.
Before closing a socket, theSO_LINGERoption is set on it with a timeout value of 0.
When the socket is closed, TCP RST is sent to the client, and all memory
occupied by this socket is released.
This helps avoid keeping an already closed socket with filled buffers
in a FIN_WAIT1 state for a long time.

`SO_LINGER`It should be noted that timed out keep-alive connections are
closed normally.

Syntax:resolveraddress...
 [valid=time]
 [ipv4=on|off]
 [ipv6=on|off]
 [status_zone=zone];Default:—Context:http,server,location

`resolver
address ...
 [valid=time]
 [ipv4=on|off]
 [ipv6=on|off]
 [status_zone=zone];``address``valid``time``ipv4``on``off``ipv6``on``off``status_zone``zone``http``server``location`Configures name servers used to resolve names of upstream servers
into addresses, for example:

> resolver 127.0.0.1 [::1]:5353;

```
resolver 127.0.0.1 [::1]:5353;
```
The address can be specified as a domain name or IP address,
with an optional port (1.3.1, 1.2.2).
If port is not specified, the port 53 is used.
Name servers are queried in a round-robin fashion.

> Before version 1.1.7, only a single name server could be configured.
Specifying name servers using IPv6 addresses is supported
starting from versions 1.3.1 and 1.2.2.
By default, nginx will look up both IPv4 and IPv6 addresses while resolving.
If looking up of IPv4 or IPv6 addresses is not desired,
theipv4=off(1.23.1) or
theipv6=offparameter can be specified.

`ipv4=off``ipv6=off`
> Resolving of names into IPv6 addresses is supported
starting from version 1.5.8.
By default, nginx caches answers using the TTL value of a response.
An optionalvalidparameter allows overriding it:

`valid`
> resolver 127.0.0.1 [::1]:5353 valid=30s;

```
resolver 127.0.0.1 [::1]:5353 valid=30s;
```

> Before version 1.1.9, tuning of caching time was not possible,
and nginx always cached answers for the duration of 5 minutes.

> To prevent DNS spoofing, it is recommended
configuring DNS servers in a properly secured trusted local network.
The optionalstatus_zoneparameter (1.17.1)
enablescollectionof DNS server statistics of requests and responses
in the specifiedzone.
The parameter is available as part of ourcommercial subscription.

`status_zone``zone`Syntax:resolver_timeouttime;Default:resolver_timeout 30s;Context:http,server,location

`resolver_timeout time;``time`
```
resolver_timeout 30s;
```
`http``server``location`Sets a timeout for name resolution, for example:

> resolver_timeout 5s;

```
resolver_timeout 5s;
```
Syntax:rootpath;Default:root html;Context:http,server,location,if in location

`root path;``path`
```
root html;
```
`http``server``location``if in location`Sets the root directory for requests.
For example, with the following configuration

> location /i/ {
 root /data/w3;
}

```
location /i/ {
 root /data/w3;
}
```
The/data/w3/i/top.giffile will be sent in response to
the “/i/top.gif” request.

`/data/w3/i/top.gif``/i/top.gif`Thepathvalue can contain variables,
except$document_rootand$realpath_root.

`path``$document_root``$realpath_root`A path to the file is constructed by merely adding a URI to the value
of therootdirective.
If a URI has to be modified, thealiasdirective should be used.

`root`Syntax:satisfyall|any;Default:satisfy all;Context:http,server,location

`satisfy all | any;``all``any`
```
satisfy all;
```
`http``server``location`Allows access if all (all) or at least one
(any) of thengx_http_access_module,ngx_http_auth_basic_module,ngx_http_auth_request_module,ngx_http_auth_jwt_module(1.13.10),
orngx_http_auth_oidc_module(1.27.4)
modules allow access.

`all``any`
> location / {
 satisfy any;

 allow 192.168.1.0/32;
 deny all;

 auth_basic "closed site";
 auth_basic_user_file conf/htpasswd;
}

```
location / {
 satisfy any;

 allow 192.168.1.0/32;
 deny all;

 auth_basic "closed site";
 auth_basic_user_file conf/htpasswd;
}
```
Syntax:send_lowatsize;Default:send_lowat 0;Context:http,server,location

`send_lowat size;``size`
```
send_lowat 0;
```
`http``server``location`If the directive is set to a non-zero value, nginx will try to minimize
the number of send operations on client sockets by using eitherNOTE_LOWATflag of thekqueuemethod
or theSO_SNDLOWATsocket option.
In both cases the specifiedsizeis used.

`NOTE_LOWAT``SO_SNDLOWAT``size`This directive is ignored on Linux, Solaris, and Windows.

Syntax:send_timeouttime;Default:send_timeout 60s;Context:http,server,location

`send_timeout time;``time`
```
send_timeout 60s;
```
`http``server``location`Sets a timeout for transmitting a response to the client.
The timeout is set only between two successive write operations,
not for the transmission of the whole response.
If the client does not receive anything within this time,
the connection is closed.

Syntax:sendfileon|off;Default:sendfile off;Context:http,server,location,if in location

`sendfile on | off;``on``off`
```
sendfile off;
```
`http``server``location``if in location`Enables or disables the use ofsendfile().

`sendfile()`Starting from nginx 0.8.12 and FreeBSD 5.2.1,aiocan be used to pre-load data
forsendfile():

`sendfile()`
> location /video/ {
 sendfile on;
 tcp_nopush on;
 aio on;
}

```
location /video/ {
 sendfile on;
 tcp_nopush on;
 aio on;
}
```
In this configuration,sendfile()is called with
theSF_NODISKIOflag which causes it not to block on disk I/O,
but, instead, report back that the data are not in memory.
nginx then initiates an asynchronous data load by reading one byte.
On the first read, the FreeBSD kernel loads the first 128K bytes
of a file into memory, although next reads will only load data in 16K chunks.
This can be changed using theread_aheaddirective.

`sendfile()``SF_NODISKIO`
> Before version 1.7.11, pre-loading could be enabled withaio sendfile;.
`aio sendfile;`Syntax:sendfile_max_chunksize;Default:sendfile_max_chunk 2m;Context:http,server,location

`sendfile_max_chunk size;``size`
```
sendfile_max_chunk 2m;
```
`http``server``location`Limits the amount of data that can be
transferred in a singlesendfile()call.
Without the limit, one fast connection may seize the worker process entirely.

`sendfile()`
> Prior to version 1.21.4, by default there was no limit.
Syntax:server{ ... }Default:—Context:http

`server { ... }``http`Sets configuration for a virtual server.
There is no clear separation between IP-based (based on the IP address)
and name-based (based on the “Host” request header field)
virtual servers.
Instead, thelistendirectives describe all
addresses and ports that should accept connections for the server, and theserver_namedirective lists all server names.
Example configurations are provided in the
“How nginx processes a request” document.

Syntax:server_namename...;Default:server_name "";Context:server

`server_name name ...;``name`
```
server_name "";
```
`server`Sets names of a virtual server, for example:

> server {
 server_name example.com www.example.com;
}

```
server {
 server_name example.com www.example.com;
}
```
The first name becomes the primary server name.

Server names can include an asterisk (“*”)
replacing the first or last part of a name:

`*`
> server {
 server_name example.com *.example.com www.example.*;
}

```
server {
 server_name example.com *.example.com www.example.*;
}
```
Such names are called wildcard names.

The first two of the names mentioned above can be combined in one:

> server {
 server_name .example.com;
}

```
server {
 server_name .example.com;
}
```
It is also possible to use regular expressions in server names,
preceding the name with a tilde (“~”):

`~`
> server {
 server_name www.example.com ~^www\d+\.example\.com$;
}

```
server {
 server_name www.example.com ~^www\d+\.example\.com$;
}
```
Regular expressions can contain captures (0.7.40) that can later
be used in other directives:

> server {
 server_name ~^(www\.)?(.+)$;

 location / {
 root /sites/$2;
 }
}

server {
 server_name _;

 location / {
 root /sites/default;
 }
}

```
server {
 server_name ~^(www\.)?(.+)$;

 location / {
 root /sites/$2;
 }
}

server {
 server_name _;

 location / {
 root /sites/default;
 }
}
```
Named captures in regular expressions create variables (0.8.25)
that can later be used in other directives:

> server {
 server_name ~^(www\.)?(?<domain>.+)$;

 location / {
 root /sites/$domain;
 }
}

server {
 server_name _;

 location / {
 root /sites/default;
 }
}

```
server {
 server_name ~^(www\.)?(?<domain>.+)$;

 location / {
 root /sites/$domain;
 }
}

server {
 server_name _;

 location / {
 root /sites/default;
 }
}
```
If the directive’s parameter is set to “$hostname” (0.9.4), the
machine’s hostname is inserted.

`$hostname`It is also possible to specify an empty server name (0.7.11):

> server {
 server_name www.example.com "";
}

```
server {
 server_name www.example.com "";
}
```
It allows this server to process requests without the “Host”
header field — instead of the default server — for the given address:port pair.
This is the default setting.

> Before 0.8.48, the machine’s hostname was used by default.
During searching for a virtual server by name,
if the name matches more than one of the specified variants,
(e.g. both a wildcard name and regular expression match), the first matching
variant will be chosen, in the following order of priority:

• the exact name
• the longest wildcard name starting with an asterisk,
e.g. “*.example.com”
`*.example.com`• the longest wildcard name ending with an asterisk,
e.g. “mail.*”
`mail.*`• the first matching regular expression
(in order of appearance in the configuration file)
Detailed description of server names is provided in a separateServer namesdocument.

Syntax:server_name_in_redirecton|off;Default:server_name_in_redirect off;Context:http,server,location

`server_name_in_redirect on | off;``on``off`
```
server_name_in_redirect off;
```
`http``server``location`Enables or disables the use of the primary server name, specified by theserver_namedirective,
inabsoluteredirects issued by nginx.
When the use of the primary server name is disabled, the name from the
“Host” request header field is used.
If this field is not present, the IP address of the server is used.

The use of a port in redirects is controlled by
theport_in_redirectdirective.

Syntax:server_names_hash_bucket_sizesize;Default:server_names_hash_bucket_size 32|64|128;Context:http

`server_names_hash_bucket_size size;``size`
```
server_names_hash_bucket_size 32|64|128;
```
`http`Sets the bucket size for the server names hash tables.
The default value depends on the size of the processor’s cache line.
The details of setting up hash tables are provided in a separatedocument.

Syntax:server_names_hash_max_sizesize;Default:server_names_hash_max_size 512;Context:http

`server_names_hash_max_size size;``size`
```
server_names_hash_max_size 512;
```
`http`Sets the maximumsizeof the server names hash tables.
The details of setting up hash tables are provided in a separatedocument.

`size`Syntax:server_tokenson|off|build|string;Default:server_tokens on;Context:http,server,location

`server_tokens
on |
 off |
 build |
 string;``on``off``build``string`
```
server_tokens on;
```
`http``server``location`Enables or disables emitting nginx version on error pages and in the
“Server” response header field.

Thebuildparameter (1.11.10) enables emitting
abuild namealong with nginx version.

`build`Additionally, as part of ourcommercial subscription,
starting from version 1.9.13
the signature on error pages and
the “Server” response header field value
can be set explicitly using thestringwith variables.
An empty string disables the emission of the “Server” field.

`string`Syntax:subrequest_output_buffer_sizesize;Default:subrequest_output_buffer_size 4k|8k;Context:http,server,locationThis directive appeared in version 1.13.10.

`subrequest_output_buffer_size size;``size`
```
subrequest_output_buffer_size 4k|8k;
```
`http``server``location`This directive appeared in version 1.13.10.

Sets thesizeof the buffer used for
storing the response body of a subrequest.
By default, the buffer size is equal to one memory page.
This is either 4K or 8K, depending on a platform.
It can be made smaller, however.

`size`The directive is applicable only for subrequests
with response bodies saved into memory.
For example, such subrequests are created bySSI.

Syntax:tcp_nodelayon|off;Default:tcp_nodelay on;Context:http,server,location

`tcp_nodelay on | off;``on``off`
```
tcp_nodelay on;
```
`http``server``location`Enables or disables the use of theTCP_NODELAYoption.
The option is enabled when a connection is transitioned into the
keep-alive state.
Additionally, it is enabled on SSL connections,
for unbuffered proxying,
and forWebSocketproxying.

`TCP_NODELAY`Syntax:tcp_nopushon|off;Default:tcp_nopush off;Context:http,server,location

`tcp_nopush on | off;``on``off`
```
tcp_nopush off;
```
`http``server``location`Enables or disables the use of
theTCP_NOPUSHsocket option on FreeBSD
or theTCP_CORKsocket option on Linux.
The options are enabled only whensendfileis used.
Enabling the option allows

`TCP_NOPUSH``TCP_CORK`• sending the response header and the beginning of a file in one packet,
on Linux and FreeBSD 4.*;
• sending a file in full packets.
Syntax:try_filesfile...uri;try_filesfile... =code;Default:—Context:server,location

`try_files file ... uri;``file``uri``try_files file ... =code;``file``code``server``location`Checks the existence of files in the specified order and uses
the first found file for request processing; the processing
is performed in the current context.
The path to a file is constructed from thefileparameter
according to therootandaliasdirectives.
It is possible to check directory’s existence by specifying
a slash at the end of a name, e.g. “$uri/”.
If none of the files were found, an internal redirect to theurispecified in the last parameter is made.
For example:

`file``$uri/``uri`
> location /images/ {
 try_files $uri /images/default.gif;
}

location = /images/default.gif {
 expires 30s;
}

```
location /images/ {
 try_files $uri /images/default.gif;
}

location = /images/default.gif {
 expires 30s;
}
```
The last parameter can also point to a named location,
as shown in examples below.
Starting from version 0.7.51, the last parameter can also be acode:

`code`
> location / {
 try_files $uri $uri/index.html $uri.html =404;
}

```
location / {
 try_files $uri $uri/index.html $uri.html =404;
}
```
Example in proxying Mongrel:

> location / {
 try_files /system/maintenance.html
 $uri $uri/index.html $uri.html
 @mongrel;
}

location @mongrel {
 proxy_pass http://mongrel;
}

```
location / {
 try_files /system/maintenance.html
 $uri $uri/index.html $uri.html
 @mongrel;
}

location @mongrel {
 proxy_pass http://mongrel;
}
```
Example for Drupal/FastCGI:

> location / {
 try_files $uri $uri/ @drupal;
}

location ~ \.php$ {
 try_files $uri @drupal;

 fastcgi_pass ...;

 fastcgi_param SCRIPT_FILENAME /path/to$fastcgi_script_name;
 fastcgi_param SCRIPT_NAME $fastcgi_script_name;
 fastcgi_param QUERY_STRING $args;

 ... other fastcgi_param's
}

location @drupal {
 fastcgi_pass ...;

 fastcgi_param SCRIPT_FILENAME /path/to/index.php;
 fastcgi_param SCRIPT_NAME /index.php;
 fastcgi_param QUERY_STRING q=$uri&$args;

 ... other fastcgi_param's
}

```
location / {
 try_files $uri $uri/ @drupal;
}

location ~ \.php$ {
 try_files $uri @drupal;

 fastcgi_pass ...;

 fastcgi_param SCRIPT_FILENAME /path/to$fastcgi_script_name;
 fastcgi_param SCRIPT_NAME $fastcgi_script_name;
 fastcgi_param QUERY_STRING $args;

 ... other fastcgi_param's
}

location @drupal {
 fastcgi_pass ...;

 fastcgi_param SCRIPT_FILENAME /path/to/index.php;
 fastcgi_param SCRIPT_NAME /index.php;
 fastcgi_param QUERY_STRING q=$uri&$args;

 ... other fastcgi_param's
}
```
In the following example,

> location / {
 try_files $uri $uri/ @drupal;
}

```
location / {
 try_files $uri $uri/ @drupal;
}
```
thetry_filesdirective is equivalent to

`try_files`
> location / {
 error_page 404 = @drupal;
 log_not_found off;
}

```
location / {
 error_page 404 = @drupal;
 log_not_found off;
}
```

> location ~ \.php$ {
 try_files $uri @drupal;

 fastcgi_pass ...;

 fastcgi_param SCRIPT_FILENAME /path/to$fastcgi_script_name;

 ...
}

```
location ~ \.php$ {
 try_files $uri @drupal;

 fastcgi_pass ...;

 fastcgi_param SCRIPT_FILENAME /path/to$fastcgi_script_name;

 ...
}
```
try_fileschecks the existence of the PHP file
before passing the request to the FastCGI server.

`try_files`Example for Wordpress and Joomla:

> location / {
 try_files $uri $uri/ @wordpress;
}

location ~ \.php$ {
 try_files $uri @wordpress;

 fastcgi_pass ...;

 fastcgi_param SCRIPT_FILENAME /path/to$fastcgi_script_name;
 ... other fastcgi_param's
}

location @wordpress {
 fastcgi_pass ...;

 fastcgi_param SCRIPT_FILENAME /path/to/index.php;
 ... other fastcgi_param's
}

```
location / {
 try_files $uri $uri/ @wordpress;
}

location ~ \.php$ {
 try_files $uri @wordpress;

 fastcgi_pass ...;

 fastcgi_param SCRIPT_FILENAME /path/to$fastcgi_script_name;
 ... other fastcgi_param's
}

location @wordpress {
 fastcgi_pass ...;

 fastcgi_param SCRIPT_FILENAME /path/to/index.php;
 ... other fastcgi_param's
}
```
Syntax:types{ ... }Default:types {
 text/html html;
 image/gif gif;
 image/jpeg jpg;
}Context:http,server,location

`types { ... }`
```
types {
 text/html html;
 image/gif gif;
 image/jpeg jpg;
}
```
`http``server``location`Maps file name extensions to MIME types of responses.
Extensions are case-insensitive.
Several extensions can be mapped to one type, for example:

> types {
 application/octet-stream bin exe dll;
 application/octet-stream deb;
 application/octet-stream dmg;
}

```
types {
 application/octet-stream bin exe dll;
 application/octet-stream deb;
 application/octet-stream dmg;
}
```
A sufficiently full mapping table is distributed with nginx in theconf/mime.typesfile.

`conf/mime.types`To make a particular location emit the
“application/octet-stream”
MIME type for all requests, the following configuration can be used:

`application/octet-stream`
> location /download/ {
 types { }
 default_type application/octet-stream;
}

```
location /download/ {
 types { }
 default_type application/octet-stream;
}
```
Syntax:types_hash_bucket_sizesize;Default:types_hash_bucket_size 64;Context:http,server,location

`types_hash_bucket_size size;``size`
```
types_hash_bucket_size 64;
```
`http``server``location`Sets the bucket size for the types hash tables.
The details of setting up hash tables are provided in a separatedocument.

> Prior to version 1.5.13,
the default value depended on the size of the processor’s cache line.
Syntax:types_hash_max_sizesize;Default:types_hash_max_size 1024;Context:http,server,location

`types_hash_max_size size;``size`
```
types_hash_max_size 1024;
```
`http``server``location`Sets the maximumsizeof the types hash tables.
The details of setting up hash tables are provided in a separatedocument.

`size`Syntax:underscores_in_headerson|off;Default:underscores_in_headers off;Context:http,server

`underscores_in_headers on | off;``on``off`
```
underscores_in_headers off;
```
`http``server`Enables or disables the use of underscores in client request header fields.
When the use of underscores is disabled, request header fields whose names
contain underscores are
marked as invalid and become subject to theignore_invalid_headersdirective.

If the directive is specified on theserverlevel,
the value from the default server can be used.
Details are provided in the
“Virtual
server selection” section.

Syntax:variables_hash_bucket_sizesize;Default:variables_hash_bucket_size 64;Context:http

`variables_hash_bucket_size size;``size`
```
variables_hash_bucket_size 64;
```
`http`Sets the bucket size for the variables hash table.
The details of setting up hash tables are provided in a separatedocument.

Syntax:variables_hash_max_sizesize;Default:variables_hash_max_size 1024;Context:http

`variables_hash_max_size size;``size`
```
variables_hash_max_size 1024;
```
`http`Sets the maximumsizeof the variables hash table.
The details of setting up hash tables are provided in a separatedocument.

`size`
> Prior to version 1.5.13, the default value was 512.

#### Embedded Variables
Thengx_http_core_modulemodule supports embedded variables
with names matching the Apache Server variables.
First of all, these are variables representing client request header
fields, such as$http_user_agent,$http_cookie,
and so on.
Also there are other variables:

`ngx_http_core_module``$http_user_agent``$http_cookie``$arg_``name``name``$args``$binary_remote_addr``$body_bytes_sent``%B``mod_log_config``$bytes_sent``$connection``$connection_requests``$connection_time``$content_length``$content_type``$cookie_``name``name``$document_root``$document_uri``$uri``$host``$hostname``$http_``name``$https``on``$is_args``?``$limit_rate``$msec``$nginx_version``$pid``$pipe``p``.``$proxy_protocol_addr`The PROXY protocol must be previously enabled by setting theproxy_protocolparameter
in thelistendirective.

`proxy_protocol``$proxy_protocol_port`The PROXY protocol must be previously enabled by setting theproxy_protocolparameter
in thelistendirective.

`proxy_protocol``$proxy_protocol_server_addr`The PROXY protocol must be previously enabled by setting theproxy_protocolparameter
in thelistendirective.

`proxy_protocol``$proxy_protocol_server_port`The PROXY protocol must be previously enabled by setting theproxy_protocolparameter
in thelistendirective.

`proxy_protocol``$proxy_protocol_tlv_``name``name``0x`
> $proxy_protocol_tlv_alpn
$proxy_protocol_tlv_0x01

```
$proxy_protocol_tlv_alpn
$proxy_protocol_tlv_0x01
```
`ssl_`
> $proxy_protocol_tlv_ssl_version
$proxy_protocol_tlv_ssl_0x21

```
$proxy_protocol_tlv_ssl_version
$proxy_protocol_tlv_ssl_0x21
```
The following TLV type names are supported:

• alpn(0x01) - 
upper layer protocol used over the connection
`alpn``0x01`• authority(0x02) - 
host name value passed by the client
`authority``0x02`• unique_id(0x05) - 
unique connection id
`unique_id``0x05`• netns(0x30) - 
name of the namespace
`netns``0x30`• ssl(0x20) - 
binary SSL TLV structure
`ssl``0x20`The following SSL TLV type names are supported:

• ssl_version(0x21) - 
SSL version used in client connection
`ssl_version``0x21`• ssl_cn(0x22) - 
SSL certificate Common Name
`ssl_cn``0x22`• ssl_cipher(0x23) - 
name of the used cipher
`ssl_cipher``0x23`• ssl_sig_alg(0x24) - 
algorithm used to sign the certificate
`ssl_sig_alg``0x24`• ssl_key_alg(0x25) - 
public-key algorithm
`ssl_key_alg``0x25`Also, the following special SSL TLV type name is supported:

• ssl_verify- 
client SSL certificate verification result,0if the client presented a certificate
and it was successfully verified,
non-zero otherwise.
`ssl_verify``0`The PROXY protocol must be previously enabled by setting theproxy_protocolparameter
in thelistendirective.

`proxy_protocol``$query_string``$args``$realpath_root``$remote_addr``$remote_port``$remote_user``$request``$request_body`The variable’s value is made available in locations
processed by theproxy_pass,fastcgi_pass,uwsgi_pass,
andscgi_passdirectives when the request body was read to
amemory buffer.

`$request_body_file`At the end of processing, the file needs to be removed.
To always write the request body to a file,client_body_in_file_onlyneeds to be enabled.
When the name of a temporary file is passed in a proxied request
or in a request to a FastCGI/uwsgi/SCGI server,
passing the request body should be disabled by theproxy_pass_request_body off,fastcgi_pass_request_body off,uwsgi_pass_request_body off, orscgi_pass_request_body offdirectives, respectively.

`$request_completion``OK``$request_filename``$request_id``$request_length``$request_method``GET``POST``$request_time``$request_uri``$scheme``http``https``$sent_http_``name``$sent_trailer_``name``$server_addr`Computing a value of this variable usually requires one system call.
To avoid a system call, thelistendirectives
must specify addresses and use thebindparameter.

`bind``$server_name``$server_port``$server_protocol``HTTP/1.0``HTTP/1.1``$status``$tcpinfo_rtt``$tcpinfo_rttvar``$tcpinfo_snd_cwnd``$tcpinfo_rcv_space``TCP_INFO``$time_iso8601``$time_local``$uri`The value of$urimay change during request processing,
e.g. when doing internal redirects, or when using index files.

`$uri`

---

## 10. Module ngx_http_ssl_module

**Source:** https://nginx.org/en/docs/http/ngx_http_ssl_module.html
**Word Count:** 2,468

### Content:

## Module ngx_http_ssl_module
Thengx_http_ssl_modulemodule provides the
necessary support for HTTPS.

`ngx_http_ssl_module`This module is not built by default, it should be enabled with the--with-http_ssl_moduleconfiguration parameter.

`--with-http_ssl_module`
> This module requires theOpenSSLlibrary.

#### Example Configuration
To reduce the processor load, it is recommended to

• set the number ofworker processesequal to the number of processors,
• enablekeep-aliveconnections,
• enable thesharedsession cache,
• disable thebuilt-insession cache,
• and possibly increase the sessionlifetime(by default, 5 minutes):

> worker_processes auto;http {

 ...

 server {
 listen 443 ssl;keepalive_timeout 70;ssl_protocols TLSv1.2 TLSv1.3;
 ssl_ciphers AES128-SHA:AES256-SHA:RC4-SHA:DES-CBC3-SHA:RC4-MD5;
 ssl_certificate /usr/local/nginx/conf/cert.pem;
 ssl_certificate_key /usr/local/nginx/conf/cert.key;ssl_session_cache shared:SSL:10m;ssl_session_timeout 10m;...
 }

```
worker_processes auto;

http {

 ...

 server {
 listen 443 ssl;
 keepalive_timeout 70;

 ssl_protocols TLSv1.2 TLSv1.3;
 ssl_ciphers AES128-SHA:AES256-SHA:RC4-SHA:DES-CBC3-SHA:RC4-MD5;
 ssl_certificate /usr/local/nginx/conf/cert.pem;
 ssl_certificate_key /usr/local/nginx/conf/cert.key;
 ssl_session_cache shared:SSL:10m;
 ssl_session_timeout 10m;

 ...
 }
```

#### Directives
Syntax:sslon|off;Default:ssl off;Context:http,server

`ssl on | off;``on``off`
```
ssl off;
```
`http``server`This directive was made obsolete in version 1.15.0
and was removed in version 1.25.1.
Thesslparameter
of thelistendirective
should be used instead.

`ssl`Syntax:ssl_buffer_sizesize;Default:ssl_buffer_size 16k;Context:http,serverThis directive appeared in version 1.5.9.

`ssl_buffer_size size;``size`
```
ssl_buffer_size 16k;
```
`http``server`This directive appeared in version 1.5.9.

Sets the size of the buffer used for sending data.

By default, the buffer size is 16k, which corresponds to minimal
overhead when sending big responses.
To minimize Time To First Byte it may be beneficial to use smaller values,
for example:

> ssl_buffer_size 4k;

```
ssl_buffer_size 4k;
```
Syntax:ssl_certificatefile;Default:—Context:http,server

`ssl_certificate file;``file``http``server`Specifies afilewith the certificate in the PEM format
for the given virtual server.
If intermediate certificates should be specified in addition to a primary
certificate, they should be specified in the same file in the following
order: the primary certificate comes first, then the intermediate certificates.
A secret key in the PEM format may be placed in the same file.

`file`Since version 1.11.0,
this directive can be specified multiple times
to load certificates of different types, for example, RSA and ECDSA:

> server {
 listen 443 ssl;
 server_name example.com;

 ssl_certificate example.com.rsa.crt;
 ssl_certificate_key example.com.rsa.key;

 ssl_certificate example.com.ecdsa.crt;
 ssl_certificate_key example.com.ecdsa.key;

 ...
}

```
server {
 listen 443 ssl;
 server_name example.com;

 ssl_certificate example.com.rsa.crt;
 ssl_certificate_key example.com.rsa.key;

 ssl_certificate example.com.ecdsa.crt;
 ssl_certificate_key example.com.ecdsa.key;

 ...
}
```

> Only OpenSSL 1.0.2 or higher supports separatecertificate chainsfor different certificates.
With older versions, only one certificate chain can be used.
Since version 1.15.9, variables can be used in thefilename
when using OpenSSL 1.0.2 or higher:

`file`
> ssl_certificate $ssl_server_name.crt;
ssl_certificate_key $ssl_server_name.key;

```
ssl_certificate $ssl_server_name.crt;
ssl_certificate_key $ssl_server_name.key;
```
Note that using variables implies that
a certificate will be loaded for each SSL handshake,
and this may have a negative impact on performance.

The valuedata:$variablecan be specified instead of thefile(1.15.10),
which loads a certificate from a variable
without using intermediate files.
Note that inappropriate use of this syntax may have its security implications,
such as writing secret key data toerror log.

`data``$variable``file`It should be kept in mind that due to the SSL/TLS protocol limitations,
for maximum interoperability with clients that do not useSNI,
virtual servers with different certificates should listen ondifferent
IP addresses.

Syntax:ssl_certificate_cacheoff;ssl_certificate_cachemax=N[inactive=time]
 [valid=time];Default:ssl_certificate_cache off;Context:http,serverThis directive appeared in version 1.27.4.

`ssl_certificate_cache off;``off``ssl_certificate_cache
max=N
 [inactive=time]
 [valid=time];``max``N``inactive``time``valid``time`
```
ssl_certificate_cache off;
```
`http``server`This directive appeared in version 1.27.4.

Defines a cache that storesSSL certificatesandsecret keysspecified withvariables.

The directive has the following parameters:

`max``inactive``valid``off`
> ssl_certificate $ssl_server_name.crt;
ssl_certificate_key $ssl_server_name.key;
ssl_certificate_cache max=1000 inactive=20s valid=1m;

```
ssl_certificate $ssl_server_name.crt;
ssl_certificate_key $ssl_server_name.key;
ssl_certificate_cache max=1000 inactive=20s valid=1m;
```
Syntax:ssl_certificate_keyfile;Default:—Context:http,server

`ssl_certificate_key file;``file``http``server`Specifies afilewith the secret key in the PEM format
for the given virtual server.

`file`The valueengine:name:idcan be specified instead of thefile(1.7.9),
which loads a secret key with a specifiedidfrom the OpenSSL enginename.

`engine``name``id``file``id``name`The valuestore:scheme:idcan be specified instead of thefile(1.29.0),
which is used to load a secret key with a specifiedidand OpenSSL provider registered URIscheme, such aspkcs11.

`store``scheme``id``file``id``scheme``pkcs11`The valuedata:$variablecan be specified instead of thefile(1.15.10),
which loads a secret key from a variable without using intermediate files.
Note that inappropriate use of this syntax may have its security implications,
such as writing secret key data toerror log.

`data``$variable``file`Since version 1.15.9, variables can be used in thefilename
when using OpenSSL 1.0.2 or higher.

`file`Syntax:ssl_ciphersciphers;Default:ssl_ciphers HIGH:!aNULL:!MD5;Context:http,server

`ssl_ciphers ciphers;``ciphers`
```
ssl_ciphers HIGH:!aNULL:!MD5;
```
`http``server`Specifies the enabled ciphers.
The ciphers are specified in the format understood by the
OpenSSL library, for example:

> ssl_ciphers ALL:!aNULL:!EXPORT56:RC4+RSA:+HIGH:+MEDIUM:+LOW:+SSLv2:+EXP;

```
ssl_ciphers ALL:!aNULL:!EXPORT56:RC4+RSA:+HIGH:+MEDIUM:+LOW:+SSLv2:+EXP;
```
The full list can be viewed using the
“openssl ciphers” command.

`openssl ciphers`
> The previous versions of nginx useddifferentciphers by default.
Syntax:ssl_client_certificatefile;Default:—Context:http,server

`ssl_client_certificate file;``file``http``server`Specifies afilewith trusted CA certificates in the PEM format
used toverifyclient certificates and
OCSP responses ifssl_staplingis enabled.

`file`The list of certificates will be sent to clients.
If this is not desired, thessl_trusted_certificatedirective can be used.

Syntax:ssl_conf_commandnamevalue;Default:—Context:http,serverThis directive appeared in version 1.19.4.

`ssl_conf_command name value;``name``value``http``server`This directive appeared in version 1.19.4.

Sets arbitrary OpenSSL configurationcommands.

> The directive is supported when using OpenSSL 1.0.2 or higher.
Severalssl_conf_commanddirectives
can be specified on the same level:

`ssl_conf_command`
> ssl_conf_command Options PrioritizeChaCha;
ssl_conf_command Ciphersuites TLS_CHACHA20_POLY1305_SHA256;

```
ssl_conf_command Options PrioritizeChaCha;
ssl_conf_command Ciphersuites TLS_CHACHA20_POLY1305_SHA256;
```
These directives are inherited from the previous configuration level
if and only if there are nossl_conf_commanddirectives
defined on the current level.

`ssl_conf_command`
> Note that configuring OpenSSL directly
might result in unexpected behavior.
Syntax:ssl_crlfile;Default:—Context:http,serverThis directive appeared in version 0.8.7.

`ssl_crl file;``file``http``server`This directive appeared in version 0.8.7.

Specifies afilewith revoked certificates (CRL)
in the PEM format used toverifyclient certificates.

`file`Syntax:ssl_dhparamfile;Default:—Context:http,serverThis directive appeared in version 0.7.2.

`ssl_dhparam file;``file``http``server`This directive appeared in version 0.7.2.

Specifies afilewith DH parameters for DHE ciphers.

`file`By default no parameters are set,
and therefore DHE ciphers will not be used.

> Prior to version 1.11.0, builtin parameters were used by default.
Syntax:ssl_early_dataon|off;Default:ssl_early_data off;Context:http,serverThis directive appeared in version 1.15.3.

`ssl_early_data on | off;``on``off`
```
ssl_early_data off;
```
`http``server`This directive appeared in version 1.15.3.

Enables or disables TLS 1.3early data.

> Requests sent within early data are subject toreplay attacks.
To protect against such attacks at the application layer,
the$ssl_early_datavariable
should be used.

> proxy_set_header Early-Data $ssl_early_data;

```
proxy_set_header Early-Data $ssl_early_data;
```

> The directive is supported when using OpenSSL 1.1.1 or higher (1.15.4) andBoringSSL.
Syntax:ssl_ecdh_curvecurve;Default:ssl_ecdh_curve auto;Context:http,serverThis directive appeared in versions 1.1.0 and 1.0.6.

`ssl_ecdh_curve curve;``curve`
```
ssl_ecdh_curve auto;
```
`http``server`This directive appeared in versions 1.1.0 and 1.0.6.

Specifies acurvefor ECDHE ciphers.

`curve`When using OpenSSL 1.0.2 or higher,
it is possible to specify multiple curves (1.11.0), for example:

> ssl_ecdh_curve prime256v1:secp384r1;

```
ssl_ecdh_curve prime256v1:secp384r1;
```
The special valueauto(1.11.0) instructs nginx to use
a list built into the OpenSSL library when using OpenSSL 1.0.2 or higher,
orprime256v1with older versions.

`auto``prime256v1`
> Prior to version 1.11.0,
theprime256v1curve was used by default.
`prime256v1`
> When using OpenSSL 1.0.2 or higher,
this directive sets the list of curves supported by the server.
Thus, in order for ECDSA certificates to work,
it is important to include the curves used in the certificates.
Syntax:ssl_key_logpath;Default:—Context:http,serverThis directive appeared in version 1.27.2.

`ssl_key_log path;``http``server`This directive appeared in version 1.27.2.

Enables logging of client connection SSL keys
and specifies the path to the key log file.
Keys are logged in theSSLKEYLOGFILEformat compatible with Wireshark.

> This directive is available as part of ourcommercial subscription.
Syntax:ssl_ocspon|off|leaf;Default:ssl_ocsp off;Context:http,serverThis directive appeared in version 1.19.0.

`ssl_ocsp on |
 off |
 leaf;``on``off``leaf`
```
ssl_ocsp off;
```
`http``server`This directive appeared in version 1.19.0.

Enables OCSP validation of the client certificate chain.
Theleafparameter
enables validation of the client certificate only.

`leaf`For the OCSP validation to work,
thessl_verify_clientdirective should be set toonoroptional.

`on``optional`To resolve the OCSP responder hostname,
theresolverdirective
should also be specified.

> ssl_verify_client on;
ssl_ocsp on;
resolver *********;

```
ssl_verify_client on;
ssl_ocsp on;
resolver *********;
```
Syntax:ssl_ocsp_cacheoff|
 [shared:name:size];Default:ssl_ocsp_cache off;Context:http,serverThis directive appeared in version 1.19.0.

`ssl_ocsp_cache
off |
 [shared:name:size];``off``shared``name``size`
```
ssl_ocsp_cache off;
```
`http``server`This directive appeared in version 1.19.0.

Setsnameandsizeof the cache
that stores client certificates status for OCSP validation.
The cache is shared between all worker processes.
A cache with the same name can be used in several
virtual servers.

`name``size`Theoffparameter prohibits the use of the cache.

`off`Syntax:ssl_ocsp_responderurl;Default:—Context:http,serverThis directive appeared in version 1.19.0.

`ssl_ocsp_responder url;``url``http``server`This directive appeared in version 1.19.0.

Overrides the URL of the OCSP responder specified in the
“Authority
Information Access” certificate extension
forvalidationof client certificates.

Only “http://” OCSP responders are supported:

`http://`
> ssl_ocsp_responder http://ocsp.example.com/;

```
ssl_ocsp_responder http://ocsp.example.com/;
```
Syntax:ssl_password_filefile;Default:—Context:http,serverThis directive appeared in version 1.7.3.

`ssl_password_file file;``file``http``server`This directive appeared in version 1.7.3.

Specifies afilewith passphrases forsecret keyswhere each passphrase is specified on a separate line.
Passphrases are tried in turn when loading the key.

`file`
> http {
 ssl_password_file /etc/keys/global.pass;
 ...

 server {
 server_name www1.example.com;
 ssl_certificate_key /etc/keys/first.key;
 }

 server {
 server_name www2.example.com;

 # named pipe can also be used instead of a file
 ssl_password_file /etc/keys/fifo;
 ssl_certificate_key /etc/keys/second.key;
 }
}

```
http {
 ssl_password_file /etc/keys/global.pass;
 ...

 server {
 server_name www1.example.com;
 ssl_certificate_key /etc/keys/first.key;
 }

 server {
 server_name www2.example.com;

 # named pipe can also be used instead of a file
 ssl_password_file /etc/keys/fifo;
 ssl_certificate_key /etc/keys/second.key;
 }
}
```
Syntax:ssl_prefer_server_cipherson|off;Default:ssl_prefer_server_ciphers off;Context:http,server

`ssl_prefer_server_ciphers on | off;``on``off`
```
ssl_prefer_server_ciphers off;
```
`http``server`Specifies that server ciphers should be preferred over client ciphers
when the SSLv3 and TLS protocols are used.

Syntax:ssl_protocols[SSLv2]
 [SSLv3]
 [TLSv1]
 [TLSv1.1]
 [TLSv1.2]
 [TLSv1.3];Default:ssl_protocols TLSv1.2 TLSv1.3;Context:http,server

`ssl_protocols 
 [SSLv2]
 [SSLv3]
 [TLSv1]
 [TLSv1.1]
 [TLSv1.2]
 [TLSv1.3];``SSLv2``SSLv3``TLSv1``TLSv1.1``TLSv1.2``TLSv1.3`
```
ssl_protocols TLSv1.2 TLSv1.3;
```
`http``server`Enables the specified protocols.

If the directive is specified
on theserverlevel,
the value from the default server can be used.
Details are provided in the
“Virtual
server selection” section.

> TheTLSv1.1andTLSv1.2parameters
(1.1.13, 1.0.12) work only when OpenSSL 1.0.1 or higher is used.
`TLSv1.1``TLSv1.2`
> TheTLSv1.3parameter (1.13.0) works only when
OpenSSL 1.1.1 or higher is used.
`TLSv1.3`
> TheTLSv1.3parameter is used by default
since 1.23.4.
`TLSv1.3`Syntax:ssl_reject_handshakeon|off;Default:ssl_reject_handshake off;Context:http,serverThis directive appeared in version 1.19.4.

`ssl_reject_handshake on | off;``on``off`
```
ssl_reject_handshake off;
```
`http``server`This directive appeared in version 1.19.4.

If enabled, SSL handshakes in
theserverblock will be rejected.

For example, in the following configuration, SSL handshakes with
server names other thanexample.comare rejected:

`example.com`
> server {
 listen 443 ssl default_server;
 ssl_reject_handshake on;
}

server {
 listen 443 ssl;
 server_name example.com;
 ssl_certificate example.com.crt;
 ssl_certificate_key example.com.key;
}

```
server {
 listen 443 ssl default_server;
 ssl_reject_handshake on;
}

server {
 listen 443 ssl;
 server_name example.com;
 ssl_certificate example.com.crt;
 ssl_certificate_key example.com.key;
}
```
Syntax:ssl_session_cacheoff|none|
 [builtin[:size]]
 [shared:name:size];Default:ssl_session_cache none;Context:http,server

`ssl_session_cache
off |
 none |
 [builtin[:size]]
 [shared:name:size];``off``none``builtin``size``shared``name``size`
```
ssl_session_cache none;
```
`http``server`Sets the types and sizes of caches that store session parameters.
A cache can be of any of the following types:

`off``none``builtin``shared`Both cache types can be used simultaneously, for example:

> ssl_session_cache builtin:1000 shared:SSL:10m;

```
ssl_session_cache builtin:1000 shared:SSL:10m;
```
but using only shared cache without the built-in cache should
be more efficient.

Syntax:ssl_session_ticket_keyfile;Default:—Context:http,serverThis directive appeared in version 1.5.7.

`ssl_session_ticket_key file;``file``http``server`This directive appeared in version 1.5.7.

Sets afilewith the secret key used to encrypt
and decrypt TLS session tickets.
The directive is necessary if the same key has to be shared between
multiple servers.
By default, a randomly generated key is used.

`file`If several keys are specified, only the first key is
used to encrypt TLS session tickets.
This allows configuring key rotation, for example:

> ssl_session_ticket_key current.key;
ssl_session_ticket_key previous.key;

```
ssl_session_ticket_key current.key;
ssl_session_ticket_key previous.key;
```
Thefilemust contain 80 or 48 bytes
of random data and can be created using the following command:

`file`
> openssl rand 80 > ticket.key

```
openssl rand 80 > ticket.key
```
Depending on the file size either AES256 (for 80-byte keys, 1.11.8)
or AES128 (for 48-byte keys) is used for encryption.

Syntax:ssl_session_ticketson|off;Default:ssl_session_tickets on;Context:http,serverThis directive appeared in version 1.5.9.

`ssl_session_tickets on | off;``on``off`
```
ssl_session_tickets on;
```
`http``server`This directive appeared in version 1.5.9.

Enables or disables session resumption throughTLS session tickets.

Syntax:ssl_session_timeouttime;Default:ssl_session_timeout 5m;Context:http,server

`ssl_session_timeout time;``time`
```
ssl_session_timeout 5m;
```
`http``server`Specifies a time during which a client may reuse the
session parameters.

Syntax:ssl_staplingon|off;Default:ssl_stapling off;Context:http,serverThis directive appeared in version 1.3.7.

`ssl_stapling on | off;``on``off`
```
ssl_stapling off;
```
`http``server`This directive appeared in version 1.3.7.

Enables or disablesstapling
of OCSP responsesby the server.
Example:

> ssl_stapling on;
resolver *********;

```
ssl_stapling on;
resolver *********;
```
For the OCSP stapling to work, the certificate of the server certificate
issuer should be known.
If thessl_certificatefile does
not contain intermediate certificates,
the certificate of the server certificate issuer should be
present in thessl_trusted_certificatefile.

For a resolution of the OCSP responder hostname,
theresolverdirective
should also be specified.

Syntax:ssl_stapling_filefile;Default:—Context:http,serverThis directive appeared in version 1.3.7.

`ssl_stapling_file file;``file``http``server`This directive appeared in version 1.3.7.

When set, the stapled OCSP response will be taken from the
specifiedfileinstead of querying
the OCSP responder specified in the server certificate.

`file`The file should be in the DER format as produced by the
“openssl ocsp” command.

`openssl ocsp`Syntax:ssl_stapling_responderurl;Default:—Context:http,serverThis directive appeared in version 1.3.7.

`ssl_stapling_responder url;``url``http``server`This directive appeared in version 1.3.7.

Overrides the URL of the OCSP responder specified in the
“Authority
Information Access” certificate extension.

Only “http://” OCSP responders are supported:

`http://`
> ssl_stapling_responder http://ocsp.example.com/;

```
ssl_stapling_responder http://ocsp.example.com/;
```
Syntax:ssl_stapling_verifyon|off;Default:ssl_stapling_verify off;Context:http,serverThis directive appeared in version 1.3.7.

`ssl_stapling_verify on | off;``on``off`
```
ssl_stapling_verify off;
```
`http``server`This directive appeared in version 1.3.7.

Enables or disables verification of OCSP responses by the server.

For verification to work, the certificate of the server certificate
issuer, the root certificate, and all intermediate certificates
should be configured as trusted using thessl_trusted_certificatedirective.

Syntax:ssl_trusted_certificatefile;Default:—Context:http,serverThis directive appeared in version 1.3.7.

`ssl_trusted_certificate file;``file``http``server`This directive appeared in version 1.3.7.

Specifies afilewith trusted CA certificates in the PEM format
used toverifyclient certificates and
OCSP responses ifssl_staplingis enabled.

`file`In contrast to the certificate set byssl_client_certificate,
the list of these certificates will not be sent to clients.

Syntax:ssl_verify_clienton|off|optional|optional_no_ca;Default:ssl_verify_client off;Context:http,server

`ssl_verify_client
on | off |
 optional | optional_no_ca;``on``off``optional``optional_no_ca`
```
ssl_verify_client off;
```
`http``server`Enables verification of client certificates.
The verification result is stored in the$ssl_client_verifyvariable.

Theoptionalparameter (0.8.7+) requests the client
certificate and verifies it if the certificate is present.

`optional`Theoptional_no_caparameter (1.3.8, 1.2.5)
requests the client
certificate but does not require it to be signed by a trusted CA certificate.
This is intended for the use in cases when a service that is external to nginx
performs the actual certificate verification.
The contents of the certificate is accessible through the$ssl_client_certvariable.

`optional_no_ca`Syntax:ssl_verify_depthnumber;Default:ssl_verify_depth 1;Context:http,server

`ssl_verify_depth number;``number`
```
ssl_verify_depth 1;
```
`http``server`Sets the verification depth in the client certificates chain.

#### Error Processing
Thengx_http_ssl_modulemodule supports several
non-standard error codes that can be used for redirects using theerror_pagedirective:

`ngx_http_ssl_module`The redirection happens after the request is fully parsed and
the variables, such as$request_uri,$uri,$argsand others, are available.

`$request_uri``$uri``$args`
#### Embedded Variables
Thengx_http_ssl_modulemodule supports
embedded variables:

`ngx_http_ssl_module``$ssl_alpn_protocol``$ssl_cipher``$ssl_ciphers`
> AES128-SHA:AES256-SHA:0x00ff

```
AES128-SHA:AES256-SHA:0x00ff
```

> The variable is fully supported only when using OpenSSL version 1.0.2 or higher.
With older versions, the variable is available
only for new sessions and lists only known ciphers.
`$ssl_client_escaped_cert``$ssl_client_cert`
> The variable is deprecated,
the$ssl_client_escaped_certvariable should be used instead.
`$ssl_client_escaped_cert``$ssl_client_fingerprint``$ssl_client_i_dn``$ssl_client_i_dn_legacy`
> Prior to version 1.11.6, the variable name was$ssl_client_i_dn.
`$ssl_client_i_dn``$ssl_client_raw_cert``$ssl_client_s_dn``$ssl_client_s_dn_legacy`
> Prior to version 1.11.6, the variable name was$ssl_client_s_dn.
`$ssl_client_s_dn``$ssl_client_serial``$ssl_client_v_end``$ssl_client_v_remain``$ssl_client_v_start``$ssl_client_verify``SUCCESS``FAILED:``reason``NONE`
> Prior to version 1.11.7, the “FAILED” result
did not contain thereasonstring.
`FAILED``reason``$ssl_curve`
> prime256v1

```
prime256v1
```

> The variable is supported only when using OpenSSL version 3.0 or higher.
With older versions, the variable value will be an empty string.
`$ssl_curves`
> 0x001d:prime256v1:secp521r1:secp384r1

```
0x001d:prime256v1:secp521r1:secp384r1
```

> The variable is supported only when using OpenSSL version 1.0.2 or higher.
With older versions, the variable value will be an empty string.

> The variable is available only for new sessions.
`$ssl_early_data``1``$ssl_protocol``$ssl_server_name``$ssl_session_id``$ssl_session_reused``r``.`

---

## 11. Controlling

**Source:** https://nginx.org/en/docs/control.html
**Word Count:** 1,674

### Content:

## Controlling nginx
nginx can be controlled with signals.
The process ID of the master process is written to the file/usr/local/nginx/logs/nginx.pidby default.
This name may be changed at configuration time, or innginx.confusing thepiddirective.
The master process supports the following signals:

`/usr/local/nginx/logs/nginx.pid``nginx.conf`
> TERM, INTfast shutdownQUITgraceful shutdownHUPchanging configuration,
keeping up with a changed time zone (only for FreeBSD and Linux),
starting new worker processes with a new configuration,
graceful shutdown of old worker processesUSR1re-opening log filesUSR2upgrading an executable fileWINCHgraceful shutdown of worker processes
Individual worker processes can be controlled with signals as well,
though it is not required.
The supported signals are:

> TERM, INTfast shutdownQUITgraceful shutdownUSR1re-opening log filesWINCHabnormal termination for debugging
(requiresdebug_pointsto be enabled)

#### Changing Configuration
In order for nginx to re-read the configuration file, a HUP
signal should be sent to the master process.
The master process first checks the syntax validity, then tries
to apply new configuration, that is, to open log files and new
listen sockets.
If this fails, it rolls back changes and continues to work
with old configuration.
If this succeeds, it starts new worker processes, and
sends messages to old worker processes requesting them to
shut down gracefully.
Old worker processes close listen sockets and continue to service
old clients.
After all clients are serviced, old worker processes are shut down.

Let’s illustrate this by example.
Imagine that nginx is run on FreeBSD and the command

> ps axw -o pid,ppid,user,%cpu,vsz,wchan,command | egrep '(nginx|PID)'

```
ps axw -o pid,ppid,user,%cpu,vsz,wchan,command | egrep '(nginx|PID)'
```
produces the following output:

> PID PPID USER %CPU VSZ WCHAN COMMAND
33126 1 root 0.0 1148 pause nginx: master process /usr/local/nginx/sbin/nginx
33127 33126 nobody 0.0 1380 kqread nginx: worker process (nginx)
33128 33126 nobody 0.0 1364 kqread nginx: worker process (nginx)
33129 33126 nobody 0.0 1364 kqread nginx: worker process (nginx)

```
PID PPID USER %CPU VSZ WCHAN COMMAND
33126 1 root 0.0 1148 pause nginx: master process /usr/local/nginx/sbin/nginx
33127 33126 nobody 0.0 1380 kqread nginx: worker process (nginx)
33128 33126 nobody 0.0 1364 kqread nginx: worker process (nginx)
33129 33126 nobody 0.0 1364 kqread nginx: worker process (nginx)
```
If HUP is sent to the master process, the output becomes:

> PID PPID USER %CPU VSZ WCHAN COMMAND
33126 1 root 0.0 1164 pause nginx: master process /usr/local/nginx/sbin/nginx
33129 33126 nobody 0.0 1380 kqread nginx: worker process is shutting down (nginx)
33134 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
33135 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
33136 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)

```
PID PPID USER %CPU VSZ WCHAN COMMAND
33126 1 root 0.0 1164 pause nginx: master process /usr/local/nginx/sbin/nginx
33129 33126 nobody 0.0 1380 kqread nginx: worker process is shutting down (nginx)
33134 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
33135 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
33136 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
```
One of the old worker processes with PID 33129 still continues to work.
After some time it exits:

> PID PPID USER %CPU VSZ WCHAN COMMAND
33126 1 root 0.0 1164 pause nginx: master process /usr/local/nginx/sbin/nginx
33134 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
33135 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
33136 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)

```
PID PPID USER %CPU VSZ WCHAN COMMAND
33126 1 root 0.0 1164 pause nginx: master process /usr/local/nginx/sbin/nginx
33134 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
33135 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
33136 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
```

#### Rotating Log-files
In order to rotate log files, they need to be renamed first.
After that USR1 signal should be sent to the master process.
The master process will then re-open all currently open log files and
assign them an unprivileged user under which the worker processes
are running, as an owner.
After successful re-opening, the master process closes all open files and
sends the message to worker process to ask them to re-open files.
Worker processes also open new files and close old files right away.
As a result, old files are almost immediately available for post
processing, such as compression.

#### Upgrading Executable on the Fly
In order to upgrade the server executable, the new executable file
should be put in place of an old file first.
After that USR2 signal should be sent to the master process.
The master process first renames its file with the process ID to a
new file with the.oldbinsuffix, e.g./usr/local/nginx/logs/nginx.pid.oldbin,
then starts a new executable file that in turn starts new
worker processes:

`.oldbin``/usr/local/nginx/logs/nginx.pid.oldbin`
> PID PPID USER %CPU VSZ WCHAN COMMAND
33126 1 root 0.0 1164 pause nginx: master process /usr/local/nginx/sbin/nginx
33134 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
33135 33126 nobody 0.0 1380 kqread nginx: worker process (nginx)
33136 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
36264 33126 root 0.0 1148 pause nginx: master process /usr/local/nginx/sbin/nginx
36265 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36266 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36267 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)

```
PID PPID USER %CPU VSZ WCHAN COMMAND
33126 1 root 0.0 1164 pause nginx: master process /usr/local/nginx/sbin/nginx
33134 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
33135 33126 nobody 0.0 1380 kqread nginx: worker process (nginx)
33136 33126 nobody 0.0 1368 kqread nginx: worker process (nginx)
36264 33126 root 0.0 1148 pause nginx: master process /usr/local/nginx/sbin/nginx
36265 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36266 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36267 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
```
After that all worker processes (old and new ones) continue to accept requests.
If the WINCH signal is sent to the first master process, it will
send messages to its worker processes, requesting them to shut
down gracefully, and they will start to exit:

> PID PPID USER %CPU VSZ WCHAN COMMAND
33126 1 root 0.0 1164 pause nginx: master process /usr/local/nginx/sbin/nginx
33135 33126 nobody 0.0 1380 kqread nginx: worker process is shutting down (nginx)
36264 33126 root 0.0 1148 pause nginx: master process /usr/local/nginx/sbin/nginx
36265 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36266 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36267 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)

```
PID PPID USER %CPU VSZ WCHAN COMMAND
33126 1 root 0.0 1164 pause nginx: master process /usr/local/nginx/sbin/nginx
33135 33126 nobody 0.0 1380 kqread nginx: worker process is shutting down (nginx)
36264 33126 root 0.0 1148 pause nginx: master process /usr/local/nginx/sbin/nginx
36265 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36266 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36267 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
```
After some time, only the new worker processes will process requests:

> PID PPID USER %CPU VSZ WCHAN COMMAND
33126 1 root 0.0 1164 pause nginx: master process /usr/local/nginx/sbin/nginx
36264 33126 root 0.0 1148 pause nginx: master process /usr/local/nginx/sbin/nginx
36265 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36266 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36267 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)

```
PID PPID USER %CPU VSZ WCHAN COMMAND
33126 1 root 0.0 1164 pause nginx: master process /usr/local/nginx/sbin/nginx
36264 33126 root 0.0 1148 pause nginx: master process /usr/local/nginx/sbin/nginx
36265 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36266 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36267 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
```
It should be noted that the old master process does not close its listen
sockets, and it can be managed to start its worker processes again if needed.
If for some reason the new executable file works unacceptably, one of the
following can be done:

• Send the HUP signal to the old master process.
The old master process will start new worker processes
without re-reading the configuration.
After that, all new processes can be shut down gracefully,
by sending the QUIT signal to the new master process.
Send the HUP signal to the old master process.
The old master process will start new worker processes
without re-reading the configuration.
After that, all new processes can be shut down gracefully,
by sending the QUIT signal to the new master process.

• Send the TERM signal to the new master process.
It will then send a message to its worker processes requesting them
to exit immediately, and they will all exit almost immediately.
(If new processes do not exit for some reason,
the KILL signal should be sent to them to force them to exit.)
When the new master process exits, the old master process will start new
worker processes automatically.
Send the TERM signal to the new master process.
It will then send a message to its worker processes requesting them
to exit immediately, and they will all exit almost immediately.
(If new processes do not exit for some reason,
the KILL signal should be sent to them to force them to exit.)
When the new master process exits, the old master process will start new
worker processes automatically.

If the new master process exits then the old master process discards
the.oldbinsuffix from the file name with the process ID.

`.oldbin`If upgrade was successful, then the QUIT signal should be sent to
the old master process, and only new processes will stay:

> PID PPID USER %CPU VSZ WCHAN COMMAND
36264 1 root 0.0 1148 pause nginx: master process /usr/local/nginx/sbin/nginx
36265 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36266 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36267 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)

```
PID PPID USER %CPU VSZ WCHAN COMMAND
36264 1 root 0.0 1148 pause nginx: master process /usr/local/nginx/sbin/nginx
36265 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36266 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
36267 36264 nobody 0.0 1364 kqread nginx: worker process (nginx)
```

---

## 12. Command-line parameters

**Source:** https://nginx.org/en/docs/switches.html
**Word Count:** 237

### Content:

## Command-line parameters
nginx supports the following command-line parameters:

• -?|-h— print help
for command-line parameters.
`-?``-h`• -cfile— use an alternative
configurationfileinstead of a default file.
`-c file``file``file`• -efile— use an alternative
error logfileto store the log
instead of a default file (1.19.5).
The special valuestderrselects the standard error file.
`-e file``file``file``stderr`• -gdirectives— setglobal configuration directives,
for example,nginx -g "pid /var/run/nginx.pid; worker_processes `sysctl -n hw.ncpu`;"
`-g directives``directives`
> nginx -g "pid /var/run/nginx.pid; worker_processes `sysctl -n hw.ncpu`;"

```
nginx -g "pid /var/run/nginx.pid; worker_processes `sysctl -n hw.ncpu`;"
```
• -pprefix— set nginx path prefix,
i.e. a directory that will keep server files
(default value is/usr/local/nginx).
`-p prefix``prefix``/usr/local/nginx`• -q— suppress non-error messages
during configuration testing.
`-q`• -ssignal— send asignalto the master process.
The argumentsignalcan be one of:stop— shut down quicklyquit— shut down gracefullyreload— reload configuration,
start the new worker process with a new configuration,
gracefully shut down old worker processes.reopen— reopen log files
`-s signal``signal`• stop— shut down quickly
`stop`• quit— shut down gracefully
`quit`• reload— reload configuration,
start the new worker process with a new configuration,
gracefully shut down old worker processes.
`reload`• reopen— reopen log files
`reopen`• -t— test the configuration file: nginx checks the
configuration for correct syntax, and then tries to open files
referred in the configuration.
`-t`• -T— same as-t,
but additionally dump configuration files to standard output (1.9.2).
`-T``-t`• -v— print nginx version.
`-v`• -V— print nginx version, compiler version,
and configure parameters.
`-V`

---

## 13. Guide​

**Source:** https://nginxproxymanager.com/guide/
**Word Count:** 1,642

### Content:

Guide​This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.Quick SetupFull SetupScreenshotsProject Goal​I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.Features​Beautiful and Secure Admin Interface based onTablerEasily create forwarding domains, redirections, streams and 404 hosts without knowing anything about NginxFree SSL using Let's Encrypt or provide your own custom SSL certificatesAccess Lists and basic HTTP Authentication for your hostsAdvanced Nginx configuration available for super usersUser management, permissions and audit logHosting your home network​I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.Your home router will have a Port Forwarding section somewhere. Log in and find itAdd port forwarding for port 80 and 443 to the server hosting this projectConfigure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53Use the Nginx Proxy Manager as your gateway to forward to your other web based servicesQuick Setup​Install Docker and Docker-ComposeDocker Install documentationDocker-Compose Install documentationCreate a docker-compose.yml file similar to this:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThis is the bare minimum configuration required. See thedocumentationfor more.Bring up your stack by runningbashdocker-composeup-d# If using docker-compose-plugindockercomposeup-dLog in to the Admin UIWhen your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.http://127.0.0.1:81Default Admin User:Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password.Contributing​All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.comContributors​Special thanks toall of our contributors.Getting Support​Found a bug?DiscussionsReddit

Guide​This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.Quick SetupFull SetupScreenshotsProject Goal​I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.Features​Beautiful and Secure Admin Interface based onTablerEasily create forwarding domains, redirections, streams and 404 hosts without knowing anything about NginxFree SSL using Let's Encrypt or provide your own custom SSL certificatesAccess Lists and basic HTTP Authentication for your hostsAdvanced Nginx configuration available for super usersUser management, permissions and audit logHosting your home network​I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.Your home router will have a Port Forwarding section somewhere. Log in and find itAdd port forwarding for port 80 and 443 to the server hosting this projectConfigure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53Use the Nginx Proxy Manager as your gateway to forward to your other web based servicesQuick Setup​Install Docker and Docker-ComposeDocker Install documentationDocker-Compose Install documentationCreate a docker-compose.yml file similar to this:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThis is the bare minimum configuration required. See thedocumentationfor more.Bring up your stack by runningbashdocker-composeup-d# If using docker-compose-plugindockercomposeup-dLog in to the Admin UIWhen your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.http://127.0.0.1:81Default Admin User:Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password.Contributing​All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.comContributors​Special thanks toall of our contributors.Getting Support​Found a bug?DiscussionsReddit

Guide​This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.Quick SetupFull SetupScreenshotsProject Goal​I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.Features​Beautiful and Secure Admin Interface based onTablerEasily create forwarding domains, redirections, streams and 404 hosts without knowing anything about NginxFree SSL using Let's Encrypt or provide your own custom SSL certificatesAccess Lists and basic HTTP Authentication for your hostsAdvanced Nginx configuration available for super usersUser management, permissions and audit logHosting your home network​I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.Your home router will have a Port Forwarding section somewhere. Log in and find itAdd port forwarding for port 80 and 443 to the server hosting this projectConfigure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53Use the Nginx Proxy Manager as your gateway to forward to your other web based servicesQuick Setup​Install Docker and Docker-ComposeDocker Install documentationDocker-Compose Install documentationCreate a docker-compose.yml file similar to this:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThis is the bare minimum configuration required. See thedocumentationfor more.Bring up your stack by runningbashdocker-composeup-d# If using docker-compose-plugindockercomposeup-dLog in to the Admin UIWhen your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.http://127.0.0.1:81Default Admin User:Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password.Contributing​All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.comContributors​Special thanks toall of our contributors.Getting Support​Found a bug?DiscussionsReddit

# Guide​
This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.

• Quick Setup
• Full Setup
• Screenshots

## Project Goal​
I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.

## Features​
• Beautiful and Secure Admin Interface based onTabler
• Easily create forwarding domains, redirections, streams and 404 hosts without knowing anything about Nginx
• Free SSL using Let's Encrypt or provide your own custom SSL certificates
• Access Lists and basic HTTP Authentication for your hosts
• Advanced Nginx configuration available for super users
• User management, permissions and audit log

## Hosting your home network​
I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.

• Your home router will have a Port Forwarding section somewhere. Log in and find it
• Add port forwarding for port 80 and 443 to the server hosting this project
• Configure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53
• Use the Nginx Proxy Manager as your gateway to forward to your other web based services

## Quick Setup​
• Install Docker and Docker-Compose
• Docker Install documentation
• Docker-Compose Install documentation
• Create a docker-compose.yml file similar to this:
ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencrypt

```
services:
 app:
 image: 'jc21/nginx-proxy-manager:latest'
 restart: unless-stopped
 ports:
 - '80:80'
 - '81:81'
 - '443:443'
 volumes:
 - ./data:/data
 - ./letsencrypt:/etc/letsencrypt
```
This is the bare minimum configuration required. See thedocumentationfor more.

• Bring up your stack by running
bashdocker-composeup-d# If using docker-compose-plugindockercomposeup-d

```
docker-compose up -d

# If using docker-compose-plugin
docker compose up -d
```
• Log in to the Admin UI
When your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.

`81`http://127.0.0.1:81

Default Admin User:

Email: <EMAIL>: changeme

```
Email: <EMAIL>
Password: changeme
```
Immediately after logging in with this default user you will be asked to modify your details and change your password.

## Contributing​
All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.

`develop``master`CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.

Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.com

`develop`
### Contributors​
Special thanks toall of our contributors.

## Getting Support​
• Found a bug?
• Discussions
• Reddit

---

## 14. Guide​

**Source:** https://nginxproxymanager.com/guide/#quick-setup
**Word Count:** 1,642

### Content:

Guide​This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.Quick SetupFull SetupScreenshotsProject Goal​I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.Features​Beautiful and Secure Admin Interface based onTablerEasily create forwarding domains, redirections, streams and 404 hosts without knowing anything about NginxFree SSL using Let's Encrypt or provide your own custom SSL certificatesAccess Lists and basic HTTP Authentication for your hostsAdvanced Nginx configuration available for super usersUser management, permissions and audit logHosting your home network​I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.Your home router will have a Port Forwarding section somewhere. Log in and find itAdd port forwarding for port 80 and 443 to the server hosting this projectConfigure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53Use the Nginx Proxy Manager as your gateway to forward to your other web based servicesQuick Setup​Install Docker and Docker-ComposeDocker Install documentationDocker-Compose Install documentationCreate a docker-compose.yml file similar to this:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThis is the bare minimum configuration required. See thedocumentationfor more.Bring up your stack by runningbashdocker-composeup-d# If using docker-compose-plugindockercomposeup-dLog in to the Admin UIWhen your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.http://127.0.0.1:81Default Admin User:Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password.Contributing​All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.comContributors​Special thanks toall of our contributors.Getting Support​Found a bug?DiscussionsReddit

Guide​This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.Quick SetupFull SetupScreenshotsProject Goal​I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.Features​Beautiful and Secure Admin Interface based onTablerEasily create forwarding domains, redirections, streams and 404 hosts without knowing anything about NginxFree SSL using Let's Encrypt or provide your own custom SSL certificatesAccess Lists and basic HTTP Authentication for your hostsAdvanced Nginx configuration available for super usersUser management, permissions and audit logHosting your home network​I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.Your home router will have a Port Forwarding section somewhere. Log in and find itAdd port forwarding for port 80 and 443 to the server hosting this projectConfigure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53Use the Nginx Proxy Manager as your gateway to forward to your other web based servicesQuick Setup​Install Docker and Docker-ComposeDocker Install documentationDocker-Compose Install documentationCreate a docker-compose.yml file similar to this:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThis is the bare minimum configuration required. See thedocumentationfor more.Bring up your stack by runningbashdocker-composeup-d# If using docker-compose-plugindockercomposeup-dLog in to the Admin UIWhen your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.http://127.0.0.1:81Default Admin User:Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password.Contributing​All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.comContributors​Special thanks toall of our contributors.Getting Support​Found a bug?DiscussionsReddit

Guide​This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.Quick SetupFull SetupScreenshotsProject Goal​I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.Features​Beautiful and Secure Admin Interface based onTablerEasily create forwarding domains, redirections, streams and 404 hosts without knowing anything about NginxFree SSL using Let's Encrypt or provide your own custom SSL certificatesAccess Lists and basic HTTP Authentication for your hostsAdvanced Nginx configuration available for super usersUser management, permissions and audit logHosting your home network​I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.Your home router will have a Port Forwarding section somewhere. Log in and find itAdd port forwarding for port 80 and 443 to the server hosting this projectConfigure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53Use the Nginx Proxy Manager as your gateway to forward to your other web based servicesQuick Setup​Install Docker and Docker-ComposeDocker Install documentationDocker-Compose Install documentationCreate a docker-compose.yml file similar to this:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThis is the bare minimum configuration required. See thedocumentationfor more.Bring up your stack by runningbashdocker-composeup-d# If using docker-compose-plugindockercomposeup-dLog in to the Admin UIWhen your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.http://127.0.0.1:81Default Admin User:Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password.Contributing​All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.comContributors​Special thanks toall of our contributors.Getting Support​Found a bug?DiscussionsReddit

# Guide​
This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.

• Quick Setup
• Full Setup
• Screenshots

## Project Goal​
I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.

## Features​
• Beautiful and Secure Admin Interface based onTabler
• Easily create forwarding domains, redirections, streams and 404 hosts without knowing anything about Nginx
• Free SSL using Let's Encrypt or provide your own custom SSL certificates
• Access Lists and basic HTTP Authentication for your hosts
• Advanced Nginx configuration available for super users
• User management, permissions and audit log

## Hosting your home network​
I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.

• Your home router will have a Port Forwarding section somewhere. Log in and find it
• Add port forwarding for port 80 and 443 to the server hosting this project
• Configure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53
• Use the Nginx Proxy Manager as your gateway to forward to your other web based services

## Quick Setup​
• Install Docker and Docker-Compose
• Docker Install documentation
• Docker-Compose Install documentation
• Create a docker-compose.yml file similar to this:
ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencrypt

```
services:
 app:
 image: 'jc21/nginx-proxy-manager:latest'
 restart: unless-stopped
 ports:
 - '80:80'
 - '81:81'
 - '443:443'
 volumes:
 - ./data:/data
 - ./letsencrypt:/etc/letsencrypt
```
This is the bare minimum configuration required. See thedocumentationfor more.

• Bring up your stack by running
bashdocker-composeup-d# If using docker-compose-plugindockercomposeup-d

```
docker-compose up -d

# If using docker-compose-plugin
docker compose up -d
```
• Log in to the Admin UI
When your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.

`81`http://127.0.0.1:81

Default Admin User:

Email: <EMAIL>: changeme

```
Email: <EMAIL>
Password: changeme
```
Immediately after logging in with this default user you will be asked to modify your details and change your password.

## Contributing​
All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.

`develop``master`CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.

Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.com

`develop`
### Contributors​
Special thanks toall of our contributors.

## Getting Support​
• Found a bug?
• Discussions
• Reddit

---

## 15. Guide​

**Source:** https://nginxproxymanager.com/guide/#running-the-app
**Word Count:** 1,642

### Content:

Guide​This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.Quick SetupFull SetupScreenshotsProject Goal​I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.Features​Beautiful and Secure Admin Interface based onTablerEasily create forwarding domains, redirections, streams and 404 hosts without knowing anything about NginxFree SSL using Let's Encrypt or provide your own custom SSL certificatesAccess Lists and basic HTTP Authentication for your hostsAdvanced Nginx configuration available for super usersUser management, permissions and audit logHosting your home network​I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.Your home router will have a Port Forwarding section somewhere. Log in and find itAdd port forwarding for port 80 and 443 to the server hosting this projectConfigure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53Use the Nginx Proxy Manager as your gateway to forward to your other web based servicesQuick Setup​Install Docker and Docker-ComposeDocker Install documentationDocker-Compose Install documentationCreate a docker-compose.yml file similar to this:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThis is the bare minimum configuration required. See thedocumentationfor more.Bring up your stack by runningbashdocker-composeup-d# If using docker-compose-plugindockercomposeup-dLog in to the Admin UIWhen your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.http://127.0.0.1:81Default Admin User:Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password.Contributing​All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.comContributors​Special thanks toall of our contributors.Getting Support​Found a bug?DiscussionsReddit

Guide​This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.Quick SetupFull SetupScreenshotsProject Goal​I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.Features​Beautiful and Secure Admin Interface based onTablerEasily create forwarding domains, redirections, streams and 404 hosts without knowing anything about NginxFree SSL using Let's Encrypt or provide your own custom SSL certificatesAccess Lists and basic HTTP Authentication for your hostsAdvanced Nginx configuration available for super usersUser management, permissions and audit logHosting your home network​I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.Your home router will have a Port Forwarding section somewhere. Log in and find itAdd port forwarding for port 80 and 443 to the server hosting this projectConfigure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53Use the Nginx Proxy Manager as your gateway to forward to your other web based servicesQuick Setup​Install Docker and Docker-ComposeDocker Install documentationDocker-Compose Install documentationCreate a docker-compose.yml file similar to this:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThis is the bare minimum configuration required. See thedocumentationfor more.Bring up your stack by runningbashdocker-composeup-d# If using docker-compose-plugindockercomposeup-dLog in to the Admin UIWhen your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.http://127.0.0.1:81Default Admin User:Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password.Contributing​All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.comContributors​Special thanks toall of our contributors.Getting Support​Found a bug?DiscussionsReddit

Guide​This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.Quick SetupFull SetupScreenshotsProject Goal​I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.Features​Beautiful and Secure Admin Interface based onTablerEasily create forwarding domains, redirections, streams and 404 hosts without knowing anything about NginxFree SSL using Let's Encrypt or provide your own custom SSL certificatesAccess Lists and basic HTTP Authentication for your hostsAdvanced Nginx configuration available for super usersUser management, permissions and audit logHosting your home network​I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.Your home router will have a Port Forwarding section somewhere. Log in and find itAdd port forwarding for port 80 and 443 to the server hosting this projectConfigure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53Use the Nginx Proxy Manager as your gateway to forward to your other web based servicesQuick Setup​Install Docker and Docker-ComposeDocker Install documentationDocker-Compose Install documentationCreate a docker-compose.yml file similar to this:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThis is the bare minimum configuration required. See thedocumentationfor more.Bring up your stack by runningbashdocker-composeup-d# If using docker-compose-plugindockercomposeup-dLog in to the Admin UIWhen your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.http://127.0.0.1:81Default Admin User:Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password.Contributing​All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.comContributors​Special thanks toall of our contributors.Getting Support​Found a bug?DiscussionsReddit

# Guide​
This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.

• Quick Setup
• Full Setup
• Screenshots

## Project Goal​
I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.

## Features​
• Beautiful and Secure Admin Interface based onTabler
• Easily create forwarding domains, redirections, streams and 404 hosts without knowing anything about Nginx
• Free SSL using Let's Encrypt or provide your own custom SSL certificates
• Access Lists and basic HTTP Authentication for your hosts
• Advanced Nginx configuration available for super users
• User management, permissions and audit log

## Hosting your home network​
I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.

• Your home router will have a Port Forwarding section somewhere. Log in and find it
• Add port forwarding for port 80 and 443 to the server hosting this project
• Configure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53
• Use the Nginx Proxy Manager as your gateway to forward to your other web based services

## Quick Setup​
• Install Docker and Docker-Compose
• Docker Install documentation
• Docker-Compose Install documentation
• Create a docker-compose.yml file similar to this:
ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencrypt

```
services:
 app:
 image: 'jc21/nginx-proxy-manager:latest'
 restart: unless-stopped
 ports:
 - '80:80'
 - '81:81'
 - '443:443'
 volumes:
 - ./data:/data
 - ./letsencrypt:/etc/letsencrypt
```
This is the bare minimum configuration required. See thedocumentationfor more.

• Bring up your stack by running
bashdocker-composeup-d# If using docker-compose-plugindockercomposeup-d

```
docker-compose up -d

# If using docker-compose-plugin
docker compose up -d
```
• Log in to the Admin UI
When your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.

`81`http://127.0.0.1:81

Default Admin User:

Email: <EMAIL>: changeme

```
Email: <EMAIL>
Password: changeme
```
Immediately after logging in with this default user you will be asked to modify your details and change your password.

## Contributing​
All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.

`develop``master`CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.

Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.com

`develop`
### Contributors​
Special thanks toall of our contributors.

## Getting Support​
• Found a bug?
• Discussions
• Reddit

---

## 16. Guide​

**Source:** https://nginxproxymanager.com/guide/#initial-run
**Word Count:** 1,642

### Content:

Guide​This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.Quick SetupFull SetupScreenshotsProject Goal​I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.Features​Beautiful and Secure Admin Interface based onTablerEasily create forwarding domains, redirections, streams and 404 hosts without knowing anything about NginxFree SSL using Let's Encrypt or provide your own custom SSL certificatesAccess Lists and basic HTTP Authentication for your hostsAdvanced Nginx configuration available for super usersUser management, permissions and audit logHosting your home network​I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.Your home router will have a Port Forwarding section somewhere. Log in and find itAdd port forwarding for port 80 and 443 to the server hosting this projectConfigure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53Use the Nginx Proxy Manager as your gateway to forward to your other web based servicesQuick Setup​Install Docker and Docker-ComposeDocker Install documentationDocker-Compose Install documentationCreate a docker-compose.yml file similar to this:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThis is the bare minimum configuration required. See thedocumentationfor more.Bring up your stack by runningbashdocker-composeup-d# If using docker-compose-plugindockercomposeup-dLog in to the Admin UIWhen your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.http://127.0.0.1:81Default Admin User:Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password.Contributing​All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.comContributors​Special thanks toall of our contributors.Getting Support​Found a bug?DiscussionsReddit

Guide​This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.Quick SetupFull SetupScreenshotsProject Goal​I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.Features​Beautiful and Secure Admin Interface based onTablerEasily create forwarding domains, redirections, streams and 404 hosts without knowing anything about NginxFree SSL using Let's Encrypt or provide your own custom SSL certificatesAccess Lists and basic HTTP Authentication for your hostsAdvanced Nginx configuration available for super usersUser management, permissions and audit logHosting your home network​I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.Your home router will have a Port Forwarding section somewhere. Log in and find itAdd port forwarding for port 80 and 443 to the server hosting this projectConfigure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53Use the Nginx Proxy Manager as your gateway to forward to your other web based servicesQuick Setup​Install Docker and Docker-ComposeDocker Install documentationDocker-Compose Install documentationCreate a docker-compose.yml file similar to this:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThis is the bare minimum configuration required. See thedocumentationfor more.Bring up your stack by runningbashdocker-composeup-d# If using docker-compose-plugindockercomposeup-dLog in to the Admin UIWhen your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.http://127.0.0.1:81Default Admin User:Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password.Contributing​All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.comContributors​Special thanks toall of our contributors.Getting Support​Found a bug?DiscussionsReddit

Guide​This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.Quick SetupFull SetupScreenshotsProject Goal​I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.Features​Beautiful and Secure Admin Interface based onTablerEasily create forwarding domains, redirections, streams and 404 hosts without knowing anything about NginxFree SSL using Let's Encrypt or provide your own custom SSL certificatesAccess Lists and basic HTTP Authentication for your hostsAdvanced Nginx configuration available for super usersUser management, permissions and audit logHosting your home network​I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.Your home router will have a Port Forwarding section somewhere. Log in and find itAdd port forwarding for port 80 and 443 to the server hosting this projectConfigure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53Use the Nginx Proxy Manager as your gateway to forward to your other web based servicesQuick Setup​Install Docker and Docker-ComposeDocker Install documentationDocker-Compose Install documentationCreate a docker-compose.yml file similar to this:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThis is the bare minimum configuration required. See thedocumentationfor more.Bring up your stack by runningbashdocker-composeup-d# If using docker-compose-plugindockercomposeup-dLog in to the Admin UIWhen your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.http://127.0.0.1:81Default Admin User:Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password.Contributing​All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.comContributors​Special thanks toall of our contributors.Getting Support​Found a bug?DiscussionsReddit

# Guide​
This project comes as a pre-built docker image that enables you to easily forward to your websites running at home or otherwise, including free SSL, without having to know too much about Nginx or Letsencrypt.

• Quick Setup
• Full Setup
• Screenshots

## Project Goal​
I created this project to fill a personal need to provide users with an easy way to accomplish reverse proxying hosts with SSL termination and it had to be so easy that a monkey could do it. This goal hasn't changed. While there might be advanced options they are optional and the project should be as simple as possible so that the barrier for entry here is low.

## Features​
• Beautiful and Secure Admin Interface based onTabler
• Easily create forwarding domains, redirections, streams and 404 hosts without knowing anything about Nginx
• Free SSL using Let's Encrypt or provide your own custom SSL certificates
• Access Lists and basic HTTP Authentication for your hosts
• Advanced Nginx configuration available for super users
• User management, permissions and audit log

## Hosting your home network​
I won't go in to too much detail here but here are the basics for someone new to this self-hosted world.

• Your home router will have a Port Forwarding section somewhere. Log in and find it
• Add port forwarding for port 80 and 443 to the server hosting this project
• Configure your domain name details to point to your home, either with a static ip or a service like DuckDNS orAmazon Route53
• Use the Nginx Proxy Manager as your gateway to forward to your other web based services

## Quick Setup​
• Install Docker and Docker-Compose
• Docker Install documentation
• Docker-Compose Install documentation
• Create a docker-compose.yml file similar to this:
ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:-'80:80'-'81:81'-'443:443'volumes:-./data:/data-./letsencrypt:/etc/letsencrypt

```
services:
 app:
 image: 'jc21/nginx-proxy-manager:latest'
 restart: unless-stopped
 ports:
 - '80:80'
 - '81:81'
 - '443:443'
 volumes:
 - ./data:/data
 - ./letsencrypt:/etc/letsencrypt
```
This is the bare minimum configuration required. See thedocumentationfor more.

• Bring up your stack by running
bashdocker-composeup-d# If using docker-compose-plugindockercomposeup-d

```
docker-compose up -d

# If using docker-compose-plugin
docker compose up -d
```
• Log in to the Admin UI
When your docker container is running, connect to it on port81for the admin interface. Sometimes this can take a little bit because of the entropy of keys.

`81`http://127.0.0.1:81

Default Admin User:

Email: <EMAIL>: changeme

```
Email: <EMAIL>
Password: changeme
```
Immediately after logging in with this default user you will be asked to modify your details and change your password.

## Contributing​
All are welcome to create pull requests for this project, against thedevelopbranch. Official releases are created from themasterbranch.

`develop``master`CI is used in this project. All PR's must pass before being considered. After passing, docker builds for PR's are available on dockerhub for manual verifications.

Documentation within thedevelopbranch is available for preview athttps://develop.nginxproxymanager.com

`develop`
### Contributors​
Special thanks toall of our contributors.

## Getting Support​
• Found a bug?
• Discussions
• Reddit

---

## 17. Full Setup Instructions​

**Source:** https://nginxproxymanager.com/setup/
**Word Count:** 2,143

### Content:

Full Setup Instructions​Running the App​Create adocker-compose.ymlfile:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# These ports are in format <host-port>:<container-port>-'80:80'# Public HTTP Port-'443:443'# Public HTTPS Port-'81:81'# Admin Web Port# Add any other Stream port you want to expose# - '21:21' # FTPenvironment:# Uncomment this if you want to change the location of# the SQLite DB file within the container# DB_SQLITE_FILE: "/data/database.sqlite"# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThen:bashdockercomposeup-dUsing MySQL / MariaDB Database​If you opt for the MySQL configuration you will have to provide the database server yourself. You can also use MariaDB. Here are the minimum supported versions:MySQL v5.7.8+MariaDB v10.2.7+It's easy to use another docker container for your database also and link it as part of the docker stack, so that's what the following examples are going to use.Here is an example of what yourdocker-compose.ymlwill look like when using a MariaDB container:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# These ports are in format <host-port>:<container-port>-'80:80'# Public HTTP Port-'443:443'# Public HTTPS Port-'81:81'# Admin Web Port# Add any other Stream port you want to expose# - '21:21' # FTPenvironment:# Mysql/Maria connection parameters:DB_MYSQL_HOST:"db"DB_MYSQL_PORT:3306DB_MYSQL_USER:"npm"DB_MYSQL_PASSWORD:"npm"DB_MYSQL_NAME:"npm"# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptdepends_on:-dbdb:image:'jc21/mariadb-aria:latest'restart:unless-stoppedenvironment:MYSQL_ROOT_PASSWORD:'npm'MYSQL_DATABASE:'npm'MYSQL_USER:'npm'MYSQL_PASSWORD:'npm'MARIADB_AUTO_UPGRADE:'1'volumes:-./mysql:/var/lib/mysqlWARNINGPlease note, thatDB_MYSQL_*environment variables will take precedent overDB_SQLITE_*variables. So if you keep the MySQL variables, you will not be able to use SQLite.Using Postgres database​Similar to the MySQL server setup:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# These ports are in format <host-port>:<container-port>-'80:80'# Public HTTP Port-'443:443'# Public HTTPS Port-'81:81'# Admin Web Port# Add any other Stream port you want to expose# - '21:21' # FTPenvironment:# Postgres parameters:DB_POSTGRES_HOST:'db'DB_POSTGRES_PORT:'5432'DB_POSTGRES_USER:'npm'DB_POSTGRES_PASSWORD:'npmpass'DB_POSTGRES_NAME:'npm'# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptdepends_on:-dbdb:image:postgres:latestenvironment:POSTGRES_USER:'npm'POSTGRES_PASSWORD:'npmpass'POSTGRES_DB:'npm'volumes:-./postgres:/var/lib/postgresql/dataWARNINGCustom Postgres schema is not supported, as suchpublicwill be used.Running on Raspberry PI / ARM devices​The docker images support the following architectures:amd64arm64armv7The docker images are a manifest of all the architecture docker builds supported, so this means you don't have to worry about doing anything special and you can follow the common instructions above.Check out thedockerhub tagsfor a list of supported architectures and if you want one that doesn't exist,create a feature request.Also, if you don't know how to already, followthis guide to install docker and docker-composeon Raspbian.Please note that thejc21/mariadb-aria:latestimage might have some problems on some ARM devices, if you want a separate database container, use theyobasystems/alpine-mariadb:latestimage.Initial Run​After the app is running for the first time, the following will happen:JWT keys will be generated and saved in the data folderThe database will initialize with table structuresA default admin user will be createdThis process can take a couple of minutes depending on your machine.Default Administrator User​Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password. You can change defaults with:environment:INITIAL_ADMIN_EMAIL: my@example.comINITIAL_ADMIN_PASSWORD: mypassword1

Full Setup Instructions​Running the App​Create adocker-compose.ymlfile:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# These ports are in format <host-port>:<container-port>-'80:80'# Public HTTP Port-'443:443'# Public HTTPS Port-'81:81'# Admin Web Port# Add any other Stream port you want to expose# - '21:21' # FTPenvironment:# Uncomment this if you want to change the location of# the SQLite DB file within the container# DB_SQLITE_FILE: "/data/database.sqlite"# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThen:bashdockercomposeup-dUsing MySQL / MariaDB Database​If you opt for the MySQL configuration you will have to provide the database server yourself. You can also use MariaDB. Here are the minimum supported versions:MySQL v5.7.8+MariaDB v10.2.7+It's easy to use another docker container for your database also and link it as part of the docker stack, so that's what the following examples are going to use.Here is an example of what yourdocker-compose.ymlwill look like when using a MariaDB container:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# These ports are in format <host-port>:<container-port>-'80:80'# Public HTTP Port-'443:443'# Public HTTPS Port-'81:81'# Admin Web Port# Add any other Stream port you want to expose# - '21:21' # FTPenvironment:# Mysql/Maria connection parameters:DB_MYSQL_HOST:"db"DB_MYSQL_PORT:3306DB_MYSQL_USER:"npm"DB_MYSQL_PASSWORD:"npm"DB_MYSQL_NAME:"npm"# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptdepends_on:-dbdb:image:'jc21/mariadb-aria:latest'restart:unless-stoppedenvironment:MYSQL_ROOT_PASSWORD:'npm'MYSQL_DATABASE:'npm'MYSQL_USER:'npm'MYSQL_PASSWORD:'npm'MARIADB_AUTO_UPGRADE:'1'volumes:-./mysql:/var/lib/mysqlWARNINGPlease note, thatDB_MYSQL_*environment variables will take precedent overDB_SQLITE_*variables. So if you keep the MySQL variables, you will not be able to use SQLite.Using Postgres database​Similar to the MySQL server setup:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# These ports are in format <host-port>:<container-port>-'80:80'# Public HTTP Port-'443:443'# Public HTTPS Port-'81:81'# Admin Web Port# Add any other Stream port you want to expose# - '21:21' # FTPenvironment:# Postgres parameters:DB_POSTGRES_HOST:'db'DB_POSTGRES_PORT:'5432'DB_POSTGRES_USER:'npm'DB_POSTGRES_PASSWORD:'npmpass'DB_POSTGRES_NAME:'npm'# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptdepends_on:-dbdb:image:postgres:latestenvironment:POSTGRES_USER:'npm'POSTGRES_PASSWORD:'npmpass'POSTGRES_DB:'npm'volumes:-./postgres:/var/lib/postgresql/dataWARNINGCustom Postgres schema is not supported, as suchpublicwill be used.Running on Raspberry PI / ARM devices​The docker images support the following architectures:amd64arm64armv7The docker images are a manifest of all the architecture docker builds supported, so this means you don't have to worry about doing anything special and you can follow the common instructions above.Check out thedockerhub tagsfor a list of supported architectures and if you want one that doesn't exist,create a feature request.Also, if you don't know how to already, followthis guide to install docker and docker-composeon Raspbian.Please note that thejc21/mariadb-aria:latestimage might have some problems on some ARM devices, if you want a separate database container, use theyobasystems/alpine-mariadb:latestimage.Initial Run​After the app is running for the first time, the following will happen:JWT keys will be generated and saved in the data folderThe database will initialize with table structuresA default admin user will be createdThis process can take a couple of minutes depending on your machine.Default Administrator User​Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password. You can change defaults with:environment:INITIAL_ADMIN_EMAIL: my@example.comINITIAL_ADMIN_PASSWORD: mypassword1

Full Setup Instructions​Running the App​Create adocker-compose.ymlfile:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# These ports are in format <host-port>:<container-port>-'80:80'# Public HTTP Port-'443:443'# Public HTTPS Port-'81:81'# Admin Web Port# Add any other Stream port you want to expose# - '21:21' # FTPenvironment:# Uncomment this if you want to change the location of# the SQLite DB file within the container# DB_SQLITE_FILE: "/data/database.sqlite"# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptThen:bashdockercomposeup-dUsing MySQL / MariaDB Database​If you opt for the MySQL configuration you will have to provide the database server yourself. You can also use MariaDB. Here are the minimum supported versions:MySQL v5.7.8+MariaDB v10.2.7+It's easy to use another docker container for your database also and link it as part of the docker stack, so that's what the following examples are going to use.Here is an example of what yourdocker-compose.ymlwill look like when using a MariaDB container:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# These ports are in format <host-port>:<container-port>-'80:80'# Public HTTP Port-'443:443'# Public HTTPS Port-'81:81'# Admin Web Port# Add any other Stream port you want to expose# - '21:21' # FTPenvironment:# Mysql/Maria connection parameters:DB_MYSQL_HOST:"db"DB_MYSQL_PORT:3306DB_MYSQL_USER:"npm"DB_MYSQL_PASSWORD:"npm"DB_MYSQL_NAME:"npm"# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptdepends_on:-dbdb:image:'jc21/mariadb-aria:latest'restart:unless-stoppedenvironment:MYSQL_ROOT_PASSWORD:'npm'MYSQL_DATABASE:'npm'MYSQL_USER:'npm'MYSQL_PASSWORD:'npm'MARIADB_AUTO_UPGRADE:'1'volumes:-./mysql:/var/lib/mysqlWARNINGPlease note, thatDB_MYSQL_*environment variables will take precedent overDB_SQLITE_*variables. So if you keep the MySQL variables, you will not be able to use SQLite.Using Postgres database​Similar to the MySQL server setup:ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# These ports are in format <host-port>:<container-port>-'80:80'# Public HTTP Port-'443:443'# Public HTTPS Port-'81:81'# Admin Web Port# Add any other Stream port you want to expose# - '21:21' # FTPenvironment:# Postgres parameters:DB_POSTGRES_HOST:'db'DB_POSTGRES_PORT:'5432'DB_POSTGRES_USER:'npm'DB_POSTGRES_PASSWORD:'npmpass'DB_POSTGRES_NAME:'npm'# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptdepends_on:-dbdb:image:postgres:latestenvironment:POSTGRES_USER:'npm'POSTGRES_PASSWORD:'npmpass'POSTGRES_DB:'npm'volumes:-./postgres:/var/lib/postgresql/dataWARNINGCustom Postgres schema is not supported, as suchpublicwill be used.Running on Raspberry PI / ARM devices​The docker images support the following architectures:amd64arm64armv7The docker images are a manifest of all the architecture docker builds supported, so this means you don't have to worry about doing anything special and you can follow the common instructions above.Check out thedockerhub tagsfor a list of supported architectures and if you want one that doesn't exist,create a feature request.Also, if you don't know how to already, followthis guide to install docker and docker-composeon Raspbian.Please note that thejc21/mariadb-aria:latestimage might have some problems on some ARM devices, if you want a separate database container, use theyobasystems/alpine-mariadb:latestimage.Initial Run​After the app is running for the first time, the following will happen:JWT keys will be generated and saved in the data folderThe database will initialize with table structuresA default admin user will be createdThis process can take a couple of minutes depending on your machine.Default Administrator User​Email: <EMAIL>: changemeImmediately after logging in with this default user you will be asked to modify your details and change your password. You can change defaults with:environment:INITIAL_ADMIN_EMAIL: my@example.comINITIAL_ADMIN_PASSWORD: mypassword1

# Full Setup Instructions​

## Running the App​
Create adocker-compose.ymlfile:

`docker-compose.yml`ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# These ports are in format <host-port>:<container-port>-'80:80'# Public HTTP Port-'443:443'# Public HTTPS Port-'81:81'# Admin Web Port# Add any other Stream port you want to expose# - '21:21' # FTPenvironment:# Uncomment this if you want to change the location of# the SQLite DB file within the container# DB_SQLITE_FILE: "/data/database.sqlite"# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencrypt

```
services:
 app:
 image: 'jc21/nginx-proxy-manager:latest'
 restart: unless-stopped
 ports:
 # These ports are in format <host-port>:<container-port>
 - '80:80' # Public HTTP Port
 - '443:443' # Public HTTPS Port
 - '81:81' # Admin Web Port
 # Add any other Stream port you want to expose
 # - '21:21' # FTP

 environment:
 # Uncomment this if you want to change the location of
 # the SQLite DB file within the container
 # DB_SQLITE_FILE: "/data/database.sqlite"

 # Uncomment this if IPv6 is not enabled on your host
 # DISABLE_IPV6: 'true'

 volumes:
 - ./data:/data
 - ./letsencrypt:/etc/letsencrypt
```
bashdockercomposeup-d

```
docker compose up -d
```

## Using MySQL / MariaDB Database​
If you opt for the MySQL configuration you will have to provide the database server yourself. You can also use MariaDB. Here are the minimum supported versions:

• MySQL v5.7.8+
• MariaDB v10.2.7+
It's easy to use another docker container for your database also and link it as part of the docker stack, so that's what the following examples are going to use.

Here is an example of what yourdocker-compose.ymlwill look like when using a MariaDB container:

`docker-compose.yml`ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# These ports are in format <host-port>:<container-port>-'80:80'# Public HTTP Port-'443:443'# Public HTTPS Port-'81:81'# Admin Web Port# Add any other Stream port you want to expose# - '21:21' # FTPenvironment:# Mysql/Maria connection parameters:DB_MYSQL_HOST:"db"DB_MYSQL_PORT:3306DB_MYSQL_USER:"npm"DB_MYSQL_PASSWORD:"npm"DB_MYSQL_NAME:"npm"# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptdepends_on:-dbdb:image:'jc21/mariadb-aria:latest'restart:unless-stoppedenvironment:MYSQL_ROOT_PASSWORD:'npm'MYSQL_DATABASE:'npm'MYSQL_USER:'npm'MYSQL_PASSWORD:'npm'MARIADB_AUTO_UPGRADE:'1'volumes:-./mysql:/var/lib/mysql

```
services:
 app:
 image: 'jc21/nginx-proxy-manager:latest'
 restart: unless-stopped
 ports:
 # These ports are in format <host-port>:<container-port>
 - '80:80' # Public HTTP Port
 - '443:443' # Public HTTPS Port
 - '81:81' # Admin Web Port
 # Add any other Stream port you want to expose
 # - '21:21' # FTP
 environment:
 # Mysql/Maria connection parameters:
 DB_MYSQL_HOST: "db"
 DB_MYSQL_PORT: 3306
 DB_MYSQL_USER: "npm"
 DB_MYSQL_PASSWORD: "npm"
 DB_MYSQL_NAME: "npm"
 # Uncomment this if IPv6 is not enabled on your host
 # DISABLE_IPV6: 'true'
 volumes:
 - ./data:/data
 - ./letsencrypt:/etc/letsencrypt
 depends_on:
 - db

 db:
 image: 'jc21/mariadb-aria:latest'
 restart: unless-stopped
 environment:
 MYSQL_ROOT_PASSWORD: 'npm'
 MYSQL_DATABASE: 'npm'
 MYSQL_USER: 'npm'
 MYSQL_PASSWORD: 'npm'
 MARIADB_AUTO_UPGRADE: '1'
 volumes:
 - ./mysql:/var/lib/mysql
```
WARNINGPlease note, thatDB_MYSQL_*environment variables will take precedent overDB_SQLITE_*variables. So if you keep the MySQL variables, you will not be able to use SQLite.

Please note, thatDB_MYSQL_*environment variables will take precedent overDB_SQLITE_*variables. So if you keep the MySQL variables, you will not be able to use SQLite.

`DB_MYSQL_*``DB_SQLITE_*`
## Using Postgres database​
Similar to the MySQL server setup:

ymlservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# These ports are in format <host-port>:<container-port>-'80:80'# Public HTTP Port-'443:443'# Public HTTPS Port-'81:81'# Admin Web Port# Add any other Stream port you want to expose# - '21:21' # FTPenvironment:# Postgres parameters:DB_POSTGRES_HOST:'db'DB_POSTGRES_PORT:'5432'DB_POSTGRES_USER:'npm'DB_POSTGRES_PASSWORD:'npmpass'DB_POSTGRES_NAME:'npm'# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptdepends_on:-dbdb:image:postgres:latestenvironment:POSTGRES_USER:'npm'POSTGRES_PASSWORD:'npmpass'POSTGRES_DB:'npm'volumes:-./postgres:/var/lib/postgresql/data

```
services:
 app:
 image: 'jc21/nginx-proxy-manager:latest'
 restart: unless-stopped
 ports:
 # These ports are in format <host-port>:<container-port>
 - '80:80' # Public HTTP Port
 - '443:443' # Public HTTPS Port
 - '81:81' # Admin Web Port
 # Add any other Stream port you want to expose
 # - '21:21' # FTP
 environment:
 # Postgres parameters:
 DB_POSTGRES_HOST: 'db'
 DB_POSTGRES_PORT: '5432'
 DB_POSTGRES_USER: 'npm'
 DB_POSTGRES_PASSWORD: 'npmpass'
 DB_POSTGRES_NAME: 'npm'
 # Uncomment this if IPv6 is not enabled on your host
 # DISABLE_IPV6: 'true'
 volumes:
 - ./data:/data
 - ./letsencrypt:/etc/letsencrypt
 depends_on:
 - db

 db:
 image: postgres:latest
 environment:
 POSTGRES_USER: 'npm'
 POSTGRES_PASSWORD: 'npmpass'
 POSTGRES_DB: 'npm'
 volumes:
 - ./postgres:/var/lib/postgresql/data
```
WARNINGCustom Postgres schema is not supported, as suchpublicwill be used.

Custom Postgres schema is not supported, as suchpublicwill be used.

`public`
## Running on Raspberry PI / ARM devices​
The docker images support the following architectures:

• amd64
• arm64
• armv7
The docker images are a manifest of all the architecture docker builds supported, so this means you don't have to worry about doing anything special and you can follow the common instructions above.

Check out thedockerhub tagsfor a list of supported architectures and if you want one that doesn't exist,create a feature request.

Also, if you don't know how to already, followthis guide to install docker and docker-composeon Raspbian.

Please note that thejc21/mariadb-aria:latestimage might have some problems on some ARM devices, if you want a separate database container, use theyobasystems/alpine-mariadb:latestimage.

`jc21/mariadb-aria:latest``yobasystems/alpine-mariadb:latest`
## Initial Run​
After the app is running for the first time, the following will happen:

• JWT keys will be generated and saved in the data folder
• The database will initialize with table structures
• A default admin user will be created
This process can take a couple of minutes depending on your machine.

## Default Administrator User​
Email: <EMAIL>: changeme

```
Email: <EMAIL>
Password: changeme
```
Immediately after logging in with this default user you will be asked to modify your details and change your password. You can change defaults with:

environment:INITIAL_ADMIN_EMAIL: my@example.comINITIAL_ADMIN_PASSWORD: mypassword1

```
environment:
 INITIAL_ADMIN_EMAIL: <EMAIL>
 INITIAL_ADMIN_PASSWORD: mypassword1
```

---

## 18. Advanced Configuration​

**Source:** https://nginxproxymanager.com/advanced-config/
**Word Count:** 3,004

### Content:

Advanced Configuration​Running processes as a user/group​By default, the services (nginx etc) will run asrootuser inside the docker container. You can change this behaviour by setting the following environment variables. Not only will they run the services as this user/group, they will change the ownership on thedataandletsencryptfolders at startup.ymlservices:app:image:'jc21/nginx-proxy-manager:latest'environment:PUID:1000PGID:1000# ...This may have the side effect of a failed container start due to permission denied trying to open port 80 on some systems. The only course to fix that is to remove the variables and run as the default root user.Best Practice: Use a Docker network​For those who have a few of their upstream services running in Docker on the same Docker host as NPM, here's a trick to secure things a bit better. By creating a custom Docker network, you don't need to publish ports for your upstream services to all of the Docker host's interfaces.Create a network, ie "scoobydoo":bashdockernetworkcreatescoobydooThen add the following to thedocker-compose.ymlfile for both NPM and any other services running on this Docker host:ymlnetworks:default:external:truename:scoobydooLet's look at a Portainer example:ymlservices:portainer:image:portainer/portainerprivileged:truevolumes:-'./data:/data'-'/var/run/docker.sock:/var/run/docker.sock'restart:unless-stoppednetworks:default:external:truename:scoobydooNow in the NPM UI you can create a proxy host withportaineras the hostname, and port9000as the port. Even though this port isn't listed in the docker-compose file, it's "exposed" by the Portainer Docker image for you and not available on the Docker host outside of this Docker network. The service name is used as the hostname, so make sure your service names are unique when using the same network.Docker Healthcheck​TheDockerfilethat builds this project does not include aHEALTHCHECKbut you can opt in to this feature by adding the following to the service in yourdocker-compose.ymlfile:ymlhealthcheck:test: ["CMD","/usr/bin/check-health"]interval:10stimeout:3sDocker File Secrets​This image supports the use of Docker secrets to import from files and keep sensitive usernames or passwords from being passed or preserved in plaintext.You can set any environment variable from a file by appending__FILE(double-underscore FILE) to the environmental variable name.ymlsecrets:# Secrets are single-line text files where the sole content is the secret# Paths in this example assume that secrets are kept in local folder called ".secrets"DB_ROOT_PWD:file:.secrets/db_root_pwd.txtMYSQL_PWD:file:.secrets/mysql_pwd.txtservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# Public HTTP Port:-'80:80'# Public HTTPS Port:-'443:443'# Admin Web Port:-'81:81'environment:# These are the settings to access your dbDB_MYSQL_HOST:"db"DB_MYSQL_PORT:3306DB_MYSQL_USER:"npm"# DB_MYSQL_PASSWORD: "npm" # use secret insteadDB_MYSQL_PASSWORD__FILE:/run/secrets/MYSQL_PWDDB_MYSQL_NAME:"npm"# If you would rather use Sqlite, remove all DB_MYSQL_* lines above# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptsecrets:-MYSQL_PWDdepends_on:-dbdb:image:jc21/mariadb-ariarestart:unless-stoppedenvironment:# MYSQL_ROOT_PASSWORD: "npm" # use secret insteadMYSQL_ROOT_PASSWORD__FILE:/run/secrets/DB_ROOT_PWDMYSQL_DATABASE:"npm"MYSQL_USER:"npm"# MYSQL_PASSWORD: "npm" # use secret insteadMYSQL_PASSWORD__FILE:/run/secrets/MYSQL_PWDMARIADB_AUTO_UPGRADE:'1'volumes:-./mysql:/var/lib/mysqlsecrets:-DB_ROOT_PWD-MYSQL_PWDDisabling IPv6​On some Docker hosts IPv6 may not be enabled. In these cases, the following message may be seen in the log:Address family not supported by protocolThe easy fix is to add a Docker environment variable to the Nginx Proxy Manager stack:ymlenvironment:DISABLE_IPV6:'true'Custom Nginx Configurations​If you are a more advanced user, you might be itching for extra Nginx customizability.NPM has the ability to include different custom configuration snippets in different places.You can add your custom configuration snippet files at/data/nginx/customas follow:/data/nginx/custom/root_top.conf: Included at the top of nginx.conf/data/nginx/custom/root.conf: Included at the very end of nginx.conf/data/nginx/custom/http_top.conf: Included at the top of the main http block/data/nginx/custom/http.conf: Included at the end of the main http block/data/nginx/custom/events.conf: Included at the end of the events block/data/nginx/custom/stream.conf: Included at the end of the main stream block/data/nginx/custom/server_proxy.conf: Included at the end of every proxy server block/data/nginx/custom/server_redirect.conf: Included at the end of every redirection server block/data/nginx/custom/server_stream.conf: Included at the end of every stream server block/data/nginx/custom/server_stream_tcp.conf: Included at the end of every TCP stream server block/data/nginx/custom/server_stream_udp.conf: Included at the end of every UDP stream server block/data/nginx/custom/server_dead.conf: Included at the end of every 404 server blockEvery file is optional.X-FRAME-OPTIONS Header​You can configure theX-FRAME-OPTIONSheader value by specifying it as a Docker environment variable. The default if not specified isdeny.yml...environment:X_FRAME_OPTIONS:"sameorigin"...Customising logrotate settings​By default, NPM rotates the access- and error logs weekly and keeps 4 and 10 log files respectively. Depending on the usage, this can lead to large log files, especially access logs. You can customise the logrotate configuration through a mount (if your custom config islogrotate.custom):ymlvolumes:...-./logrotate.custom:/etc/logrotate.d/nginx-proxy-managerFor reference, the default configuration can be foundhere.Enabling the geoip2 module​To enable the geoip2 module, you can create the custom configuration file/data/nginx/custom/root_top.confand include the following snippet:load_module /usr/lib/nginx/modules/ngx_http_geoip2_module.so;load_module /usr/lib/nginx/modules/ngx_stream_geoip2_module.so;

Advanced Configuration​Running processes as a user/group​By default, the services (nginx etc) will run asrootuser inside the docker container. You can change this behaviour by setting the following environment variables. Not only will they run the services as this user/group, they will change the ownership on thedataandletsencryptfolders at startup.ymlservices:app:image:'jc21/nginx-proxy-manager:latest'environment:PUID:1000PGID:1000# ...This may have the side effect of a failed container start due to permission denied trying to open port 80 on some systems. The only course to fix that is to remove the variables and run as the default root user.Best Practice: Use a Docker network​For those who have a few of their upstream services running in Docker on the same Docker host as NPM, here's a trick to secure things a bit better. By creating a custom Docker network, you don't need to publish ports for your upstream services to all of the Docker host's interfaces.Create a network, ie "scoobydoo":bashdockernetworkcreatescoobydooThen add the following to thedocker-compose.ymlfile for both NPM and any other services running on this Docker host:ymlnetworks:default:external:truename:scoobydooLet's look at a Portainer example:ymlservices:portainer:image:portainer/portainerprivileged:truevolumes:-'./data:/data'-'/var/run/docker.sock:/var/run/docker.sock'restart:unless-stoppednetworks:default:external:truename:scoobydooNow in the NPM UI you can create a proxy host withportaineras the hostname, and port9000as the port. Even though this port isn't listed in the docker-compose file, it's "exposed" by the Portainer Docker image for you and not available on the Docker host outside of this Docker network. The service name is used as the hostname, so make sure your service names are unique when using the same network.Docker Healthcheck​TheDockerfilethat builds this project does not include aHEALTHCHECKbut you can opt in to this feature by adding the following to the service in yourdocker-compose.ymlfile:ymlhealthcheck:test: ["CMD","/usr/bin/check-health"]interval:10stimeout:3sDocker File Secrets​This image supports the use of Docker secrets to import from files and keep sensitive usernames or passwords from being passed or preserved in plaintext.You can set any environment variable from a file by appending__FILE(double-underscore FILE) to the environmental variable name.ymlsecrets:# Secrets are single-line text files where the sole content is the secret# Paths in this example assume that secrets are kept in local folder called ".secrets"DB_ROOT_PWD:file:.secrets/db_root_pwd.txtMYSQL_PWD:file:.secrets/mysql_pwd.txtservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# Public HTTP Port:-'80:80'# Public HTTPS Port:-'443:443'# Admin Web Port:-'81:81'environment:# These are the settings to access your dbDB_MYSQL_HOST:"db"DB_MYSQL_PORT:3306DB_MYSQL_USER:"npm"# DB_MYSQL_PASSWORD: "npm" # use secret insteadDB_MYSQL_PASSWORD__FILE:/run/secrets/MYSQL_PWDDB_MYSQL_NAME:"npm"# If you would rather use Sqlite, remove all DB_MYSQL_* lines above# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptsecrets:-MYSQL_PWDdepends_on:-dbdb:image:jc21/mariadb-ariarestart:unless-stoppedenvironment:# MYSQL_ROOT_PASSWORD: "npm" # use secret insteadMYSQL_ROOT_PASSWORD__FILE:/run/secrets/DB_ROOT_PWDMYSQL_DATABASE:"npm"MYSQL_USER:"npm"# MYSQL_PASSWORD: "npm" # use secret insteadMYSQL_PASSWORD__FILE:/run/secrets/MYSQL_PWDMARIADB_AUTO_UPGRADE:'1'volumes:-./mysql:/var/lib/mysqlsecrets:-DB_ROOT_PWD-MYSQL_PWDDisabling IPv6​On some Docker hosts IPv6 may not be enabled. In these cases, the following message may be seen in the log:Address family not supported by protocolThe easy fix is to add a Docker environment variable to the Nginx Proxy Manager stack:ymlenvironment:DISABLE_IPV6:'true'Custom Nginx Configurations​If you are a more advanced user, you might be itching for extra Nginx customizability.NPM has the ability to include different custom configuration snippets in different places.You can add your custom configuration snippet files at/data/nginx/customas follow:/data/nginx/custom/root_top.conf: Included at the top of nginx.conf/data/nginx/custom/root.conf: Included at the very end of nginx.conf/data/nginx/custom/http_top.conf: Included at the top of the main http block/data/nginx/custom/http.conf: Included at the end of the main http block/data/nginx/custom/events.conf: Included at the end of the events block/data/nginx/custom/stream.conf: Included at the end of the main stream block/data/nginx/custom/server_proxy.conf: Included at the end of every proxy server block/data/nginx/custom/server_redirect.conf: Included at the end of every redirection server block/data/nginx/custom/server_stream.conf: Included at the end of every stream server block/data/nginx/custom/server_stream_tcp.conf: Included at the end of every TCP stream server block/data/nginx/custom/server_stream_udp.conf: Included at the end of every UDP stream server block/data/nginx/custom/server_dead.conf: Included at the end of every 404 server blockEvery file is optional.X-FRAME-OPTIONS Header​You can configure theX-FRAME-OPTIONSheader value by specifying it as a Docker environment variable. The default if not specified isdeny.yml...environment:X_FRAME_OPTIONS:"sameorigin"...Customising logrotate settings​By default, NPM rotates the access- and error logs weekly and keeps 4 and 10 log files respectively. Depending on the usage, this can lead to large log files, especially access logs. You can customise the logrotate configuration through a mount (if your custom config islogrotate.custom):ymlvolumes:...-./logrotate.custom:/etc/logrotate.d/nginx-proxy-managerFor reference, the default configuration can be foundhere.Enabling the geoip2 module​To enable the geoip2 module, you can create the custom configuration file/data/nginx/custom/root_top.confand include the following snippet:load_module /usr/lib/nginx/modules/ngx_http_geoip2_module.so;load_module /usr/lib/nginx/modules/ngx_stream_geoip2_module.so;

Advanced Configuration​Running processes as a user/group​By default, the services (nginx etc) will run asrootuser inside the docker container. You can change this behaviour by setting the following environment variables. Not only will they run the services as this user/group, they will change the ownership on thedataandletsencryptfolders at startup.ymlservices:app:image:'jc21/nginx-proxy-manager:latest'environment:PUID:1000PGID:1000# ...This may have the side effect of a failed container start due to permission denied trying to open port 80 on some systems. The only course to fix that is to remove the variables and run as the default root user.Best Practice: Use a Docker network​For those who have a few of their upstream services running in Docker on the same Docker host as NPM, here's a trick to secure things a bit better. By creating a custom Docker network, you don't need to publish ports for your upstream services to all of the Docker host's interfaces.Create a network, ie "scoobydoo":bashdockernetworkcreatescoobydooThen add the following to thedocker-compose.ymlfile for both NPM and any other services running on this Docker host:ymlnetworks:default:external:truename:scoobydooLet's look at a Portainer example:ymlservices:portainer:image:portainer/portainerprivileged:truevolumes:-'./data:/data'-'/var/run/docker.sock:/var/run/docker.sock'restart:unless-stoppednetworks:default:external:truename:scoobydooNow in the NPM UI you can create a proxy host withportaineras the hostname, and port9000as the port. Even though this port isn't listed in the docker-compose file, it's "exposed" by the Portainer Docker image for you and not available on the Docker host outside of this Docker network. The service name is used as the hostname, so make sure your service names are unique when using the same network.Docker Healthcheck​TheDockerfilethat builds this project does not include aHEALTHCHECKbut you can opt in to this feature by adding the following to the service in yourdocker-compose.ymlfile:ymlhealthcheck:test: ["CMD","/usr/bin/check-health"]interval:10stimeout:3sDocker File Secrets​This image supports the use of Docker secrets to import from files and keep sensitive usernames or passwords from being passed or preserved in plaintext.You can set any environment variable from a file by appending__FILE(double-underscore FILE) to the environmental variable name.ymlsecrets:# Secrets are single-line text files where the sole content is the secret# Paths in this example assume that secrets are kept in local folder called ".secrets"DB_ROOT_PWD:file:.secrets/db_root_pwd.txtMYSQL_PWD:file:.secrets/mysql_pwd.txtservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# Public HTTP Port:-'80:80'# Public HTTPS Port:-'443:443'# Admin Web Port:-'81:81'environment:# These are the settings to access your dbDB_MYSQL_HOST:"db"DB_MYSQL_PORT:3306DB_MYSQL_USER:"npm"# DB_MYSQL_PASSWORD: "npm" # use secret insteadDB_MYSQL_PASSWORD__FILE:/run/secrets/MYSQL_PWDDB_MYSQL_NAME:"npm"# If you would rather use Sqlite, remove all DB_MYSQL_* lines above# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptsecrets:-MYSQL_PWDdepends_on:-dbdb:image:jc21/mariadb-ariarestart:unless-stoppedenvironment:# MYSQL_ROOT_PASSWORD: "npm" # use secret insteadMYSQL_ROOT_PASSWORD__FILE:/run/secrets/DB_ROOT_PWDMYSQL_DATABASE:"npm"MYSQL_USER:"npm"# MYSQL_PASSWORD: "npm" # use secret insteadMYSQL_PASSWORD__FILE:/run/secrets/MYSQL_PWDMARIADB_AUTO_UPGRADE:'1'volumes:-./mysql:/var/lib/mysqlsecrets:-DB_ROOT_PWD-MYSQL_PWDDisabling IPv6​On some Docker hosts IPv6 may not be enabled. In these cases, the following message may be seen in the log:Address family not supported by protocolThe easy fix is to add a Docker environment variable to the Nginx Proxy Manager stack:ymlenvironment:DISABLE_IPV6:'true'Custom Nginx Configurations​If you are a more advanced user, you might be itching for extra Nginx customizability.NPM has the ability to include different custom configuration snippets in different places.You can add your custom configuration snippet files at/data/nginx/customas follow:/data/nginx/custom/root_top.conf: Included at the top of nginx.conf/data/nginx/custom/root.conf: Included at the very end of nginx.conf/data/nginx/custom/http_top.conf: Included at the top of the main http block/data/nginx/custom/http.conf: Included at the end of the main http block/data/nginx/custom/events.conf: Included at the end of the events block/data/nginx/custom/stream.conf: Included at the end of the main stream block/data/nginx/custom/server_proxy.conf: Included at the end of every proxy server block/data/nginx/custom/server_redirect.conf: Included at the end of every redirection server block/data/nginx/custom/server_stream.conf: Included at the end of every stream server block/data/nginx/custom/server_stream_tcp.conf: Included at the end of every TCP stream server block/data/nginx/custom/server_stream_udp.conf: Included at the end of every UDP stream server block/data/nginx/custom/server_dead.conf: Included at the end of every 404 server blockEvery file is optional.X-FRAME-OPTIONS Header​You can configure theX-FRAME-OPTIONSheader value by specifying it as a Docker environment variable. The default if not specified isdeny.yml...environment:X_FRAME_OPTIONS:"sameorigin"...Customising logrotate settings​By default, NPM rotates the access- and error logs weekly and keeps 4 and 10 log files respectively. Depending on the usage, this can lead to large log files, especially access logs. You can customise the logrotate configuration through a mount (if your custom config islogrotate.custom):ymlvolumes:...-./logrotate.custom:/etc/logrotate.d/nginx-proxy-managerFor reference, the default configuration can be foundhere.Enabling the geoip2 module​To enable the geoip2 module, you can create the custom configuration file/data/nginx/custom/root_top.confand include the following snippet:load_module /usr/lib/nginx/modules/ngx_http_geoip2_module.so;load_module /usr/lib/nginx/modules/ngx_stream_geoip2_module.so;

# Advanced Configuration​

## Running processes as a user/group​
By default, the services (nginx etc) will run asrootuser inside the docker container. You can change this behaviour by setting the following environment variables. Not only will they run the services as this user/group, they will change the ownership on thedataandletsencryptfolders at startup.

`root``data``letsencrypt`ymlservices:app:image:'jc21/nginx-proxy-manager:latest'environment:PUID:1000PGID:1000# ...

```
services:
 app:
 image: 'jc21/nginx-proxy-manager:latest'
 environment:
 PUID: 1000
 PGID: 1000
 # ...
```
This may have the side effect of a failed container start due to permission denied trying to open port 80 on some systems. The only course to fix that is to remove the variables and run as the default root user.

## Best Practice: Use a Docker network​
For those who have a few of their upstream services running in Docker on the same Docker host as NPM, here's a trick to secure things a bit better. By creating a custom Docker network, you don't need to publish ports for your upstream services to all of the Docker host's interfaces.

Create a network, ie "scoobydoo":

bashdockernetworkcreatescoobydoo

```
docker network create scoobydoo
```
Then add the following to thedocker-compose.ymlfile for both NPM and any other services running on this Docker host:

`docker-compose.yml`ymlnetworks:default:external:truename:scoobydoo

```
networks:
 default:
 external: true
 name: scoobydoo
```
Let's look at a Portainer example:

ymlservices:portainer:image:portainer/portainerprivileged:truevolumes:-'./data:/data'-'/var/run/docker.sock:/var/run/docker.sock'restart:unless-stoppednetworks:default:external:truename:scoobydoo

```
services:

 portainer:
 image: portainer/portainer
 privileged: true
 volumes:
 - './data:/data'
 - '/var/run/docker.sock:/var/run/docker.sock'
 restart: unless-stopped

networks:
 default:
 external: true
 name: scoobydoo
```
Now in the NPM UI you can create a proxy host withportaineras the hostname, and port9000as the port. Even though this port isn't listed in the docker-compose file, it's "exposed" by the Portainer Docker image for you and not available on the Docker host outside of this Docker network. The service name is used as the hostname, so make sure your service names are unique when using the same network.

`portainer``9000`
## Docker Healthcheck​
TheDockerfilethat builds this project does not include aHEALTHCHECKbut you can opt in to this feature by adding the following to the service in yourdocker-compose.ymlfile:

`Dockerfile``HEALTHCHECK``docker-compose.yml`ymlhealthcheck:test: ["CMD","/usr/bin/check-health"]interval:10stimeout:3s

```
healthcheck:
 test: ["CMD", "/usr/bin/check-health"]
 interval: 10s
 timeout: 3s
```

## Docker File Secrets​
This image supports the use of Docker secrets to import from files and keep sensitive usernames or passwords from being passed or preserved in plaintext.

You can set any environment variable from a file by appending__FILE(double-underscore FILE) to the environmental variable name.

`__FILE`ymlsecrets:# Secrets are single-line text files where the sole content is the secret# Paths in this example assume that secrets are kept in local folder called ".secrets"DB_ROOT_PWD:file:.secrets/db_root_pwd.txtMYSQL_PWD:file:.secrets/mysql_pwd.txtservices:app:image:'jc21/nginx-proxy-manager:latest'restart:unless-stoppedports:# Public HTTP Port:-'80:80'# Public HTTPS Port:-'443:443'# Admin Web Port:-'81:81'environment:# These are the settings to access your dbDB_MYSQL_HOST:"db"DB_MYSQL_PORT:3306DB_MYSQL_USER:"npm"# DB_MYSQL_PASSWORD: "npm" # use secret insteadDB_MYSQL_PASSWORD__FILE:/run/secrets/MYSQL_PWDDB_MYSQL_NAME:"npm"# If you would rather use Sqlite, remove all DB_MYSQL_* lines above# Uncomment this if IPv6 is not enabled on your host# DISABLE_IPV6: 'true'volumes:-./data:/data-./letsencrypt:/etc/letsencryptsecrets:-MYSQL_PWDdepends_on:-dbdb:image:jc21/mariadb-ariarestart:unless-stoppedenvironment:# MYSQL_ROOT_PASSWORD: "npm" # use secret insteadMYSQL_ROOT_PASSWORD__FILE:/run/secrets/DB_ROOT_PWDMYSQL_DATABASE:"npm"MYSQL_USER:"npm"# MYSQL_PASSWORD: "npm" # use secret insteadMYSQL_PASSWORD__FILE:/run/secrets/MYSQL_PWDMARIADB_AUTO_UPGRADE:'1'volumes:-./mysql:/var/lib/mysqlsecrets:-DB_ROOT_PWD-MYSQL_PWD

```
secrets:
 # Secrets are single-line text files where the sole content is the secret
 # Paths in this example assume that secrets are kept in local folder called ".secrets"
 DB_ROOT_PWD:
 file: .secrets/db_root_pwd.txt
 MYSQL_PWD:
 file: .secrets/mysql_pwd.txt

services:
 app:
 image: 'jc21/nginx-proxy-manager:latest'
 restart: unless-stopped
 ports:
 # Public HTTP Port:
 - '80:80'
 # Public HTTPS Port:
 - '443:443'
 # Admin Web Port:
 - '81:81'
 environment:
 # These are the settings to access your db
 DB_MYSQL_HOST: "db"
 DB_MYSQL_PORT: 3306
 DB_MYSQL_USER: "npm"
 # DB_MYSQL_PASSWORD: "npm" # use secret instead
 DB_MYSQL_PASSWORD__FILE: /run/secrets/MYSQL_PWD
 DB_MYSQL_NAME: "npm"
 # If you would rather use Sqlite, remove all DB_MYSQL_* lines above
 # Uncomment this if IPv6 is not enabled on your host
 # DISABLE_IPV6: 'true'
 volumes:
 - ./data:/data
 - ./letsencrypt:/etc/letsencrypt
 secrets:
 - MYSQL_PWD
 depends_on:
 - db

 db:
 image: jc21/mariadb-aria
 restart: unless-stopped
 environment:
 # MYSQL_ROOT_PASSWORD: "npm" # use secret instead
 MYSQL_ROOT_PASSWORD__FILE: /run/secrets/DB_ROOT_PWD
 MYSQL_DATABASE: "npm"
 MYSQL_USER: "npm"
 # MYSQL_PASSWORD: "npm" # use secret instead
 MYSQL_PASSWORD__FILE: /run/secrets/MYSQL_PWD
 MARIADB_AUTO_UPGRADE: '1'
 volumes:
 - ./mysql:/var/lib/mysql
 secrets:
 - DB_ROOT_PWD
 - MYSQL_PWD
```

## Disabling IPv6​
On some Docker hosts IPv6 may not be enabled. In these cases, the following message may be seen in the log:

> Address family not supported by protocol
Address family not supported by protocol

The easy fix is to add a Docker environment variable to the Nginx Proxy Manager stack:

ymlenvironment:DISABLE_IPV6:'true'

```
environment:
 DISABLE_IPV6: 'true'
```

## Custom Nginx Configurations​
If you are a more advanced user, you might be itching for extra Nginx customizability.

NPM has the ability to include different custom configuration snippets in different places.

You can add your custom configuration snippet files at/data/nginx/customas follow:

`/data/nginx/custom`• /data/nginx/custom/root_top.conf: Included at the top of nginx.conf
`/data/nginx/custom/root_top.conf`• /data/nginx/custom/root.conf: Included at the very end of nginx.conf
`/data/nginx/custom/root.conf`• /data/nginx/custom/http_top.conf: Included at the top of the main http block
`/data/nginx/custom/http_top.conf`• /data/nginx/custom/http.conf: Included at the end of the main http block
`/data/nginx/custom/http.conf`• /data/nginx/custom/events.conf: Included at the end of the events block
`/data/nginx/custom/events.conf`• /data/nginx/custom/stream.conf: Included at the end of the main stream block
`/data/nginx/custom/stream.conf`• /data/nginx/custom/server_proxy.conf: Included at the end of every proxy server block
`/data/nginx/custom/server_proxy.conf`• /data/nginx/custom/server_redirect.conf: Included at the end of every redirection server block
`/data/nginx/custom/server_redirect.conf`• /data/nginx/custom/server_stream.conf: Included at the end of every stream server block
`/data/nginx/custom/server_stream.conf`• /data/nginx/custom/server_stream_tcp.conf: Included at the end of every TCP stream server block
`/data/nginx/custom/server_stream_tcp.conf`• /data/nginx/custom/server_stream_udp.conf: Included at the end of every UDP stream server block
`/data/nginx/custom/server_stream_udp.conf`• /data/nginx/custom/server_dead.conf: Included at the end of every 404 server block
`/data/nginx/custom/server_dead.conf`Every file is optional.

## X-FRAME-OPTIONS Header​
You can configure theX-FRAME-OPTIONSheader value by specifying it as a Docker environment variable. The default if not specified isdeny.

`X-FRAME-OPTIONS``deny`yml...environment:X_FRAME_OPTIONS:"sameorigin"...

```
...
 environment:
 X_FRAME_OPTIONS: "sameorigin"
 ...
```

## Customising logrotate settings​
By default, NPM rotates the access- and error logs weekly and keeps 4 and 10 log files respectively. Depending on the usage, this can lead to large log files, especially access logs. You can customise the logrotate configuration through a mount (if your custom config islogrotate.custom):

`logrotate.custom`ymlvolumes:...-./logrotate.custom:/etc/logrotate.d/nginx-proxy-manager

```
volumes:
 ...
 - ./logrotate.custom:/etc/logrotate.d/nginx-proxy-manager
```
For reference, the default configuration can be foundhere.

## Enabling the geoip2 module​
To enable the geoip2 module, you can create the custom configuration file/data/nginx/custom/root_top.confand include the following snippet:

`/data/nginx/custom/root_top.conf`load_module /usr/lib/nginx/modules/ngx_http_geoip2_module.so;load_module /usr/lib/nginx/modules/ngx_stream_geoip2_module.so;

```
load_module /usr/lib/nginx/modules/ngx_http_geoip2_module.so;
load_module /usr/lib/nginx/modules/ngx_stream_geoip2_module.so;
```

---


## Summary

- **Total Sections:** 18
- **Total Words:** 38,342
- **Total Characters:** 324,514
