# NGC Aether End-to-End Testing Script
# This script performs comprehensive end-to-end testing of the entire platform

Write-Host "🎯 NGC Aether End-to-End Testing..." -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Configuration
$baseUrl = "http://localhost:3000"  # Change to HTTPS URL if SSL is configured
$keycloakUrl = "http://localhost:9090"  # Change to HTTPS URL if SSL is configured

# Function to test API endpoint with JSON response
function Test-ApiEndpoint {
    param(
        [string]$Url,
        [string]$TestName,
        [hashtable]$Headers = @{},
        [string]$Method = "GET",
        [string]$Body = $null
    )
    
    try {
        Write-Host "🔍 Testing $TestName..." -ForegroundColor Yellow
        
        $params = @{
            Uri = $Url
            Method = $Method
            UseBasicParsing = $true
            Headers = $Headers
        }
        
        if ($Body) {
            $params.Body = $Body
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-WebRequest @params
        
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $TestName: SUCCESS (Status: $($response.StatusCode))" -ForegroundColor Green
            return @{ Success = $true; Response = $response }
        } else {
            Write-Host "⚠️ $TestName: Unexpected status $($response.StatusCode)" -ForegroundColor Yellow
            return @{ Success = $false; Response = $response }
        }
    }
    catch {
        Write-Host "❌ $TestName: FAILED - $($_.Exception.Message)" -ForegroundColor Red
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Test Results Tracking
$testResults = @{
    Infrastructure = @{}
    Authentication = @{}
    AIServices = @{}
    Integration = @{}
}

Write-Host "`n🏗️ Phase 1: Infrastructure Testing" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Test 1.1: Core Service Health
$testResults.Infrastructure.PostgreSQL = Test-ApiEndpoint -Url "http://localhost:5432" -TestName "PostgreSQL Connection" -Method "GET"
$testResults.Infrastructure.Keycloak = Test-ApiEndpoint -Url "$keycloakUrl/health" -TestName "Keycloak Health"
$testResults.Infrastructure.OpenWebUI = Test-ApiEndpoint -Url "$baseUrl/health" -TestName "OpenWebUI Health"
$testResults.Infrastructure.NginxPM = Test-ApiEndpoint -Url "http://localhost:81/api" -TestName "Nginx Proxy Manager"

# Test 1.2: AI Service Health
$testResults.Infrastructure.Ollama = Test-ApiEndpoint -Url "http://localhost:11434/api/tags" -TestName "Ollama API"
$testResults.Infrastructure.Tika = Test-ApiEndpoint -Url "http://localhost:9998/version" -TestName "Tika Server"
$testResults.Infrastructure.Pipelines = Test-ApiEndpoint -Url "http://localhost:9099/health" -TestName "Pipelines Service"

Write-Host "`n🔐 Phase 2: Authentication Testing" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan

# Test 2.1: OIDC Discovery
$oidcUrl = "$keycloakUrl/realms/openwebui-realm/.well-known/openid-configuration"
$testResults.Authentication.OIDCDiscovery = Test-ApiEndpoint -Url $oidcUrl -TestName "OIDC Discovery"

# Test 2.2: Keycloak Realm
$realmUrl = "$keycloakUrl/realms/openwebui-realm"
$testResults.Authentication.RealmAccess = Test-ApiEndpoint -Url $realmUrl -TestName "Keycloak Realm Access"

# Test 2.3: OpenWebUI OAuth Configuration
$testResults.Authentication.OAuthConfig = Test-ApiEndpoint -Url "$baseUrl/api/config" -TestName "OpenWebUI OAuth Config"

Write-Host "`n🤖 Phase 3: AI Services Testing" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

# Test 3.1: Ollama Model List
$testResults.AIServices.OllamaModels = Test-ApiEndpoint -Url "http://localhost:11434/api/tags" -TestName "Ollama Model List"

# Test 3.2: Tika Document Processing
$testResults.AIServices.TikaProcessing = Test-ApiEndpoint -Url "http://localhost:9998/tika" -TestName "Tika Document Processing"

# Test 3.3: Pipelines Health
$testResults.AIServices.PipelinesHealth = Test-ApiEndpoint -Url "http://localhost:9099/health" -TestName "Pipelines Health Check"

Write-Host "`n🔗 Phase 4: Integration Testing" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

# Test 4.1: OpenWebUI to Ollama Integration
$testResults.Integration.OpenWebUIToOllama = Test-ApiEndpoint -Url "$baseUrl/api/models" -TestName "OpenWebUI to Ollama Integration"

# Test 4.2: OpenWebUI to Pipelines Integration
$testResults.Integration.OpenWebUIToPipelines = Test-ApiEndpoint -Url "$baseUrl/api/pipelines" -TestName "OpenWebUI to Pipelines Integration"

# Test 4.3: Database Connectivity from OpenWebUI
$testResults.Integration.DatabaseConnectivity = Test-ApiEndpoint -Url "$baseUrl/api/users" -TestName "Database Connectivity"

Write-Host "`n📊 Test Results Summary" -ForegroundColor Cyan
Write-Host "=======================" -ForegroundColor Cyan

$totalTests = 0
$passedTests = 0

# Infrastructure Results
Write-Host "`n🏗️ Infrastructure Tests:" -ForegroundColor White
foreach ($test in $testResults.Infrastructure.Keys) {
    $totalTests++
    $result = $testResults.Infrastructure[$test]
    if ($result.Success) { 
        $passedTests++
        Write-Host "  ✅ $test" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $test" -ForegroundColor Red
    }
}

# Authentication Results
Write-Host "`n🔐 Authentication Tests:" -ForegroundColor White
foreach ($test in $testResults.Authentication.Keys) {
    $totalTests++
    $result = $testResults.Authentication[$test]
    if ($result.Success) { 
        $passedTests++
        Write-Host "  ✅ $test" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $test" -ForegroundColor Red
    }
}

# AI Services Results
Write-Host "`n🤖 AI Services Tests:" -ForegroundColor White
foreach ($test in $testResults.AIServices.Keys) {
    $totalTests++
    $result = $testResults.AIServices[$test]
    if ($result.Success) { 
        $passedTests++
        Write-Host "  ✅ $test" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $test" -ForegroundColor Red
    }
}

# Integration Results
Write-Host "`n🔗 Integration Tests:" -ForegroundColor White
foreach ($test in $testResults.Integration.Keys) {
    $totalTests++
    $result = $testResults.Integration[$test]
    if ($result.Success) { 
        $passedTests++
        Write-Host "  ✅ $test" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $test" -ForegroundColor Red
    }
}

# Overall Results
Write-Host "`n🎯 Overall Results:" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
$successRate = [math]::Round(($passedTests / $totalTests) * 100, 1)
Write-Host "Tests Passed: $passedTests/$totalTests ($successRate%)" -ForegroundColor $(if ($successRate -ge 80) { 'Green' } elseif ($successRate -ge 60) { 'Yellow' } else { 'Red' })

if ($successRate -ge 90) {
    Write-Host "`n🎉 EXCELLENT! Your NGC Aether environment is fully operational!" -ForegroundColor Green
    Write-Host "All critical systems are functioning correctly." -ForegroundColor Green
} elseif ($successRate -ge 80) {
    Write-Host "`n✅ GOOD! Your NGC Aether environment is mostly operational." -ForegroundColor Yellow
    Write-Host "Some minor issues detected. Review failed tests above." -ForegroundColor Yellow
} elseif ($successRate -ge 60) {
    Write-Host "`n⚠️ PARTIAL! Your NGC Aether environment has significant issues." -ForegroundColor Yellow
    Write-Host "Several systems are not functioning correctly. Immediate attention required." -ForegroundColor Yellow
} else {
    Write-Host "`n❌ CRITICAL! Your NGC Aether environment has major problems." -ForegroundColor Red
    Write-Host "Most systems are not functioning. Comprehensive troubleshooting required." -ForegroundColor Red
}

Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "==============" -ForegroundColor Cyan

if ($successRate -ge 90) {
    Write-Host "1. ✅ Proceed with user onboarding and training" -ForegroundColor Green
    Write-Host "2. ✅ Set up monitoring and alerting" -ForegroundColor Green
    Write-Host "3. ✅ Configure backup and disaster recovery" -ForegroundColor Green
    Write-Host "4. ✅ Implement security hardening" -ForegroundColor Green
} else {
    Write-Host "1. 🔧 Review and fix failed tests" -ForegroundColor Yellow
    Write-Host "2. 📋 Check service logs for detailed error information" -ForegroundColor Yellow
    Write-Host "3. 🔄 Re-run tests after fixes" -ForegroundColor Yellow
    Write-Host "4. 📞 Contact support if issues persist" -ForegroundColor Yellow
}

Write-Host "`n🔗 Access URLs:" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan
Write-Host "OpenWebUI:      $baseUrl" -ForegroundColor White
Write-Host "Keycloak Admin: $keycloakUrl" -ForegroundColor White
Write-Host "Nginx PM:       http://localhost:81" -ForegroundColor White
Write-Host "Ollama API:     http://localhost:11434" -ForegroundColor White
