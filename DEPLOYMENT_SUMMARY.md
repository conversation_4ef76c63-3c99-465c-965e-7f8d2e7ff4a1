# NGC Aether Production Environment - Complete Deployment Summary

## 🎯 Quick Start Guide

### Prerequisites
- Windows PowerShell 5.1 or later
- Docker Desktop with Docker Compose
- Access to NGC network (IP: ************)
- DNS server: NG-V2K-DC05.northgrum.com (*************)

### 1. Navigate to Project Directory
```powershell
# Navigate to the directory containing docker-compose.yml and .env
cd "C:\Users\<USER>\start-services.ps1
```

### 3. Test Everything (Automated)
```powershell
# Run comprehensive testing
.\test-services.ps1
.\end-to-end-test.ps1
```

## 📋 Manual Step-by-Step Process

### Phase 1: Start Docker Services

```powershell
# 1. Stop any existing containers
docker compose down

# 2. Pull latest images
docker compose pull

# 3. Start services in order
docker compose up -d postgres
docker compose up -d keycloak
docker compose up -d ollama tika pipelines nginx-proxy-manager
docker compose up -d open-webui

# 4. Check status
docker compose ps
```

### Phase 2: Verify Service Health

```powershell
# Check container health
docker compose ps

# Test endpoints
Invoke-WebRequest -Uri "http://localhost:9090/health" -UseBasicParsing  # Keycloak
Invoke-WebRequest -Uri "http://localhost:3000/health" -UseBasicParsing  # OpenWebUI
Invoke-WebRequest -Uri "http://localhost:81/api" -UseBasicParsing       # Nginx PM
Invoke-WebRequest -Uri "http://localhost:11434/api/tags" -UseBasicParsing # Ollama
Invoke-WebRequest -Uri "http://localhost:9998/version" -UseBasicParsing  # Tika
Invoke-WebRequest -Uri "http://localhost:9099/health" -UseBasicParsing   # Pipelines
```

### Phase 3: Configure SSL (Production)

1. **Access Nginx Proxy Manager**: http://localhost:81
   - Default login: <EMAIL> / changeme
   - Change credentials on first login

2. **Create Proxy Hosts** for each service:
   - OpenWebUI: `openwebui.aether-prod.gc1.myngc.com` → `open-webui:8080`
   - Keycloak: `keycloak.aether-prod.gc1.myngc.com` → `keycloak:8080`
   - Ollama: `ollama.aether-prod.gc1.myngc.com` → `ollama:11434`
   - Tika: `tika.aether-prod.gc1.myngc.com` → `tika:9998`
   - Pipelines: `pipelines.aether-prod.gc1.myngc.com` → `pipelines:9099`

3. **Request SSL Certificates** for each domain

### Phase 4: Test SSO Integration

1. **Access Keycloak Admin**: http://localhost:9090
   - Username: admin
   - Password: [from .env KEYCLOAK_ADMIN_PASSWORD]

2. **Verify Realm Configuration**:
   - Realm: openwebui-realm
   - Client: openwebui
   - Valid Redirect URIs configured

3. **Create Test User**:
   - Username: testuser
   - Password: TestPassword123!
   - Email verified and enabled

4. **Test SSO Flow**:
   - Access OpenWebUI: http://localhost:3000
   - Click "Sign in with Keycloak SSO"
   - Login with test credentials
   - Verify successful authentication

### Phase 5: End-to-End Testing

```powershell
# Run comprehensive end-to-end tests
.\end-to-end-test.ps1
```

## 🔗 Service Access URLs

### Development (HTTP)
- **OpenWebUI**: http://localhost:3000
- **Keycloak Admin**: http://localhost:9090
- **Nginx Proxy Manager**: http://localhost:81
- **Ollama API**: http://localhost:11434
- **Tika Server**: http://localhost:9998
- **Pipelines**: http://localhost:9099

### Production (HTTPS)
- **OpenWebUI**: https://openwebui.aether-prod.gc1.myngc.com
- **Keycloak Admin**: https://keycloak.aether-prod.gc1.myngc.com
- **Nginx Proxy Manager**: https://npm.aether-prod.gc1.myngc.com:81
- **Ollama API**: https://ollama.aether-prod.gc1.myngc.com
- **Tika Server**: https://tika.aether-prod.gc1.myngc.com
- **Pipelines**: https://pipelines.aether-prod.gc1.myngc.com

## 🔐 Default Credentials

### Keycloak Admin
- Username: `admin`
- Password: `NgcAether2024!KeycloakAdmin#Secure$$Prod` (from .env)

### Nginx Proxy Manager
- Email: `<EMAIL>`
- Password: `changeme` (change on first login)

### Test User (SSO)
- Username: `testuser`
- Password: `TestPassword123!`

## 🛠️ Troubleshooting

### Common Issues

1. **Services Won't Start**
   ```powershell
   # Check logs
   docker compose logs [service-name]
   
   # Restart specific service
   docker compose restart [service-name]
   ```

2. **SSL Certificate Issues**
   - Verify DNS resolution
   - Check domain ownership
   - Ensure ports 80/443 are accessible

3. **SSO Authentication Fails**
   - Verify OAUTH_REDIRECT_URI matches Keycloak client config
   - Check OAUTH_CLIENT_SECRET matches Keycloak
   - Ensure OIDC discovery endpoint is accessible

4. **Database Connection Issues**
   ```powershell
   # Test PostgreSQL connectivity
   docker exec ai-postgres pg_isready -U openwebui -d openwebui
   ```

### Useful Commands

```powershell
# View all container logs
docker compose logs -f

# Restart all services
docker compose restart

# Stop all services
docker compose down

# Remove all data (CAUTION!)
docker compose down -v

# Check resource usage
docker stats

# Access container shell
docker exec -it [container-name] /bin/bash
```

## 📊 Health Check Commands

```powershell
# Quick health check script
$services = @{
    "Keycloak" = "http://localhost:9090/health"
    "OpenWebUI" = "http://localhost:3000/health"
    "Nginx PM" = "http://localhost:81/api"
    "Ollama" = "http://localhost:11434/api/tags"
    "Tika" = "http://localhost:9998/version"
    "Pipelines" = "http://localhost:9099/health"
}

foreach ($service in $services.Keys) {
    try {
        $response = Invoke-WebRequest -Uri $services[$service] -UseBasicParsing -TimeoutSec 10
        Write-Host "✅ $service is healthy" -ForegroundColor Green
    } catch {
        Write-Host "❌ $service is not responding" -ForegroundColor Red
    }
}
```

## 🎉 Success Criteria

Your NGC Aether environment is ready when:

- [ ] All Docker containers are running and healthy
- [ ] All service endpoints respond correctly
- [ ] SSL certificates are configured (for production)
- [ ] Keycloak realm and client are configured
- [ ] SSO authentication flow works end-to-end
- [ ] Test user can login and access OpenWebUI
- [ ] AI services (Ollama, Tika, Pipelines) are accessible
- [ ] Database connectivity is working
- [ ] No critical errors in container logs

## 📞 Support

For issues or questions:
1. Check container logs: `docker compose logs [service-name]`
2. Review troubleshooting section above
3. Run diagnostic scripts: `.\test-services.ps1`
4. Contact system administrator with specific error messages

## 🔄 Maintenance

### Regular Tasks
- Monitor container health and resource usage
- Review and rotate logs
- Update container images regularly
- Backup database and configuration
- Monitor SSL certificate expiration
- Review security logs and access patterns
