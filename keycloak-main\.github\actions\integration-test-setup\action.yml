name: Setup integration test
description: Download Maven caches needed for integration tests

inputs:
  jdk-dist:
    description: JDK distribution
    required: false
    default: temurin
  jdk-version:
    description: JDK version
    required: false
    default: "21"

runs:
  using: composite
  steps:
    - id: update-hosts
      name: Update /etc/hosts
      uses: ./.github/actions/update-hosts

    - id: setup-java
      name: Setup Java
      uses: ./.github/actions/java-setup
      with:
        distribution: ${{ inputs.jdk-dist }}
        java-version: ${{ inputs.jdk-version }}

    - id: maven-cache
      name: Maven cache
      uses: ./.github/actions/maven-cache

    - id: pnpm-store-cache
      name: PNPM store cache
      uses: ./.github/actions/pnpm-store-cache

    - id: download-keycloak
      name: Download Keycloak Maven artifacts
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
      with:
        name: m2-keycloak.tzts

    - id: extract-maven-artifacts
      name: Extract Keycloak Maven artifacts
      shell: bash
      run: |
        if [ "$RUNNER_OS" == "Windows" ]; then
          # zstd binary might be missing in older versions, install only when necessary
          which zstd > /dev/null || choco install zstandard
        fi
        tar -C ~/ --use-compress-program="zstd -d" -xf m2-keycloak.tzts
