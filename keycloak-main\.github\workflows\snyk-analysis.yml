name: Snyk

on: 
  workflow_dispatch:

env:
  MAVEN_ARGS: "-B -nsu -Daether.connector.http.connectionMaxTtl=25"

defaults:
  run:
    shell: bash

permissions:
  contents: read

jobs:
  analysis:
    name: Analysis of Quarkus and Operator
    runs-on: ubuntu-latest
    if: github.repository == 'keycloak/keycloak'
    permissions:
      security-events: write # Required for SARIF uploads
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Build Keycloak
        uses: ./.github/actions/build-keycloak

      - uses: snyk/actions/setup@cdb760004ba9ea4d525f2e043745dfe85bb9077e # master

      - name: Check for vulnerabilities in Quarkus
        run: snyk test --policy-path=${GITHUB_WORKSPACE}/.github/snyk/.snyk --all-projects --prune-repeated-subdependencies --exclude=tests --sarif-file-output=quarkus-report.sarif quarkus/deployment
        continue-on-error: true
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

      - name: Upload Quarkus scanner results to GitHub
        uses: github/codeql-action/upload-sarif@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858 # v3.29.0
        continue-on-error: true
        with:
          sarif_file: quarkus-report.sarif
          category: snyk-quarkus-report

      - name: Check for vulnerabilities in Operator
        run: |
          ./mvnw -Poperator -pl operator -am -DskipTests clean install
          snyk test --policy-path=${GITHUB_WORKSPACE}/.github/snyk/.snyk --all-projects --prune-repeated-subdependencies --exclude=tests --sarif-file-output=operator-report.sarif operator
        continue-on-error: true
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

      - name: Upload Operator scanner results to GitHub
        uses: github/codeql-action/upload-sarif@ce28f5bb42b7a9f2c824e633a3f6ee835bab6858 # v3.29.0
        with:
          sarif_file: operator-report.sarif
          category: snyk-operator-report
