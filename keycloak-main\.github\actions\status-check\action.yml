name: Check required jobs
description: Check if all required jobs where successful or skipped

inputs:
  jobs:
    description: Jobs (value must be toJSON(needs))
    required: true

runs:
  using: "composite"
  steps:
    - id: check-jobs
      name: Check jobs
      shell: bash
      run: |
        JOBS='${{ inputs.jobs }}'

        echo "Job status:"
        echo $JOBS | jq -r 'to_entries[] | " - \(.key): \(.value.result)"'
        
        for i in $(echo $JOBS | jq -r 'to_entries[] | .value.result'); do
          if [ "$i" != "success" ] && [ "$i" != "skipped" ]; then
            echo ""
            echo "Status check not okay!"
            exit 1
          fi
        done
