# Security Validation Script for NGC Aether OpenWebUI + Keycloak Deployment
# This script validates all security configurations and identifies any remaining vulnerabilities

param(
    [switch]$Detailed,
    [switch]$Fix
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[✓] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[⚠] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[✗] $Message" -ForegroundColor Red
}

function Write-Critical {
    param([string]$Message)
    Write-Host "[🚨] CRITICAL: $Message" -ForegroundColor Red -BackgroundColor Yellow
}

# Security validation functions
function Test-DatabasePortExposure {
    Write-Status "Checking database port exposure..."
    
    $dockerCompose = Get-Content "docker-compose.yml" -Raw
    
    if ($dockerCompose -match '5432:5432') {
        Write-Critical "Database port 5432 is exposed externally!"
        return $false
    } else {
        Write-Success "Database port is not exposed externally"
        return $true
    }
}

function Test-PrivilegedContainers {
    Write-Status "Checking for privileged containers..."
    
    $dockerCompose = Get-Content "docker-compose.yml" -Raw
    $privilegedCount = ([regex]::Matches($dockerCompose, "privileged:\s*true")).Count
    
    if ($privilegedCount -gt 0) {
        Write-Critical "Found $privilegedCount containers running in privileged mode!"
        return $false
    } else {
        Write-Success "No containers running in privileged mode"
        return $true
    }
}

function Test-WeakPasswords {
    Write-Status "Checking for weak passwords..."
    
    $issues = @()
    
    # Check .env file for weak passwords
    $envContent = Get-Content ".env" -Raw
    
    if ($envContent -match "admin123|test123|password123|123456") {
        $issues += "Weak passwords found in .env file"
    }
    
    # Check realm-config.json for sample users
    $realmContent = Get-Content "realm-config.json" -Raw
    
    if ($realmContent -match '"users":\s*\[' -and $realmContent -match "admin123|test123") {
        $issues += "Sample users with weak passwords found in realm-config.json"
    }
    
    if ($issues.Count -gt 0) {
        foreach ($issue in $issues) {
            Write-Critical $issue
        }
        return $false
    } else {
        Write-Success "No weak passwords detected"
        return $true
    }
}

function Test-SecretStrength {
    Write-Status "Checking secret strength..."
    
    $envContent = Get-Content ".env"
    $weakSecrets = @()
    
    foreach ($line in $envContent) {
        if ($line -match "PASSWORD=(.+)" -or $line -match "SECRET=(.+)") {
            $secret = $matches[1]
            if ($secret.Length -lt 16) {
                $weakSecrets += "Short secret found: $($line.Split('=')[0])"
            }
            if ($secret -notmatch "[A-Z]" -or $secret -notmatch "[a-z]" -or $secret -notmatch "[0-9]" -or $secret -notmatch "[!@#$%^&*]") {
                $weakSecrets += "Weak secret complexity: $($line.Split('=')[0])"
            }
        }
    }
    
    if ($weakSecrets.Count -gt 0) {
        foreach ($secret in $weakSecrets) {
            Write-Warning $secret
        }
        return $false
    } else {
        Write-Success "All secrets meet strength requirements"
        return $true
    }
}

function Test-NetworkSegmentation {
    Write-Status "Checking network segmentation..."
    
    $dockerCompose = Get-Content "docker-compose.yml" -Raw
    
    if ($dockerCompose -match "frontend-network" -and $dockerCompose -match "backend-network" -and $dockerCompose -match "database-network") {
        Write-Success "Network segmentation implemented"
        return $true
    } else {
        Write-Warning "Network segmentation not fully implemented"
        return $false
    }
}

function Test-SecurityOptions {
    Write-Status "Checking container security options..."
    
    $dockerCompose = Get-Content "docker-compose.yml" -Raw
    
    $securityFeatures = @(
        "no-new-privileges:true",
        "cap_drop:",
        "cap_add:",
        "seccomp:"
    )
    
    $missingFeatures = @()
    foreach ($feature in $securityFeatures) {
        if ($dockerCompose -notmatch [regex]::Escape($feature)) {
            $missingFeatures += $feature
        }
    }
    
    if ($missingFeatures.Count -gt 0) {
        Write-Warning "Missing security features: $($missingFeatures -join ', ')"
        return $false
    } else {
        Write-Success "Container security options properly configured"
        return $true
    }
}

function Test-DnsConfiguration {
    Write-Status "Checking DNS configuration for NGC Aether..."
    
    $domains = @(
        "aether-prod.gc1.myngc.com",
        "openwebui.aether-prod.gc1.myngc.com",
        "keycloak.aether-prod.gc1.myngc.com",
        "npm.aether-prod.gc1.myngc.com"
    )
    
    $allResolved = $true
    foreach ($domain in $domains) {
        try {
            $result = Resolve-DnsName -Name $domain -ErrorAction SilentlyContinue
            if ($result -and $result.IPAddress -contains "************") {
                Write-Success "$domain resolves correctly to ************"
            } else {
                Write-Warning "$domain does not resolve to expected IP ************"
                $allResolved = $false
            }
        }
        catch {
            Write-Warning "$domain resolution failed"
            $allResolved = $false
        }
    }
    
    return $allResolved
}

function Test-FilePermissions {
    Write-Status "Checking file permissions..."
    
    $sensitiveFiles = @(".env", "realm-config.json")
    $issues = @()
    
    foreach ($file in $sensitiveFiles) {
        if (Test-Path $file) {
            # On Windows, check if file is readable by others
            $acl = Get-Acl $file
            $everyone = [System.Security.Principal.SecurityIdentifier]::new("S-1-1-0")
            
            foreach ($access in $acl.Access) {
                if ($access.IdentityReference.Translate([System.Security.Principal.SecurityIdentifier]) -eq $everyone) {
                    if ($access.FileSystemRights -band [System.Security.AccessControl.FileSystemRights]::Read) {
                        $issues += "$file is readable by Everyone"
                    }
                }
            }
        }
    }
    
    if ($issues.Count -gt 0) {
        foreach ($issue in $issues) {
            Write-Warning $issue
        }
        return $false
    } else {
        Write-Success "File permissions are appropriately restrictive"
        return $true
    }
}

function Test-SslConfiguration {
    Write-Status "Checking SSL/TLS configuration..."
    
    $envContent = Get-Content ".env" -Raw
    
    if ($envContent -match "https://") {
        Write-Success "HTTPS URLs configured"
        
        # Check if SSL is properly configured in environment
        if ($envContent -match "USE_SSL=true") {
            Write-Success "SSL enabled in configuration"
            return $true
        } else {
            Write-Warning "SSL not explicitly enabled in configuration"
            return $false
        }
    } else {
        Write-Warning "HTTPS URLs not configured"
        return $false
    }
}

# Main validation execution
Write-Host "🔒 NGC Aether Security Validation Report" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""

$validationResults = @{}

# Run all security tests
$validationResults["DatabaseSecurity"] = Test-DatabasePortExposure
$validationResults["PrivilegedContainers"] = Test-PrivilegedContainers
$validationResults["WeakPasswords"] = Test-WeakPasswords
$validationResults["SecretStrength"] = Test-SecretStrength
$validationResults["NetworkSegmentation"] = Test-NetworkSegmentation
$validationResults["SecurityOptions"] = Test-SecurityOptions
$validationResults["DnsConfiguration"] = Test-DnsConfiguration
$validationResults["FilePermissions"] = Test-FilePermissions
$validationResults["SslConfiguration"] = Test-SslConfiguration

Write-Host ""
Write-Host "📊 Security Validation Summary" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

$passedTests = ($validationResults.Values | Where-Object { $_ -eq $true }).Count
$totalTests = $validationResults.Count
$failedTests = $totalTests - $passedTests

Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $failedTests" -ForegroundColor Red

if ($failedTests -eq 0) {
    Write-Host ""
    Write-Host "🎉 All security validations passed!" -ForegroundColor Green
    Write-Host "Your deployment is ready for production." -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "⚠️  Security issues detected!" -ForegroundColor Yellow
    Write-Host "Please address the failed validations before production deployment." -ForegroundColor Yellow
    
    Write-Host ""
    Write-Host "Failed Validations:" -ForegroundColor Red
    foreach ($test in $validationResults.GetEnumerator()) {
        if (-not $test.Value) {
            Write-Host "  - $($test.Key)" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "🔧 Next Steps:" -ForegroundColor Cyan
if ($failedTests -gt 0) {
    Write-Host "1. Review and fix the failed security validations above"
    Write-Host "2. Re-run this script to verify fixes"
    Write-Host "3. Proceed with deployment only after all tests pass"
} else {
    Write-Host "1. Deploy services: docker-compose up -d"
    Write-Host "2. Configure SSL certificates in Nginx Proxy Manager"
    Write-Host "3. Test SSO authentication flow"
    Write-Host "4. Set up monitoring and backup procedures"
}

Write-Host ""
Write-Host "Commands:" -ForegroundColor Cyan
Write-Host "  Basic validation:     .\security-validation.ps1"
Write-Host "  Detailed validation:  .\security-validation.ps1 -Detailed"

# Return exit code based on results
if ($failedTests -gt 0) {
    exit 1
} else {
    exit 0
}
