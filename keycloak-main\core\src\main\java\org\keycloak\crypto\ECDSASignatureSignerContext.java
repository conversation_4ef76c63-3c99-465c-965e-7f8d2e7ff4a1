/*
 * Copyright 2023 Red Hat, Inc. and/or its affiliates
 * and other contributors as indicated by the <AUTHOR>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.keycloak.crypto;

/**
 *
 * <AUTHOR>
 */
public class ECDSASignatureSignerContext extends AsymmetricSignatureSignerContext {

    public ECDSASignatureSignerContext(KeyWrapper key) throws SignatureException {
        super(key);
    }

    @Override
    public byte[] sign(byte[] data) throws SignatureException {
        try {
            int size = ECDSAAlgorithm.getSignatureLength(getAlgorithm());
            return ECDSAAlgorithm.asn1derToConcatenatedRS(super.sign(data), size);
        } catch (Exception e) {
            throw new SignatureException("Signing failed", e);
        }
    }
}
