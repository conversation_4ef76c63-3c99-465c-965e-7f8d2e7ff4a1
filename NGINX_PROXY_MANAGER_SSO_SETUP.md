# Nginx Proxy Manager SSO Configuration Guide
## OpenWebUI + Keycloak Integration for NGC Aether

### 🎯 Overview

This guide configures Nginx Proxy Manager (NPM) to enable seamless SSO between Keycloak and OpenWebUI using your NGC Aether domain configuration.

## 📋 Prerequisites

1. **Services Running**: All Docker containers deployed and healthy
2. **DNS Configured**: Subdomains pointing to ************
3. **Firewall**: Ports 80, 443, 81 accessible
4. **Domain Access**: aether-prod.gc1.myngc.com and subdomains

## 🚀 Step-by-Step Configuration

### **Step 1: Access Nginx Proxy Manager**

1. **Navigate to NPM Admin Interface:**
   ```
   http://************:81
   ```

2. **Default Login Credentials:**
   - Email: `<EMAIL>`
   - Password: `changeme`
   - **⚠️ CHANGE THESE IMMEDIATELY!**

3. **First Login Setup:**
   - Change admin email to your NGC email
   - Set strong password
   - Update user details

### **Step 2: Configure Keycloak Proxy Host**

1. **Add New Proxy Host:**
   - Click "Proxy Hosts" → "Add Proxy Host"

2. **Details Tab:**
   ```
   Domain Names: keycloak.aether-prod.gc1.myngc.com
   Scheme: http
   Forward Hostname/IP: keycloak
   Forward Port: 8080
   Cache Assets: ✓ (enabled)
   Block Common Exploits: ✓ (enabled)
   Websockets Support: ✓ (enabled)
   ```

3. **SSL Tab:**
   ```
   SSL Certificate: Request a new SSL Certificate
   Force SSL: ✓ (enabled)
   HTTP/2 Support: ✓ (enabled)
   HSTS Enabled: ✓ (enabled)
   HSTS Subdomains: ✓ (enabled)
   ```

4. **Advanced Tab (Critical for Keycloak):**
   ```nginx
   # Keycloak-specific proxy configuration
   proxy_set_header Host $host;
   proxy_set_header X-Real-IP $remote_addr;
   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
   proxy_set_header X-Forwarded-Proto $scheme;
   proxy_set_header X-Forwarded-Host $host;
   proxy_set_header X-Forwarded-Port $server_port;
   
   # Buffer settings for large Keycloak responses
   proxy_buffer_size 128k;
   proxy_buffers 4 256k;
   proxy_busy_buffers_size 256k;
   proxy_temp_file_write_size 256k;
   
   # Timeout settings
   proxy_connect_timeout 60s;
   proxy_send_timeout 60s;
   proxy_read_timeout 60s;
   
   # Security headers for Keycloak
   add_header X-Frame-Options "SAMEORIGIN" always;
   add_header X-Content-Type-Options "nosniff" always;
   add_header X-XSS-Protection "1; mode=block" always;
   add_header Referrer-Policy "strict-origin-when-cross-origin" always;
   ```

### **Step 3: Configure OpenWebUI Proxy Host**

1. **Add New Proxy Host:**
   - Click "Proxy Hosts" → "Add Proxy Host"

2. **Details Tab:**
   ```
   Domain Names: openwebui.aether-prod.gc1.myngc.com
   Scheme: http
   Forward Hostname/IP: open-webui
   Forward Port: 8080
   Cache Assets: ✓ (enabled)
   Block Common Exploits: ✓ (enabled)
   Websockets Support: ✓ (enabled)
   ```

3. **SSL Tab:**
   ```
   SSL Certificate: Request a new SSL Certificate
   Force SSL: ✓ (enabled)
   HTTP/2 Support: ✓ (enabled)
   HSTS Enabled: ✓ (enabled)
   HSTS Subdomains: ✓ (enabled)
   ```

4. **Advanced Tab (Critical for SSO):**
   ```nginx
   # OpenWebUI-specific proxy configuration
   proxy_set_header Host $host;
   proxy_set_header X-Real-IP $remote_addr;
   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
   proxy_set_header X-Forwarded-Proto $scheme;
   proxy_set_header X-Forwarded-Host $host;
   proxy_set_header X-Forwarded-Port $server_port;
   
   # OAuth/OIDC specific headers
   proxy_set_header X-Forwarded-Uri $request_uri;
   proxy_set_header X-Original-URL $scheme://$http_host$request_uri;
   
   # WebSocket support for real-time features
   proxy_http_version 1.1;
   proxy_set_header Upgrade $http_upgrade;
   proxy_set_header Connection "upgrade";
   
   # Timeout settings for long-running requests
   proxy_connect_timeout 60s;
   proxy_send_timeout 300s;
   proxy_read_timeout 300s;
   
   # Buffer settings
   proxy_buffering off;
   proxy_request_buffering off;
   
   # Security headers
   add_header X-Frame-Options "SAMEORIGIN" always;
   add_header X-Content-Type-Options "nosniff" always;
   add_header X-XSS-Protection "1; mode=block" always;
   add_header Referrer-Policy "strict-origin-when-cross-origin" always;
   add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: https:; frame-ancestors 'self';" always;
   ```

### **Step 4: Configure NPM Admin Access (Optional)**

1. **Add New Proxy Host:**
   ```
   Domain Names: npm.aether-prod.gc1.myngc.com
   Scheme: http
   Forward Hostname/IP: nginx-proxy-manager
   Forward Port: 81
   ```

2. **SSL Configuration:**
   - Request SSL certificate
   - Enable Force SSL

## 🔧 Environment Configuration Updates

Your `.env` file is already correctly configured, but ensure these values match your NPM setup:

```bash
# Production HTTPS URLs (already configured)
WEBUI_URL=https://openwebui.aether-prod.gc1.myngc.com
KEYCLOAK_HOSTNAME=keycloak.aether-prod.gc1.myngc.com
OAUTH_REDIRECT_URI=https://openwebui.aether-prod.gc1.myngc.com/oauth/oidc/callback
OPENID_PROVIDER_URL=https://keycloak.aether-prod.gc1.myngc.com/realms/openwebui-realm
```

## 🔍 SSL Certificate Configuration

### **Let's Encrypt Setup:**

1. **For each domain, in SSL tab:**
   - Select "Request a new SSL Certificate"
   - Enter email: `<EMAIL>`
   - Accept Terms of Service
   - Click "Save"

2. **Certificate Validation:**
   - NPM will automatically handle ACME challenge
   - Certificates auto-renew every 60 days
   - Monitor expiration in NPM dashboard

### **Custom Certificate (if required):**

If using corporate certificates:
1. Upload certificate files in NPM
2. Configure custom certificate for each domain
3. Ensure certificate includes all subdomains

## 🧪 Testing SSO Flow

### **Step 1: Test Individual Services**

1. **Test Keycloak:**
   ```
   https://keycloak.aether-prod.gc1.myngc.com
   ```
   - Should show Keycloak welcome page
   - Access admin console
   - Verify SSL certificate

2. **Test OpenWebUI:**
   ```
   https://openwebui.aether-prod.gc1.myngc.com
   ```
   - Should show OpenWebUI login page
   - Verify "Sign in with Keycloak SSO" button
   - Check SSL certificate

### **Step 2: Test SSO Authentication**

1. **Initiate SSO Login:**
   - Go to OpenWebUI
   - Click "Sign in with Keycloak SSO"
   - Should redirect to Keycloak

2. **Verify Redirect Chain:**
   ```
   OpenWebUI → Keycloak → Authentication → OpenWebUI
   ```

3. **Check Browser Network Tab:**
   - Verify HTTPS throughout
   - Check for proper redirects
   - Ensure no mixed content warnings

## 🔧 Troubleshooting Common Issues

### **Issue 1: SSL Certificate Errors**

**Symptoms:** Certificate warnings, mixed content
**Solutions:**
1. Verify DNS resolution: `nslookup keycloak.aether-prod.gc1.myngc.com`
2. Check certificate status in NPM
3. Ensure Force SSL enabled
4. Clear browser cache

### **Issue 2: Keycloak Redirect Loops**

**Symptoms:** Infinite redirects, authentication failures
**Solutions:**
1. Check Keycloak hostname configuration:
   ```bash
   KC_HOSTNAME=keycloak.aether-prod.gc1.myngc.com
   KC_HOSTNAME_STRICT=false
   KC_PROXY=edge
   ```
2. Verify proxy headers in NPM Advanced tab
3. Check realm redirect URIs

### **Issue 3: CORS Errors**

**Symptoms:** Cross-origin request blocked
**Solutions:**
1. Add CORS headers in NPM Advanced tab:
   ```nginx
   add_header Access-Control-Allow-Origin "https://openwebui.aether-prod.gc1.myngc.com" always;
   add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
   add_header Access-Control-Allow-Headers "Authorization, Content-Type" always;
   ```

### **Issue 4: WebSocket Connection Failures**

**Symptoms:** Real-time features not working
**Solutions:**
1. Ensure WebSocket headers in NPM:
   ```nginx
   proxy_http_version 1.1;
   proxy_set_header Upgrade $http_upgrade;
   proxy_set_header Connection "upgrade";
   ```

## 📋 Post-Configuration Checklist

- [ ] Keycloak accessible via HTTPS
- [ ] OpenWebUI accessible via HTTPS
- [ ] SSL certificates valid and trusted
- [ ] SSO button appears in OpenWebUI
- [ ] SSO authentication flow works
- [ ] User can login and access OpenWebUI
- [ ] WebSocket connections working
- [ ] No browser security warnings

## 🔐 Security Considerations

1. **NPM Admin Security:**
   - Change default credentials
   - Use strong passwords
   - Enable 2FA if available
   - Restrict admin access by IP

2. **SSL Security:**
   - Use strong cipher suites
   - Enable HSTS
   - Monitor certificate expiration
   - Regular security updates

3. **Proxy Security:**
   - Implement rate limiting
   - Monitor access logs
   - Use fail2ban for brute force protection
   - Regular NPM updates

## 🚀 Deployment Commands

```bash
# 1. Deploy services
docker-compose up -d

# 2. Wait for services to be ready
sleep 60

# 3. Check service health
docker-compose ps

# 4. Test internal connectivity
docker exec ai-nginx-proxy-manager curl -f http://keycloak:8080/health
docker exec ai-nginx-proxy-manager curl -f http://open-webui:8080/health

# 5. Configure NPM via web interface (manual step)
echo "Access NPM at: http://************:81"
```

Your NPM configuration will now provide seamless SSL termination and proper proxy headers for SSO authentication between Keycloak and OpenWebUI!
