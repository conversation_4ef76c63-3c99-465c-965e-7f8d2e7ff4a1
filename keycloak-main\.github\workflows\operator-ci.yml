name: Keycloak Operator CI

on:
  push:
    branches-ignore:
      - main
      - dependabot/**
  pull_request:
  workflow_dispatch:

env:
  MAVEN_ARGS: "-B -nsu -Daether.connector.http.connectionMaxTtl=25"
  MINIKUBE_VERSION: v1.32.0
  KUBERNETES_VERSION: v1.27.10 # OCP 4.14
  MINIKUBE_MEMORY: 4096 # Without explicitly setting memory, mini<PERSON><PERSON> uses ~25% of available memory which might be too little on smaller GitHub runners for running the tests

defaults:
  run:
    shell: bash

concurrency:
  # Only cancel jobs for PR updates
  group: operator-ci-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:

  conditional:
    name: Check conditional workflows and jobs
    runs-on: ubuntu-latest
    outputs:
      operator: ${{ steps.conditional.outputs.operator }}
    permissions:
      contents: read
      pull-requests: read
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - id: conditional
        uses: ./.github/actions/conditional
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

  build:
    name: Build distribution
    if: needs.conditional.outputs.operator == 'true'
    runs-on: ubuntu-latest
    needs: conditional
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Build Keycloak
        uses: ./.github/actions/build-keycloak
        with:
          upload-m2-repo: false
          upload-dist: true

  test-local-apiserver:
    name: Test local apiserver
    runs-on: ubuntu-latest
    needs: [build]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup Java
        uses: ./.github/actions/java-setup

      - name: Test operator running locally
        run: |
          ./mvnw install -Poperator -pl :keycloak-operator -am

  test-remote:
    name: Test remote
    runs-on: ubuntu-latest
    needs: [build]
    strategy:
          matrix:
            suite: [slow, fast]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Set version
        id: vars
        run: echo "version_remote=0.0.1-${GITHUB_SHA::6}" >> $GITHUB_ENV

      - name: Setup Java
        uses: ./.github/actions/java-setup

      - name: Setup Minikube-Kubernetes
        uses: manusa/actions-setup-minikube@b589f2d61bf96695c546929c72b38563e856059d # v2.14.0
        with:
          minikube version: ${{ env.MINIKUBE_VERSION }}
          kubernetes version: ${{ env.KUBERNETES_VERSION }}
          github token: ${{ secrets.GITHUB_TOKEN }}
          driver: docker
          start args: --addons=ingress --memory=${{ env.MINIKUBE_MEMORY }} --cni cilium --cpus=max

      - name: Download keycloak distribution
        id: download-keycloak-dist
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: keycloak-dist
          path: quarkus/container

      - name: Build Keycloak Docker images
        run: |
          eval $(minikube -p minikube docker-env)
          (cd quarkus/container && docker build --build-arg KEYCLOAK_DIST=$(ls keycloak-*.tar.gz) . -t keycloak:${{ env.version_remote }})
          (cd operator && ./scripts/build-testing-docker-images.sh ${{ env.version_remote }} keycloak custom-keycloak)

      - name: Test operator running in cluster
        run: |
          declare -A PARAMS
          PARAMS["slow"]="-Dkc.quarkus.tests.groups=slow"
          PARAMS["fast"]='-Dkc.quarkus.tests.groups=!slow'
          
          eval $(minikube -p minikube docker-env)
          ./mvnw install -Poperator -pl :keycloak-operator -am \
              -Dquarkus.container-image.build=true \
              -Dquarkus.kubernetes.image-pull-policy=IfNotPresent \
              -Dkc.operator.keycloak.image=keycloak:${{ env.version_remote }} \
              -Dquarkus.kubernetes.env.vars.kc-operator-keycloak-image-pull-policy=Never \
              -Dtest.operator.custom.image=custom-keycloak:${{ env.version_remote }} \
              --no-transfer-progress -Dtest.operator.deployment=remote ${PARAMS["${{ matrix.suite }}"]}

  test-olm:
    name: Test OLM installation
    runs-on: ubuntu-latest
    needs: [build]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Setup Java
        uses: ./.github/actions/java-setup

      - name: Setup Minikube-Kubernetes
        uses: manusa/actions-setup-minikube@b589f2d61bf96695c546929c72b38563e856059d # v2.14.0
        with:
          minikube version: ${{ env.MINIKUBE_VERSION }}
          kubernetes version: ${{ env.KUBERNETES_VERSION }}
          github token: ${{ secrets.GITHUB_TOKEN }}
          driver: docker
          start args: --memory=${{ env.MINIKUBE_MEMORY }} --addons=registry --insecure-registry=************/24

      - name: Install OPM
        uses: redhat-actions/openshift-tools-installer@144527c7d98999f2652264c048c7a9bd103f8a82 # v1.13.1
        with:
          source: github
          opm: 1.21.0

      - name: Install Yq
        run: sudo snap install yq

      - name: Install OLM
        working-directory: operator
        run: ./scripts/install-olm.sh

      - name: Download keycloak distribution
        id: download-keycloak-dist
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: keycloak-dist
          path: quarkus/container

      - name: Arrange OLM test installation
        working-directory: operator
        run: |
          echo "Minikube IP $(minikube ip)"
          eval $(minikube -p minikube docker-env)
          REGISTRY=$(minikube ip):5000 ./scripts/olm-testing.sh ${GITHUB_SHA::6}

      - name: Deploy an example Keycloak and wait for it to be ready
        working-directory: operator
        run: |
          kubectl apply -f src/test/resources/example-postgres.yaml
          ./scripts/check-crds-installed.sh
          kubectl apply -f src/test/resources/example-db-secret.yaml
          kubectl apply -f src/test/resources/example-tls-secret.yaml
          kubectl apply -f src/test/resources/example-keycloak.yaml
          kubectl apply -f src/test/resources/example-realm.yaml
          # Wait for the CRs to be ready
          ./scripts/check-examples-installed.sh
          
      - name: Single namespace cleanup
        working-directory: operator
        run: |
          kubectl delete -f src/test/resources/example-postgres.yaml
          kubectl delete -f src/test/resources/example-db-secret.yaml
          kubectl delete -f src/test/resources/example-tls-secret.yaml
          kubectl delete -f src/test/resources/example-keycloak.yaml
          kubectl delete -f src/test/resources/example-realm.yaml
      
      - name: Arrange OLM test installation for all namespaces
        working-directory: operator
        run: |
          kubectl patch csv keycloak-operator.v86400000.0.0 --type merge --patch '{"spec": {"installModes": [{"type": "AllNamespaces","supported": true}]}}'
          kubectl patch operatorgroup og --type json --patch '[{"op":"remove","path":"/spec/targetNamespaces"}]'
       
      - name: Deploy an example Keycloak in a different namespace and wait for it to be ready
        working-directory: operator
        run: |
          kubectl create ns keycloak
          kubectl apply -f src/test/resources/example-postgres.yaml -n keycloak
          kubectl apply -f src/test/resources/example-db-secret.yaml -n keycloak
          kubectl apply -f src/test/resources/example-tls-secret.yaml -n keycloak
          kubectl apply -f src/test/resources/example-keycloak.yaml -n keycloak
          kubectl apply -f src/test/resources/example-realm.yaml -n keycloak
          # Wait for the CRs to be ready
          ./scripts/check-examples-installed.sh keycloak

  check:
    name: Status Check - Keycloak Operator CI
    if: always()
    needs:
      - conditional
      - build
      - test-local-apiserver
      - test-remote
      - test-olm
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: ./.github/actions/status-check
        with:
          jobs: ${{ toJSON(needs) }}
