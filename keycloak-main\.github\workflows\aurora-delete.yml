name: Aurora Delete

on:
  workflow_dispatch:
    inputs:
      name:
        description: 'The name of the Aurora DB cluster'
        type: string
        required: true
      region:
        description: 'The AWS region used to host the Aurora DB'
        type: string
        required: true

permissions:
  contents: read

jobs:
  delete:
    name: Delete Aurora DB
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Initialize AWS client
        run: |
          aws configure set aws_access_key_id ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws configure set aws_secret_access_key ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws configure set region ${{ inputs.region }}

      - id: delete
        shell: bash
        run: ./aurora_delete.sh
        working-directory: .github/scripts/aws/rds
        env:
          AURORA_CLUSTER: ${{ inputs.name }}
          AURORA_REGION: ${{ inputs.region }}
