/*
 * Copyright 2017 Red Hat, Inc. and/or its affiliates
 * and other contributors as indicated by the <AUTHOR>
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 * http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.keycloak.exceptions;

import org.keycloak.representations.JsonWebToken;

/**
 * Thrown when token signature is invalid.
 * <AUTHOR>
 */
public class TokenSignatureInvalidException extends TokenVerificationException {

    public TokenSignatureInvalidException(JsonWebToken token) {
        super(token);
    }

    public TokenSignatureInvalidException(JsonWebToken token, String message) {
        super(token, message);
    }

    public TokenSignatureInvalidException(JsonWebToken token, String message, Throwable cause) {
        super(token, message, cause);
    }

    public TokenSignatureInvalidException(JsonWebToken token, Throwable cause) {
        super(token, cause);
    }

}
