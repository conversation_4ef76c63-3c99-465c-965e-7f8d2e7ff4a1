# version: "3.9"  # Removed - version attribute is obsolete in newer Docker Compose

# Production-Ready Docker Compose for AI Services Platform with CUDA support
# Optimized for EC2 RHEL 9 with full GPU acceleration and comprehensive service integration
#
# SECURITY REQUIREMENTS:
# 1. Copy .env.example to .env and update ALL passwords and secrets
# 2. Generate secure random values for all credentials
# 3. Configure proper firewall rules for exposed ports
# 4. Enable 2FA where possible and use strong passwords
# 5. Regularly update container images for security patches

services:
  # PostgreSQL Database - Multi-purpose database for NPM and OpenWebUI
  postgres:
    image: postgres:15
    container_name: ai-postgres
    networks:
      - database-network
      - backend-network
    # SECURITY: Database port NOT exposed externally - internal access only
    # ports:
    #   - "5432:5432"  # REMOVED for security
    restart: unless-stopped
    shm_size: 256mb
    security_opt:
      - no-new-privileges:true
      - seccomp:unconfined
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
      - DAC_OVERRIDE
    user: postgres
    read_only: false  # PostgreSQL needs write access to data directory
    tmpfs:
      - /tmp
      - /var/tmp
    environment:
      # Multiple databases configuration
      POSTGRES_DB: ${POSTGRES_DB:-npm}
      POSTGRES_USER: ${POSTGRES_USER:-npm}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      # OpenWebUI database configuration
      OPENWEBUI_DB_NAME: ${OPENWEBUI_DB_NAME:-openwebui}
      OPENWEBUI_DB_USER: ${OPENWEBUI_DB_USER:-openwebui}
      OPENWEBUI_DB_PASSWORD: ${OPENWEBUI_DB_PASSWORD}
      # Keycloak database configuration
      KEYCLOAK_DB_NAME: ${KEYCLOAK_DB_NAME:-keycloak}
      KEYCLOAK_DB_USER: ${KEYCLOAK_DB_USER:-keycloak}
      KEYCLOAK_DB_PASSWORD: ${KEYCLOAK_DB_PASSWORD}
      # Performance and security settings
      POSTGRES_INITDB_ARGS: "--auth-host=md5 --auth-local=trust"
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init-multiple-databases.sh:/docker-entrypoint-initdb.d/init-multiple-databases.sh:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-npm} -d ${POSTGRES_DB:-npm}"]
      interval: 15s
      timeout: 10s
      retries: 5
      start_period: 30s
    command: >
      postgres
      -c shared_preload_libraries=''
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100

  # Keycloak Identity and Access Management - Production Configuration
  keycloak:
    image: quay.io/keycloak/keycloak:23.0
    container_name: ai-keycloak
    networks:
      - backend-network
      - database-network
    ports:
      - "9090:8080"
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
      - seccomp:unconfined
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
      - DAC_OVERRIDE
    read_only: false  # Keycloak needs write access for themes and deployments
    tmpfs:
      - /tmp
      - /var/tmp
    environment:
      # Database Configuration
      KC_DB: postgres
      KC_DB_URL: *******************************/${KEYCLOAK_DB_NAME:-keycloak}
      KC_DB_USERNAME: ${KEYCLOAK_DB_USER:-keycloak}
      KC_DB_PASSWORD: ${KEYCLOAK_DB_PASSWORD}
      # Hostname and Proxy Configuration
      KC_HOSTNAME: ${KEYCLOAK_HOSTNAME}
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false
      KC_HTTP_ENABLED: true
      KC_PROXY: edge
      # Admin Configuration
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN_USER}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
      # Performance and Features
      KC_HEALTH_ENABLED: true
      KC_METRICS_ENABLED: true
      KC_LOG_LEVEL: ${KC_LOG_LEVEL:-INFO}
      # JVM Optimization
      JAVA_OPTS: "-Xms512m -Xmx2g -XX:MetaspaceSize=96M -XX:MaxMetaspaceSize=256m -Djava.net.preferIPv4Stack=true"
      # Database Connection Pool
      KC_DB_POOL_INITIAL_SIZE: 5
      KC_DB_POOL_MIN_SIZE: 5
      KC_DB_POOL_MAX_SIZE: 20
      # Cache Configuration
      KC_CACHE: local
      KC_CACHE_STACK: tcp
    volumes:
      # Realm configuration import
      - ./realm-config.json:/opt/keycloak/data/import/realm-config.json:ro
      # Data persistence
      - keycloak_data:/opt/keycloak/data
    command: start --import-realm
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health/ready || curl -f http://localhost:8080/health || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 8
      start_period: 90s

  # Nginx Proxy Manager - SSL Termination and Reverse Proxy
  nginx-proxy-manager:
    image: jc21/nginx-proxy-manager:latest
    container_name: ai-nginx-proxy-manager
    networks:
      - frontend-network
      - backend-network
      - database-network
    ports:
      - "80:80"     # Public HTTP Port
      - "443:443"   # Public HTTPS Port
      - "81:81"     # Admin Web Port
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
      - seccomp:unconfined
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
      - DAC_OVERRIDE
      - NET_BIND_SERVICE
    read_only: false  # NPM needs write access for certificates and config
    tmpfs:
      - /tmp
      - /var/tmp
    environment:
      # Database Configuration
      DB_POSTGRES_HOST: postgres
      DB_POSTGRES_PORT: 5432
      DB_POSTGRES_USER: ${POSTGRES_USER:-npm}
      DB_POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      DB_POSTGRES_NAME: ${POSTGRES_DB:-npm}
      # Network Configuration
      DISABLE_IPV6: 'true'
    volumes:
      - npm_data:/data
      - npm_letsencrypt:/etc/letsencrypt
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:81/api"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Ollama - Local LLM Inference with Full CUDA Support
  ollama:
    image: ollama/ollama:latest
    container_name: ai-ollama
    runtime: nvidia
    # SECURITY: Removed privileged mode, using specific capabilities
    # privileged: true  # REMOVED for security
    networks:
      - backend-network
    ports:
      - "11434:11434"
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
      - seccomp:unconfined
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
      - DAC_OVERRIDE
      - SYS_ADMIN  # Required for GPU access
    read_only: false  # Ollama needs write access for models
    tmpfs:
      - /tmp
      - /var/tmp
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    environment:
      # NVIDIA GPU Configuration
      NVIDIA_VISIBLE_DEVICES: all
      NVIDIA_DRIVER_CAPABILITIES: compute,utility
      # Ollama Configuration
      OLLAMA_ORIGINS: "*"
      OLLAMA_INSECURE: "true"
      OLLAMA_HOST: "0.0.0.0:11434"
      OLLAMA_KEEP_ALIVE: ${OLLAMA_KEEP_ALIVE:-5m}
      OLLAMA_MAX_LOADED_MODELS: ${OLLAMA_MAX_LOADED_MODELS:-3}
    volumes:
      - ollama_data:/root/.ollama
      - /etc/ssl/certs:/etc/ssl/certs:ro
    healthcheck:
      test: ["CMD-SHELL", "ollama list || exit 1"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 60s

  # OpenWebUI - Main Application with Full CUDA and Feature Integration
  open-webui:
    image: ghcr.io/open-webui/open-webui:cuda
    container_name: ai-open-webui
    runtime: nvidia
    # SECURITY: Removed privileged mode, using specific capabilities
    # privileged: true  # REMOVED for security
    networks:
      - backend-network
      - database-network
    ports:
      - "3000:8080"
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
      - seccomp:unconfined
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
      - DAC_OVERRIDE
      - SYS_ADMIN  # Required for GPU access
    read_only: false  # OpenWebUI needs write access for data
    tmpfs:
      - /tmp
      - /var/tmp
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    environment:
      # Database Configuration
      DATABASE_URL: "postgresql://${OPENWEBUI_DB_USER:-openwebui}:${OPENWEBUI_DB_PASSWORD}@postgres:5432/${OPENWEBUI_DB_NAME:-openwebui}"

      # OpenWebUI Core Configuration
      WEBUI_NAME: ${WEBUI_NAME:-"OpenWebUI Multi-User AI Platform"}
      WEBUI_URL: ${WEBUI_URL}
      WEBUI_SECRET_KEY: ${WEBUI_SECRET_KEY}
      WEBUI_LOG_LEVEL: ${WEBUI_LOG_LEVEL:-INFO}

      # Authentication Configuration
      WEBUI_AUTH: true
      ENABLE_OAUTH_SIGNUP: ${ENABLE_OAUTH_SIGNUP:-true}
      OAUTH_MERGE_ACCOUNTS_BY_EMAIL: ${OAUTH_MERGE_ACCOUNTS_BY_EMAIL:-true}

      # OIDC/Keycloak Configuration
      OAUTH_PROVIDER_NAME: ${OAUTH_PROVIDER_NAME:-"Keycloak SSO"}
      OAUTH_CLIENT_ID: ${OAUTH_CLIENT_ID}
      OAUTH_CLIENT_SECRET: ${OAUTH_CLIENT_SECRET}
      OPENID_PROVIDER_URL: ${OPENID_PROVIDER_URL}
      OAUTH_SCOPES: ${OAUTH_SCOPES:-"openid profile email"}
      OAUTH_USERNAME_CLAIM: "preferred_username"
      OAUTH_EMAIL_CLAIM: "email"
      OAUTH_PICTURE_CLAIM: "picture"
      OAUTH_ROLES_CLAIM: ${OAUTH_ROLES_CLAIM:-"openwebui_roles"}
      OAUTH_REDIRECT_URI: ${OAUTH_REDIRECT_URI}

      # User Management
      DEFAULT_USER_ROLE: ${DEFAULT_USER_ROLE:-"user"}
      ENABLE_ADMIN_EXPORT: ${ENABLE_ADMIN_EXPORT:-true}
      ENABLE_ADMIN_CHAT_ACCESS: ${ENABLE_ADMIN_CHAT_ACCESS:-true}

      # Security Settings
      ENABLE_COMMUNITY_SHARING: ${ENABLE_COMMUNITY_SHARING:-false}
      ENABLE_MESSAGE_RATING: ${ENABLE_MESSAGE_RATING:-true}
      ENABLE_MODEL_FILTER: ${ENABLE_MODEL_FILTER:-true}

      # Service Integration URLs
      OLLAMA_BASE_URL: "http://ollama:11434"
      OPENWEBUI_PIPELINES_URL: "http://pipelines:9099"
      TIKA_SERVER_URL: "http://tika:9998"

      # CUDA Configuration
      NVIDIA_VISIBLE_DEVICES: all
      NVIDIA_DRIVER_CAPABILITIES: compute,utility

      # AI and RAG Features
      DEFAULT_MODELS: ${DEFAULT_MODELS:-"llama2:latest"}
      ENABLE_IMAGE_GENERATION: ${ENABLE_IMAGE_GENERATION:-true}
      ENABLE_RAG_WEB_SEARCH: ${ENABLE_RAG_WEB_SEARCH:-true}
      ENABLE_RAG_LOCAL_WEB_FETCH: ${ENABLE_RAG_LOCAL_WEB_FETCH:-true}
      RAG_WEB_SEARCH_ENGINE: ${RAG_WEB_SEARCH_ENGINE:-"searxng"}
      RAG_EMBEDDING_ENGINE: "ollama"
      RAG_EMBEDDING_MODEL: ${RAG_EMBEDDING_MODEL:-"nomic-embed-text:latest"}
    volumes:
      - open_webui_data:/app/backend/data
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      postgres:
        condition: service_healthy
      keycloak:
        condition: service_healthy
      ollama:
        condition: service_healthy
      tika:
        condition: service_healthy
      pipelines:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s

  # OpenWebUI Pipelines - Advanced Pipeline Processing Service
  pipelines:
    image: ghcr.io/open-webui/pipelines:main
    container_name: ai-pipelines
    networks:
      - backend-network
    ports:
      - "9099:9099"
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
      - seccomp:unconfined
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
      - DAC_OVERRIDE
    read_only: false  # Pipelines needs write access for data processing
    tmpfs:
      - /tmp
      - /var/tmp
    environment:
      # Ollama Integration
      OLLAMA_BASE_URL: "http://ollama:11434"
      # OpenWebUI Integration
      OPENWEBUI_URL: "http://open-webui:8080"
      # API Configuration
      ENABLE_OPENAI_API: ${ENABLE_OPENAI_API:-true}
      ENABLE_OLLAMA_API: ${ENABLE_OLLAMA_API:-true}
      # Pipeline Configuration
      PIPELINES_URLS: ${PIPELINES_URLS:-"https://github.com/open-webui/pipelines"}
    volumes:
      - pipelines_data:/app/pipelines
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      ollama:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9099/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 45s

  # Apache Tika - Advanced Document Processing
  tika:
    image: apache/tika:latest-full
    container_name: ai-tika
    networks:
      - backend-network
    ports:
      - "9998:9998"
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
      - seccomp:unconfined
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
      - DAC_OVERRIDE
    read_only: false  # Tika needs write access for temporary files
    tmpfs:
      - /tmp
      - /var/tmp
    environment:
      # JVM Configuration for better performance
      JAVA_OPTS: "-Xmx2g -Xms512m"
      TIKA_CONFIG_FILE: /tika-config.xml
    volumes:
      - tika_data:/tmp/tika-server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9998/version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

# Network Configuration - Segmented for Security
networks:
  # Frontend network - public-facing services
  frontend-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24
          gateway: **********
    driver_opts:
      com.docker.network.bridge.name: aether-frontend

  # Backend network - application services (internal only)
  backend-network:
    driver: bridge
    internal: false  # Set to true for complete isolation in production
    ipam:
      config:
        - subnet: **********/24
          gateway: **********
    driver_opts:
      com.docker.network.bridge.name: aether-backend

  # Database network - database access only (internal only)
  database-network:
    driver: bridge
    internal: true  # Database network is completely internal
    ipam:
      config:
        - subnet: **********/24
          gateway: **********
    driver_opts:
      com.docker.network.bridge.name: aether-database

# Volume Configuration
volumes:
  # Database volumes
  postgres_data:
    driver: local
  keycloak_data:
    driver: local

  # Application volumes
  open_webui_data:
    driver: local
  ollama_data:
    driver: local
  pipelines_data:
    driver: local
  tika_data:
    driver: local

  # Nginx Proxy Manager volumes
  npm_data:
    driver: local
  npm_letsencrypt:
    driver: local
