/*
 * Copyright 2023 Red Hat, Inc. and/or its affiliates
 * and other contributors as indicated by the <AUTHOR>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.keycloak.jose.jwk;

import org.keycloak.crypto.KeyType;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON><PERSON></a>
 */
public class OKPPublicJWK extends JWK {

    public static final String OKP = KeyType.OKP;

    public static final String CRV = "crv";
    public static final String X = "x";

    @JsonProperty(CRV)
    private String crv;

    @JsonProperty(X)
    private String x;

    public String getCrv() {
        return crv;
    }

    public void setCrv(String crv) {
        this.crv = crv;
    }

    public String getX() {
        return x;
    }

    public void setX(String x) {
        this.x = x;
    }

}
