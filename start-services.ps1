# NGC Aether Production Environment Startup Script
# This script starts all Docker services in the correct order and verifies their health

Write-Host "🚀 Starting NGC Aether Production Environment..." -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Function to check if a service is healthy
function Wait-ForServiceHealth {
    param(
        [string]$ServiceName,
        [int]$MaxWaitMinutes = 5
    )
    
    Write-Host "⏳ Waiting for $ServiceName to become healthy..." -ForegroundColor Yellow
    $timeout = (Get-Date).AddMinutes($MaxWaitMinutes)
    
    do {
        $health = docker compose ps --format json | ConvertFrom-Json | Where-Object { $_.Service -eq $ServiceName }
        if ($health -and $health.Health -eq "healthy") {
            Write-Host "✅ $ServiceName is healthy!" -ForegroundColor Green
            return $true
        }
        Start-Sleep -Seconds 10
        Write-Host "." -NoNewline -ForegroundColor Yellow
    } while ((Get-Date) -lt $timeout)
    
    Write-Host "❌ $ServiceName failed to become healthy within $MaxWaitMinutes minutes" -ForegroundColor Red
    return $false
}

# Function to display service status
function Show-ServiceStatus {
    Write-Host "`n📊 Current Service Status:" -ForegroundColor Cyan
    Write-Host "=========================" -ForegroundColor Cyan
    docker compose ps
}

# Step 1: Check if docker-compose.yml exists
if (-not (Test-Path "docker-compose.yml")) {
    Write-Host "❌ docker-compose.yml not found in current directory!" -ForegroundColor Red
    Write-Host "Please navigate to the directory containing your Docker configuration files." -ForegroundColor Yellow
    exit 1
}

# Step 2: Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "❌ .env file not found!" -ForegroundColor Red
    Write-Host "Please ensure your .env file is in the current directory." -ForegroundColor Yellow
    exit 1
}

# Step 3: Stop any existing containers
Write-Host "🛑 Stopping any existing containers..." -ForegroundColor Yellow
docker compose down

# Step 4: Pull latest images
Write-Host "📥 Pulling latest Docker images..." -ForegroundColor Yellow
docker compose pull

# Step 5: Start core infrastructure services first
Write-Host "🏗️ Starting core infrastructure services..." -ForegroundColor Yellow
docker compose up -d postgres

# Wait for PostgreSQL to be healthy
if (-not (Wait-ForServiceHealth "postgres" 3)) {
    Write-Host "❌ PostgreSQL failed to start. Exiting..." -ForegroundColor Red
    exit 1
}

# Step 6: Start Keycloak
Write-Host "🔐 Starting Keycloak..." -ForegroundColor Yellow
docker compose up -d keycloak

# Wait for Keycloak to be healthy
if (-not (Wait-ForServiceHealth "keycloak" 5)) {
    Write-Host "❌ Keycloak failed to start. Exiting..." -ForegroundColor Red
    exit 1
}

# Step 7: Start supporting services
Write-Host "🔧 Starting supporting services..." -ForegroundColor Yellow
docker compose up -d ollama tika pipelines

# Wait for supporting services
Wait-ForServiceHealth "ollama" 3
Wait-ForServiceHealth "tika" 2
Wait-ForServiceHealth "pipelines" 2

# Step 8: Start Nginx Proxy Manager
Write-Host "🌐 Starting Nginx Proxy Manager..." -ForegroundColor Yellow
docker compose up -d nginx-proxy-manager

Wait-ForServiceHealth "nginx-proxy-manager" 2

# Step 9: Start OpenWebUI (main application)
Write-Host "🎯 Starting OpenWebUI..." -ForegroundColor Yellow
docker compose up -d open-webui

# Wait for OpenWebUI to be healthy
if (-not (Wait-ForServiceHealth "open-webui" 5)) {
    Write-Host "❌ OpenWebUI failed to start. Exiting..." -ForegroundColor Red
    exit 1
}

# Step 10: Final status check
Write-Host "`n🎉 All services started!" -ForegroundColor Green
Show-ServiceStatus

Write-Host "`n📋 Service Access URLs:" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan
Write-Host "🔐 Keycloak Admin:     http://localhost:9090" -ForegroundColor White
Write-Host "🎯 OpenWebUI:          http://localhost:3000" -ForegroundColor White
Write-Host "🌐 Nginx Proxy Mgr:    http://localhost:81" -ForegroundColor White
Write-Host "🤖 Ollama API:         http://localhost:11434" -ForegroundColor White
Write-Host "📄 Tika Server:        http://localhost:9998" -ForegroundColor White
Write-Host "🔧 Pipelines:          http://localhost:9099" -ForegroundColor White

Write-Host "`n✅ NGC Aether Production Environment is ready!" -ForegroundColor Green
