<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Copyright 2019 Red Hat, Inc. and/or its affiliates
  ~ and other contributors as indicated by the <AUTHOR>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="urn:jboss:domain:keycloak-saml:1.2"
           xmlns="urn:jboss:domain:keycloak-saml:1.2"
           elementFormDefault="qualified"
           attributeFormDefault="unqualified"
           version="1.0">

    <!-- The subsystem root element -->
    <xs:element name="subsystem" type="subsystem-type"/>

    <xs:complexType name="subsystem-type">
        <xs:annotation>
            <xs:documentation>
                <![CDATA[
                    The Keycloak SAML adapter subsystem, used to register deployments managed by Keycloak SAML adapter
                ]]>
            </xs:documentation>
        </xs:annotation>
        <xs:all>
            <xs:element name="secure-deployment" minOccurs="0" type="secure-deployment-type"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="secure-deployment-type">
        <xs:all>
            <xs:element name="SP" minOccurs="1" maxOccurs="1" type="sp-type"/>
        </xs:all>
        <xs:attribute name="name" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>The name of the realm.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="sp-type">
        <xs:all>
            <xs:element name="Keys" minOccurs="0" maxOccurs="1" type="keys-type"/>
            <xs:element name="PrincipalNameMapping" minOccurs="0" maxOccurs="1" type="principal-name-mapping-type"/>
            <xs:element name="RoleIdentifiers" minOccurs="0" maxOccurs="1" type="role-identifiers-type"/>
            <xs:element name="RoleMappingsProvider" minOccurs="0" maxOccurs="1" type="role-mappings-provider-type"/>
            <xs:element name="IDP" minOccurs="1" maxOccurs="1" type="identity-provider-type"/>
        </xs:all>
        <xs:attribute name="entityID" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>The entity ID for SAML service provider</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="sslPolicy" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>The ssl policy</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="nameIDPolicyFormat" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>Name ID policy format URN</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="logoutPage" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>URI to a logout page</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="forceAuthentication" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>Redirected unauthenticated request to a login page</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="keepDOMAssertion" type="xs:boolean" use="optional">
                <xs:annotation>
                    <xs:documentation>Attribute to inject the DOM representation of the assertion into the SamlPrincipal (respecting the original syntax). Default value is false</xs:documentation>
                </xs:annotation>
        </xs:attribute>
        <xs:attribute name="isPassive" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>If user isn't logged in just return with an error.  Used to check if a user is already logged in or not</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="turnOffChangeSessionIdOnLogin" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>The session id is changed by default on a successful login.  Change this to true if you want to turn this off</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="identity-provider-type">
        <xs:all minOccurs="1" maxOccurs="1">
            <xs:element name="SingleSignOnService" minOccurs="1" maxOccurs="1" type="single-signon-type"/>
            <xs:element name="SingleLogoutService" minOccurs="0" maxOccurs="1" type="single-logout-type"/>
            <xs:element name="Keys" minOccurs="0" maxOccurs="1" type="keys-type"/>
            <xs:element name="AllowedClockSkew" type="allowed-clock-skew-type" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>This defines the allowed clock skew between IDP and SP in milliseconds. The default value is 0.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:all>
        <xs:attribute name="entityID" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>The entity ID for SAML service provider</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="signaturesRequired" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>Require signatures for single-sign-on and single-logout</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="signatureAlgorithm" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>Algorithm used for signatures</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="signatureCanonicalizationMethod" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>Canonicalization method used for signatures</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="single-signon-type">
        <xs:attribute name="signRequest" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>Sign the SSO requests</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="validateResponseSignature" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>Validate the SSO response signature</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="validateAssertionSignature" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>Validate the SSO assertion signature</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="requestBinding" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>HTTP method to use for requests</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="responseBinding" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>HTTP method to use for response</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="bindingUrl" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>SSO endpoint URL</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="assertionConsumerServiceUrl" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>Endpoint of Assertion Consumer Service at SP</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="single-logout-type">
        <xs:attribute name="validateRequestSignature" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>Validate a single-logout request signature</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="validateResponseSignature" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>Validate a single-logout response signature</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="signRequest" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>Sign single-logout requests</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="signResponse" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>Sign single-logout responses</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="requestBinding" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>HTTP method to use for request</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="responseBinding" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>HTTP method to use for response</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="postBindingUrl" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>Endpoint URL for posting</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="redirectBindingUrl" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>Endpoint URL for redirects</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="keys-type">
        <xs:sequence>
            <xs:element name="Key" minOccurs="1" maxOccurs="2" type="key-type"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="key-type">
        <xs:all>
            <xs:element name="KeyStore" minOccurs="0" maxOccurs="1" type="keystore-type"/>
            <xs:element name="PrivateKeyPem" minOccurs="0" maxOccurs="1" type="xs:string"/>
            <xs:element name="PublicKeyPem" minOccurs="0" maxOccurs="1" type="xs:string"/>
            <xs:element name="CertificatePem" minOccurs="0" maxOccurs="1" type="xs:string"/>
        </xs:all>
        <xs:attribute name="signing" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>Key can be used for signing</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="encryption" type="xs:boolean" use="optional">
            <xs:annotation>
                <xs:documentation>Key can be used for encryption</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>
    <xs:complexType name="keystore-type">
        <xs:sequence minOccurs="0" maxOccurs="1">
            <xs:element name="PrivateKey" minOccurs="0" maxOccurs="1" type="privatekey-type"/>
            <xs:element name="Certificate" minOccurs="0" maxOccurs="1" type="certificate-type"/>
        </xs:sequence>
        <xs:attribute name="file" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>Key store filesystem path</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="resource" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>Key store resource URI</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="password" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>Key store password</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="type" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>Key store format</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="alias" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>Key alias</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="privatekey-type">
        <xs:attribute name="alias" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>Private key alias</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="password" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>Private key password</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="certificate-type">
        <xs:attribute name="alias" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>Certificate alias</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="principal-name-mapping-type">
        <xs:attribute name="policy" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>Principal name mapping policy. Possible values: FROM_NAME_ID</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="attribute" type="xs:string" use="optional">
            <xs:annotation>
                <xs:documentation>Name of the attribute to use for principal name mapping</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="role-identifiers-type">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="Attribute" minOccurs="0" maxOccurs="unbounded" type="attribute-type"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="attribute-type">
        <xs:attribute name="name" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>Role attribute</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="role-mappings-provider-type">
        <xs:sequence>
            <xs:element name="Property" type="property-type" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Specifies a configuration property for the provider.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="id" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>The id of the role mappings provider that is to be used. Example: properties-based-provider.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="property-type">
        <xs:attribute name="name" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>The name (key) of the configuration property.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="value" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>The value of the configuration property.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="allowed-clock-skew-type">
        <xs:annotation>
            <xs:documentation>The value is the allowed clock skew between the IDP and the SP.</xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="xs:positiveInteger">
                <xs:attribute name="unit" type="clock-skew-unit-type"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="clock-skew-unit-type">
        <xs:annotation>
            <xs:documentation>Time unit for the value of the clock skew.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="MINUTES" />
            <xs:enumeration value="SECONDS" />
            <xs:enumeration value="MILLISECONDS" />
            <xs:enumeration value="MICROSECONDS" />
            <xs:enumeration value="NANOSECONDS" />
        </xs:restriction>
    </xs:simpleType>

</xs:schema>
